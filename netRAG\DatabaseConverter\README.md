# NetRAG Database Converter

A standalone utility to convert Microsoft Learn JSON data files into compressed binary database files for the NetRAG service.

## Purpose

This utility performs a one-time conversion of PowerShell command data from JSON format to a compressed binary format that can be efficiently loaded by the NetRAG service. This separation allows:

- **Faster service startup** - No need to process JSON and generate embeddings on every startup
- **Read-only service operation** - The main service only reads pre-computed data
- **Offline processing** - Data conversion can be done separately from service operation
- **Better resource management** - Embedding generation happens once, not on every service restart

## Prerequisites

- .NET 8.0 or later
- BERT ONNX model files:
  - `model.onnx` - BERT embedding model
  - `vocab.txt` - BERT vocabulary file

## Usage

```bash
DatabaseConverter.exe <input-json-file> <output-database-file>
```

### Example

```bash
DatabaseConverter.exe microsoft_learn_data.json database.bin
```

## Input Format

The input JSON file should contain an array of PowerShell command objects with the following structure:

```json
[
  {
    "CommandName": "Get-User",
    "Synopsis": "Gets user information from Active Directory",
    "Description": "The Get-User cmdlet retrieves user objects from Active Directory...",
    "Module": "ActiveDirectory",
    "Parameters": [
      {
        "Name": "Identity",
        "Description": "Specifies the user to retrieve",
        "Type": "String",
        "Required": true
      }
    ],
    "Examples": [
      {
        "Title": "Get a specific user",
        "Code": "Get-User -Identity 'john.doe'",
        "Description": "Retrieves information for the user john.doe"
      }
    ]
  }
]
```

## Output Format

The output is a compressed binary file containing:
- Vector embeddings for each PowerShell command
- Metadata including command names, descriptions, parameters, and examples
- Optimized for fast loading by the NetRAG service

## Process

1. **Load BERT Model** - Loads the ONNX embedding model and vocabulary
2. **Parse JSON** - Reads and parses the input JSON file
3. **Generate Embeddings** - Creates vector embeddings for each command's searchable text
4. **Create Metadata** - Extracts command information for search results
5. **Compress & Save** - Saves the data in compressed binary format

## Integration with NetRAG Service

Once the database file is created:

1. Place the `database.bin` file in the NetRAG service directory
2. Start the NetRAG service - it will automatically load the pre-computed data
3. The service will be ready to handle PowerShell command searches immediately

## Performance

- **Processing Speed**: ~100-200 commands per second (depending on hardware)
- **Compression Ratio**: Typically 60-80% size reduction compared to uncompressed JSON
- **Memory Usage**: Minimal - processes commands in batches

## Error Handling

The utility includes comprehensive error handling for:
- Missing input files
- Invalid JSON format
- Missing BERT model files
- Disk space issues
- Embedding generation failures

## Logging

The utility provides detailed console output showing:
- Progress indicators
- Processing statistics
- Error messages with context
- Final file size and location

## Building

```bash
cd DatabaseConverter
dotnet build
```

## Dependencies

- Microsoft.Extensions.AI
- Microsoft.SemanticKernel
- Microsoft.SemanticKernel.Connectors.Onnx
- NetRAG project reference
