#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Adds usage frequency data to PowerShell commands in RAG-optimized JSON format

.DESCRIPTION
    This script adds usage frequency information to PowerShell commands based on 
    common IT management scenarios. Commands are categorized by how frequently 
    they are used in typical Active Directory administration tasks.

.PARAMETER InputFile
    Path to the RAG-optimized JSON file to update

.PARAMETER OutputFile
    Path where the updated JSON file will be saved

.EXAMPLE
    .\add_usage_frequency.ps1 -InputFile "rag_optimized_ad_commands.json" -OutputFile "rag_optimized_ad_commands_with_frequency.json"
#>

param(
    [Parameter(Mandatory = $true)]
    [string]$InputFile,
    
    [Parameter(Mandatory = $true)]
    [string]$OutputFile
)

# Define usage frequency mappings based on real-world Active Directory administration patterns
# Frequencies are based on typical IT helpdesk, system administration, and security management tasks
$UsageFrequencyMap = @{
    # VERY COMMON Commands (used daily/multiple times per week) - Score 60-100
    "Get-ADUser" = @{ frequency = 100; category = "very_common" }           # Most used - user lookups, troubleshooting
    "Set-ADUser" = @{ frequency = 95; category = "very_common" }            # User attribute changes, updates
    "Get-ADGroup" = @{ frequency = 90; category = "very_common" }           # Group lookups, membership checks
    "Get-ADComputer" = @{ frequency = 85; category = "very_common" }        # Computer account management
    "Get-ADGroupMember" = @{ frequency = 80; category = "very_common" }     # Group membership verification
    "Add-ADGroupMember" = @{ frequency = 75; category = "very_common" }     # Adding users to groups
    "Remove-ADGroupMember" = @{ frequency = 70; category = "very_common" }  # Removing users from groups
    "New-ADUser" = @{ frequency = 65; category = "very_common" }            # New employee onboarding
    "Get-ADDefaultDomainPasswordPolicy" = @{ frequency = 60; category = "very_common" } # Password policy checks

    # COMMON Commands (used weekly/regularly) - Score 30-59
    "Disable-ADAccount" = @{ frequency = 55; category = "common" }          # Employee termination
    "Enable-ADAccount" = @{ frequency = 50; category = "common" }           # Account reactivation
    "Unlock-ADAccount" = @{ frequency = 45; category = "common" }           # Password lockout resolution
    "Set-ADAccountPassword" = @{ frequency = 45; category = "common" }      # Password resets
    "Search-ADAccount" = @{ frequency = 40; category = "common" }           # Account searches
    "Get-ADPrincipalGroupMembership" = @{ frequency = 35; category = "common" } # User group membership
    "Get-ADOrganizationalUnit" = @{ frequency = 35; category = "common" }   # OU structure navigation
    "Get-ADDomain" = @{ frequency = 30; category = "common" }               # Domain information
    "Get-ADDomainController" = @{ frequency = 30; category = "common" }     # DC health checks

    # MODERATE Commands (used monthly/occasionally) - Score 15-29
    "New-ADGroup" = @{ frequency = 25; category = "moderate" }              # New group creation
    "Set-ADGroup" = @{ frequency = 22; category = "moderate" }              # Group modifications
    "Move-ADObject" = @{ frequency = 20; category = "moderate" }            # OU reorganization
    "Get-ADObject" = @{ frequency = 20; category = "moderate" }             # Generic object queries
    "New-ADComputer" = @{ frequency = 18; category = "moderate" }           # Computer account creation
    "Set-ADComputer" = @{ frequency = 18; category = "moderate" }           # Computer account updates
    "Remove-ADUser" = @{ frequency = 15; category = "moderate" }            # User account deletion
    "Get-ADForest" = @{ frequency = 15; category = "moderate" }             # Forest information
    "New-ADOrganizationalUnit" = @{ frequency = 15; category = "moderate" } # OU creation

    # UNCOMMON Commands (used quarterly/specialized tasks) - Score 8-14
    "Remove-ADGroup" = @{ frequency = 12; category = "uncommon" }           # Group deletion
    "Set-ADOrganizationalUnit" = @{ frequency = 12; category = "uncommon" } # OU modifications
    "Get-ADUserResultantPasswordPolicy" = @{ frequency = 10; category = "uncommon" } # Password policy analysis
    "Remove-ADComputer" = @{ frequency = 10; category = "uncommon" }        # Computer account deletion
    "Get-ADTrust" = @{ frequency = 10; category = "uncommon" }              # Trust relationship checks
    "Set-ADObject" = @{ frequency = 10; category = "uncommon" }             # Generic object modifications
    "Get-ADReplicationFailure" = @{ frequency = 8; category = "uncommon" }  # Replication troubleshooting
    "Restore-ADObject" = @{ frequency = 8; category = "uncommon" }          # Object recovery

    # RARE Commands (used annually/highly specialized) - Score 1-7
    "Get-ADFineGrainedPasswordPolicy" = @{ frequency = 7; category = "rare" }           # FGPP management
    "Set-ADDefaultDomainPasswordPolicy" = @{ frequency = 6; category = "rare" }         # Domain policy changes
    "New-ADFineGrainedPasswordPolicy" = @{ frequency = 5; category = "rare" }           # FGPP creation
    "Sync-ADObject" = @{ frequency = 5; category = "rare" }                             # Manual replication
    "Get-ADReplicationConnection" = @{ frequency = 5; category = "rare" }               # Replication topology
    "Get-ADReplicationSite" = @{ frequency = 5; category = "rare" }                     # Site management
    "Get-ADFineGrainedPasswordPolicySubject" = @{ frequency = 4; category = "rare" }    # FGPP subjects
    "Set-ADFineGrainedPasswordPolicy" = @{ frequency = 4; category = "rare" }           # FGPP modifications
    "Get-ADReplicationAttributeMetadata" = @{ frequency = 4; category = "rare" }        # Attribute replication
    "Get-ADAuthenticationPolicy" = @{ frequency = 3; category = "rare" }                # Authentication policies
    "Get-ADReplicationPartnerMetadata" = @{ frequency = 3; category = "rare" }          # Partner metadata
    "Get-ADReplicationSiteLink" = @{ frequency = 3; category = "rare" }                 # Site link management
    "Add-ADFineGrainedPasswordPolicySubject" = @{ frequency = 3; category = "rare" }    # FGPP subject assignment
    "Remove-ADFineGrainedPasswordPolicySubject" = @{ frequency = 3; category = "rare" } # FGPP subject removal
    "Get-ADCentralAccessPolicy" = @{ frequency = 2; category = "rare" }                 # DAC policies
    "Get-ADCentralAccessRule" = @{ frequency = 2; category = "rare" }                   # DAC rules
    "Get-ADClaimType" = @{ frequency = 2; category = "rare" }                           # Claims management
    "Get-ADReplicationQueueOperation" = @{ frequency = 2; category = "rare" }           # Replication queue
    "Get-ADAuthenticationPolicySilo" = @{ frequency = 2; category = "rare" }            # Auth policy silos
    "Remove-ADFineGrainedPasswordPolicy" = @{ frequency = 2; category = "rare" }        # FGPP deletion
    "New-ADAuthenticationPolicy" = @{ frequency = 1; category = "rare" }                # Auth policy creation
    "Set-ADAuthenticationPolicy" = @{ frequency = 1; category = "rare" }                # Auth policy modification
    "New-ADAuthenticationPolicySilo" = @{ frequency = 1; category = "rare" }            # Auth silo creation
    "Get-ADClaimTransformPolicy" = @{ frequency = 1; category = "rare" }                # Claim transforms
    "Get-ADReplicationUpToDatenessVectorTable" = @{ frequency = 1; category = "rare" }  # Replication vectors
    "New-ADCentralAccessPolicy" = @{ frequency = 1; category = "rare" }                 # DAC policy creation
    "New-ADCentralAccessRule" = @{ frequency = 1; category = "rare" }                   # DAC rule creation
    "Get-ADReplicationSiteLinkBridge" = @{ frequency = 1; category = "rare" }           # Site link bridges

    # Additional commands with appropriate frequency rankings
    "Clear-ADAccountExpiration" = @{ frequency = 12; category = "uncommon" }            # Account expiration management
    "Set-ADAccountExpiration" = @{ frequency = 8; category = "uncommon" }               # Setting account expiration
    "Set-ADAccountControl" = @{ frequency = 6; category = "rare" }                      # Account control flags
    "Get-ADServiceAccount" = @{ frequency = 8; category = "uncommon" }                  # Service account management
    "New-ADServiceAccount" = @{ frequency = 4; category = "rare" }                      # Service account creation
    "Set-ADServiceAccount" = @{ frequency = 3; category = "rare" }                      # Service account modification
    "Remove-ADServiceAccount" = @{ frequency = 2; category = "rare" }                   # Service account deletion
    "Install-ADServiceAccount" = @{ frequency = 3; category = "rare" }                  # Service account installation
    "Uninstall-ADServiceAccount" = @{ frequency = 2; category = "rare" }                # Service account uninstallation
    "Test-ADServiceAccount" = @{ frequency = 4; category = "rare" }                     # Service account testing
    "Get-ADRootDSE" = @{ frequency = 6; category = "rare" }                             # Root DSE information
    "Get-ADOptionalFeature" = @{ frequency = 3; category = "rare" }                     # Optional features
    "Enable-ADOptionalFeature" = @{ frequency = 2; category = "rare" }                  # Enable optional features
    "Disable-ADOptionalFeature" = @{ frequency = 1; category = "rare" }                 # Disable optional features
    "Remove-ADOrganizationalUnit" = @{ frequency = 5; category = "rare" }               # OU deletion
    "Remove-ADObject" = @{ frequency = 8; category = "uncommon" }                       # Generic object deletion
    "Rename-ADObject" = @{ frequency = 6; category = "rare" }                           # Object renaming
    "Add-ADPrincipalGroupMembership" = @{ frequency = 15; category = "moderate" }       # Add group membership
    "Remove-ADPrincipalGroupMembership" = @{ frequency = 12; category = "uncommon" }    # Remove group membership
    "Get-ADAccountAuthorizationGroup" = @{ frequency = 5; category = "rare" }           # Authorization groups
    "Get-ADAccountResultantPasswordReplicationPolicy" = @{ frequency = 2; category = "rare" } # RODC policies
    "Add-ADDomainControllerPasswordReplicationPolicy" = @{ frequency = 1; category = "rare" } # RODC policy addition
    "Remove-ADDomainControllerPasswordReplicationPolicy" = @{ frequency = 1; category = "rare" } # RODC policy removal
    "Get-ADDomainControllerPasswordReplicationPolicy" = @{ frequency = 2; category = "rare" } # RODC policy queries
    "Get-ADDomainControllerPasswordReplicationPolicyUsage" = @{ frequency = 1; category = "rare" } # RODC usage
    "Set-ADDomain" = @{ frequency = 2; category = "rare" }                              # Domain modifications
    "Set-ADDomainMode" = @{ frequency = 1; category = "rare" }                          # Domain functional level
    "Set-ADForest" = @{ frequency = 1; category = "rare" }                              # Forest modifications
    "Set-ADForestMode" = @{ frequency = 1; category = "rare" }                          # Forest functional level
    "Move-ADDirectoryServer" = @{ frequency = 1; category = "rare" }                    # DC movement
    "Move-ADDirectoryServerOperationMasterRole" = @{ frequency = 2; category = "rare" } # FSMO role transfer
    "New-ADReplicationSite" = @{ frequency = 2; category = "rare" }                     # Site creation
    "Remove-ADReplicationSite" = @{ frequency = 1; category = "rare" }                  # Site deletion
    "Set-ADReplicationSite" = @{ frequency = 2; category = "rare" }                     # Site modification
    "New-ADReplicationSiteLink" = @{ frequency = 2; category = "rare" }                 # Site link creation
    "Remove-ADReplicationSiteLink" = @{ frequency = 1; category = "rare" }              # Site link deletion
    "Set-ADReplicationSiteLink" = @{ frequency = 2; category = "rare" }                 # Site link modification
    "New-ADReplicationSiteLinkBridge" = @{ frequency = 1; category = "rare" }           # Site link bridge creation
    "Remove-ADReplicationSiteLinkBridge" = @{ frequency = 1; category = "rare" }        # Site link bridge deletion
    "Set-ADReplicationSiteLinkBridge" = @{ frequency = 1; category = "rare" }           # Site link bridge modification
    "Set-ADReplicationConnection" = @{ frequency = 2; category = "rare" }               # Replication connection modification

    # Central Access Policy and DAC commands (rarely used in most environments)
    "Add-ADCentralAccessPolicyMember" = @{ frequency = 1; category = "rare" }           # DAC policy member addition
    "Remove-ADCentralAccessPolicyMember" = @{ frequency = 1; category = "rare" }        # DAC policy member removal
    "Set-ADCentralAccessPolicy" = @{ frequency = 1; category = "rare" }                 # DAC policy modification
    "Remove-ADCentralAccessPolicy" = @{ frequency = 1; category = "rare" }              # DAC policy deletion
    "Set-ADCentralAccessRule" = @{ frequency = 1; category = "rare" }                   # DAC rule modification
    "Remove-ADCentralAccessRule" = @{ frequency = 1; category = "rare" }                # DAC rule deletion

    # Claims and Transform Policy commands (advanced features, rarely used)
    "New-ADClaimTransformPolicy" = @{ frequency = 1; category = "rare" }                # Claim transform creation
    "Set-ADClaimTransformPolicy" = @{ frequency = 1; category = "rare" }                # Claim transform modification
    "Remove-ADClaimTransformPolicy" = @{ frequency = 1; category = "rare" }             # Claim transform deletion
    "Set-ADClaimTransformLink" = @{ frequency = 1; category = "rare" }                  # Claim transform linking
    "Clear-ADClaimTransformLink" = @{ frequency = 1; category = "rare" }                # Claim transform unlinking
    "New-ADClaimType" = @{ frequency = 1; category = "rare" }                           # Claim type creation
    "Set-ADClaimType" = @{ frequency = 1; category = "rare" }                           # Claim type modification
    "Remove-ADClaimType" = @{ frequency = 1; category = "rare" }                        # Claim type deletion

    # Authentication Policy and Silo commands (Windows Server 2012 R2+ features)
    "Set-ADAuthenticationPolicySilo" = @{ frequency = 1; category = "rare" }            # Auth silo modification
    "Remove-ADAuthenticationPolicy" = @{ frequency = 1; category = "rare" }             # Auth policy deletion
    "Remove-ADAuthenticationPolicySilo" = @{ frequency = 1; category = "rare" }         # Auth silo deletion
    "Grant-ADAuthenticationPolicySiloAccess" = @{ frequency = 1; category = "rare" }    # Auth silo access grant
    "Revoke-ADAuthenticationPolicySiloAccess" = @{ frequency = 1; category = "rare" }   # Auth silo access revoke
    "Set-ADAccountAuthenticationPolicySilo" = @{ frequency = 2; category = "rare" }     # Account auth silo assignment
    "Show-ADAuthenticationPolicyExpression" = @{ frequency = 1; category = "rare" }     # Auth policy expression display

    # Computer Service Account commands (rarely used)
    "Add-ADComputerServiceAccount" = @{ frequency = 2; category = "rare" }              # Computer service account addition
    "Remove-ADComputerServiceAccount" = @{ frequency = 2; category = "rare" }           # Computer service account removal
    "Get-ADComputerServiceAccount" = @{ frequency = 3; category = "rare" }              # Computer service account queries

    # Service Account Migration commands (specialized migration scenarios)
    "Start-ADServiceAccountMigration" = @{ frequency = 1; category = "rare" }           # Service account migration start
    "Complete-ADServiceAccountMigration" = @{ frequency = 1; category = "rare" }        # Service account migration complete
    "Reset-ADServiceAccountMigration" = @{ frequency = 1; category = "rare" }           # Service account migration reset
    "Undo-ADServiceAccountMigration" = @{ frequency = 1; category = "rare" }            # Service account migration undo
    "Reset-ADServiceAccountPassword" = @{ frequency = 2; category = "rare" }            # Service account password reset

    # DC Cloning commands (virtualization scenarios)
    "Get-ADDCCloningExcludedApplicationList" = @{ frequency = 1; category = "rare" }    # DC cloning exclusion list
    "New-ADDCCloneConfigFile" = @{ frequency = 1; category = "rare" }                   # DC clone config creation

    # Resource Property commands (File Classification Infrastructure)
    "Add-ADResourcePropertyListMember" = @{ frequency = 1; category = "rare" }          # Resource property list member addition
    "Remove-ADResourcePropertyListMember" = @{ frequency = 1; category = "rare" }       # Resource property list member removal
    "Get-ADResourcePropertyValueType" = @{ frequency = 1; category = "rare" }           # Resource property value types
}

function Add-UsageFrequencyToCommands {
    param(
        [Parameter(Mandatory = $true)]
        [string]$InputPath,
        
        [Parameter(Mandatory = $true)]
        [string]$OutputPath
    )
    
    Write-Host "Loading RAG-optimized data from: $InputPath" -ForegroundColor Green
    
    if (-not (Test-Path $InputPath)) {
        throw "Input file not found: $InputPath"
    }
    
    # Load the JSON data
    $jsonContent = Get-Content -Path $InputPath -Raw -Encoding UTF8
    $data = $jsonContent | ConvertFrom-Json
    
    if (-not $data.commands) {
        throw "Invalid JSON format: 'commands' array not found"
    }
    
    Write-Host "Processing $($data.commands.Count) commands..." -ForegroundColor Yellow
    
    $updatedCount = 0
    $defaultCount = 0
    
    # Process each command
    foreach ($command in $data.commands) {
        $commandName = $command.command_name
        
        if ($UsageFrequencyMap.ContainsKey($commandName)) {
            # Use predefined frequency data
            $freqData = $UsageFrequencyMap[$commandName]
            $command | Add-Member -NotePropertyName "usage_frequency" -NotePropertyValue $freqData.frequency -Force
            $command | Add-Member -NotePropertyName "usage_category" -NotePropertyValue $freqData.category -Force
            $updatedCount++
            
            Write-Host "  ✓ $commandName -> Frequency: $($freqData.frequency), Category: $($freqData.category)" -ForegroundColor Cyan
        } else {
            # Apply default moderate frequency for unknown commands
            $defaultFreq = 10
            $defaultCategory = "moderate"
            
            # Try to infer category from command verb and noun patterns
            if ($command.verb) {
                switch ($command.verb.ToLower()) {
                    "get" {
                        # Get commands are generally more common
                        if ($command.noun -match "User|Group|Computer") {
                            $defaultFreq = 25; $defaultCategory = "common"
                        } else {
                            $defaultFreq = 15; $defaultCategory = "uncommon"
                        }
                    }
                    "set" { $defaultFreq = 8; $defaultCategory = "uncommon" }
                    "new" { $defaultFreq = 5; $defaultCategory = "uncommon" }
                    "add" { $defaultFreq = 8; $defaultCategory = "uncommon" }
                    "remove" { $defaultFreq = 3; $defaultCategory = "rare" }
                    "enable" { $defaultFreq = 6; $defaultCategory = "uncommon" }
                    "disable" { $defaultFreq = 6; $defaultCategory = "uncommon" }
                    default { $defaultFreq = 10; $defaultCategory = "moderate" }
                }
            }
            
            $command | Add-Member -NotePropertyName "usage_frequency" -NotePropertyValue $defaultFreq -Force
            $command | Add-Member -NotePropertyName "usage_category" -NotePropertyValue $defaultCategory -Force
            $defaultCount++
            
            Write-Host "  ? $commandName -> Default Frequency: $defaultFreq, Category: $defaultCategory" -ForegroundColor Gray
        }
    }
    
    # Update metadata
    if ($data.metadata) {
        $data.metadata | Add-Member -NotePropertyName "usage_frequency_added" -NotePropertyValue (Get-Date -Format "yyyy-MM-dd HH:mm:ss") -Force
        $data.metadata | Add-Member -NotePropertyName "usage_frequency_version" -NotePropertyValue "1.0" -Force
        
        # Update features list
        if ($data.metadata.features) {
            $features = [System.Collections.ArrayList]$data.metadata.features
            if ($features -notcontains "usage_frequency_ranking") {
                $features.Add("usage_frequency_ranking") | Out-Null
                $data.metadata.features = $features.ToArray()
            }
        }
    }
    
    # Save the updated data
    Write-Host "Saving updated data to: $OutputPath" -ForegroundColor Green
    $updatedJson = $data | ConvertTo-Json -Depth 20
    $updatedJson | Out-File -FilePath $OutputPath -Encoding UTF8
    
    Write-Host "✅ Usage frequency data added successfully!" -ForegroundColor Green
    Write-Host "   - Commands with predefined frequency: $updatedCount" -ForegroundColor White
    Write-Host "   - Commands with default frequency: $defaultCount" -ForegroundColor White
    Write-Host "   - Total commands processed: $($data.commands.Count)" -ForegroundColor White
}

# Main execution
try {
    Add-UsageFrequencyToCommands -InputPath $InputFile -OutputPath $OutputFile
} catch {
    Write-Error "Failed to add usage frequency data: $_"
    exit 1
}
