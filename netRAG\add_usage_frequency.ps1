#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Adds usage frequency data to PowerShell commands in RAG-optimized JSON format

.DESCRIPTION
    This script adds usage frequency information to PowerShell commands based on 
    common IT management scenarios. Commands are categorized by how frequently 
    they are used in typical Active Directory administration tasks.

.PARAMETER InputFile
    Path to the RAG-optimized JSON file to update

.PARAMETER OutputFile
    Path where the updated JSON file will be saved

.EXAMPLE
    .\add_usage_frequency.ps1 -InputFile "rag_optimized_ad_commands.json" -OutputFile "rag_optimized_ad_commands_with_frequency.json"
#>

param(
    [Parameter(Mandatory = $true)]
    [string]$InputFile,
    
    [Parameter(Mandatory = $true)]
    [string]$OutputFile
)

# Define usage frequency mappings based on common IT management tasks
$UsageFrequencyMap = @{
    # Very Common Commands (used daily/weekly)
    "Get-ADUser" = @{ frequency = 100; category = "very_common" }
    "Get-ADGroup" = @{ frequency = 90; category = "very_common" }
    "Get-ADComputer" = @{ frequency = 85; category = "very_common" }
    "Get-ADGroupMember" = @{ frequency = 80; category = "very_common" }
    "Add-ADGroupMember" = @{ frequency = 75; category = "very_common" }
    "Remove-ADGroupMember" = @{ frequency = 70; category = "very_common" }
    "Set-ADUser" = @{ frequency = 95; category = "very_common" }
    "New-ADUser" = @{ frequency = 65; category = "very_common" }
    "Get-ADDefaultDomainPasswordPolicy" = @{ frequency = 60; category = "very_common" }
    "Get-ADDomain" = @{ frequency = 55; category = "very_common" }
    
    # Common Commands (used regularly)
    "Disable-ADAccount" = @{ frequency = 50; category = "common" }
    "Enable-ADAccount" = @{ frequency = 45; category = "common" }
    "Unlock-ADAccount" = @{ frequency = 40; category = "common" }
    "Reset-ADAccountPassword" = @{ frequency = 35; category = "common" }
    "Get-ADOrganizationalUnit" = @{ frequency = 30; category = "common" }
    "Move-ADObject" = @{ frequency = 25; category = "common" }
    "Set-ADAccountPassword" = @{ frequency = 45; category = "common" }
    "Get-ADPrincipalGroupMembership" = @{ frequency = 35; category = "common" }
    "Search-ADAccount" = @{ frequency = 40; category = "common" }
    "Get-ADUserResultantPasswordPolicy" = @{ frequency = 20; category = "common" }
    
    # Moderate Usage Commands (used occasionally)
    "New-ADGroup" = @{ frequency = 20; category = "moderate" }
    "Remove-ADUser" = @{ frequency = 15; category = "moderate" }
    "Remove-ADGroup" = @{ frequency = 12; category = "moderate" }
    "Set-ADGroup" = @{ frequency = 18; category = "moderate" }
    "New-ADOrganizationalUnit" = @{ frequency = 10; category = "moderate" }
    "Set-ADOrganizationalUnit" = @{ frequency = 8; category = "moderate" }
    "Get-ADReplicationFailure" = @{ frequency = 15; category = "moderate" }
    "Get-ADDomainController" = @{ frequency = 25; category = "moderate" }
    "Get-ADForest" = @{ frequency = 12; category = "moderate" }
    "Get-ADTrust" = @{ frequency = 8; category = "moderate" }
    
    # Rare Commands (used infrequently, specialized tasks)
    "New-ADFineGrainedPasswordPolicy" = @{ frequency = 5; category = "rare" }
    "Set-ADFineGrainedPasswordPolicy" = @{ frequency = 3; category = "rare" }
    "Get-ADFineGrainedPasswordPolicy" = @{ frequency = 8; category = "rare" }
    "Get-ADFineGrainedPasswordPolicySubject" = @{ frequency = 4; category = "rare" }
    "Add-ADFineGrainedPasswordPolicySubject" = @{ frequency = 2; category = "rare" }
    "Remove-ADFineGrainedPasswordPolicySubject" = @{ frequency = 2; category = "rare" }
    "Set-ADDefaultDomainPasswordPolicy" = @{ frequency = 6; category = "rare" }
    "Get-ADAuthenticationPolicy" = @{ frequency = 3; category = "rare" }
    "New-ADAuthenticationPolicy" = @{ frequency = 1; category = "rare" }
    "Set-ADAuthenticationPolicy" = @{ frequency = 1; category = "rare" }
    "Get-ADAuthenticationPolicySilo" = @{ frequency = 2; category = "rare" }
    "New-ADAuthenticationPolicySilo" = @{ frequency = 1; category = "rare" }
    "Get-ADCentralAccessPolicy" = @{ frequency = 2; category = "rare" }
    "Get-ADCentralAccessRule" = @{ frequency = 2; category = "rare" }
    "Get-ADClaimTransformPolicy" = @{ frequency = 1; category = "rare" }
    "Get-ADClaimType" = @{ frequency = 2; category = "rare" }
    "Sync-ADObject" = @{ frequency = 5; category = "rare" }
    "Get-ADReplicationAttributeMetadata" = @{ frequency = 3; category = "rare" }
    "Get-ADReplicationConnection" = @{ frequency = 4; category = "rare" }
    "Get-ADReplicationPartnerMetadata" = @{ frequency = 3; category = "rare" }
    "Get-ADReplicationQueueOperation" = @{ frequency = 2; category = "rare" }
    "Get-ADReplicationSite" = @{ frequency = 5; category = "rare" }
    "Get-ADReplicationSiteLink" = @{ frequency = 3; category = "rare" }
    "Get-ADReplicationSubnet" = @{ frequency = 3; category = "rare" }
    "Get-ADReplicationUpToDatenessVectorTable" = @{ frequency = 1; category = "rare" }
}

function Add-UsageFrequencyToCommands {
    param(
        [Parameter(Mandatory = $true)]
        [string]$InputPath,
        
        [Parameter(Mandatory = $true)]
        [string]$OutputPath
    )
    
    Write-Host "Loading RAG-optimized data from: $InputPath" -ForegroundColor Green
    
    if (-not (Test-Path $InputPath)) {
        throw "Input file not found: $InputPath"
    }
    
    # Load the JSON data
    $jsonContent = Get-Content -Path $InputPath -Raw -Encoding UTF8
    $data = $jsonContent | ConvertFrom-Json
    
    if (-not $data.commands) {
        throw "Invalid JSON format: 'commands' array not found"
    }
    
    Write-Host "Processing $($data.commands.Count) commands..." -ForegroundColor Yellow
    
    $updatedCount = 0
    $defaultCount = 0
    
    # Process each command
    foreach ($command in $data.commands) {
        $commandName = $command.command_name
        
        if ($UsageFrequencyMap.ContainsKey($commandName)) {
            # Use predefined frequency data
            $freqData = $UsageFrequencyMap[$commandName]
            $command | Add-Member -NotePropertyName "usage_frequency" -NotePropertyValue $freqData.frequency -Force
            $command | Add-Member -NotePropertyName "usage_category" -NotePropertyValue $freqData.category -Force
            $updatedCount++
            
            Write-Host "  ✓ $commandName -> Frequency: $($freqData.frequency), Category: $($freqData.category)" -ForegroundColor Cyan
        } else {
            # Apply default moderate frequency for unknown commands
            $defaultFreq = 10
            $defaultCategory = "moderate"
            
            # Try to infer category from command verb
            if ($command.verb) {
                switch ($command.verb.ToLower()) {
                    "get" { $defaultFreq = 15; $defaultCategory = "common" }
                    "set" { $defaultFreq = 8; $defaultCategory = "moderate" }
                    "new" { $defaultFreq = 5; $defaultCategory = "moderate" }
                    "remove" { $defaultFreq = 3; $defaultCategory = "rare" }
                    default { $defaultFreq = 10; $defaultCategory = "moderate" }
                }
            }
            
            $command | Add-Member -NotePropertyName "usage_frequency" -NotePropertyValue $defaultFreq -Force
            $command | Add-Member -NotePropertyName "usage_category" -NotePropertyValue $defaultCategory -Force
            $defaultCount++
            
            Write-Host "  ? $commandName -> Default Frequency: $defaultFreq, Category: $defaultCategory" -ForegroundColor Gray
        }
    }
    
    # Update metadata
    if ($data.metadata) {
        $data.metadata | Add-Member -NotePropertyName "usage_frequency_added" -NotePropertyValue (Get-Date -Format "yyyy-MM-dd HH:mm:ss") -Force
        $data.metadata | Add-Member -NotePropertyName "usage_frequency_version" -NotePropertyValue "1.0" -Force
        
        # Update features list
        if ($data.metadata.features) {
            $features = [System.Collections.ArrayList]$data.metadata.features
            if ($features -notcontains "usage_frequency_ranking") {
                $features.Add("usage_frequency_ranking") | Out-Null
                $data.metadata.features = $features.ToArray()
            }
        }
    }
    
    # Save the updated data
    Write-Host "Saving updated data to: $OutputPath" -ForegroundColor Green
    $updatedJson = $data | ConvertTo-Json -Depth 20
    $updatedJson | Out-File -FilePath $OutputPath -Encoding UTF8
    
    Write-Host "✅ Usage frequency data added successfully!" -ForegroundColor Green
    Write-Host "   - Commands with predefined frequency: $updatedCount" -ForegroundColor White
    Write-Host "   - Commands with default frequency: $defaultCount" -ForegroundColor White
    Write-Host "   - Total commands processed: $($data.commands.Count)" -ForegroundColor White
}

# Main execution
try {
    Add-UsageFrequencyToCommands -InputPath $InputFile -OutputPath $OutputFile
} catch {
    Write-Error "Failed to add usage frequency data: $_"
    exit 1
}
