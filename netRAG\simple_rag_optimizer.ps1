# Simple PowerShell Active Directory JSON Optimizer for RAG
# Creates a clean, searchable JSON structure optimized for RAG and LLM integration

param(
    [string]$InputFile = "comprehensive_ad_data.json",
    [string]$OutputFile = "ad_powershell_rag_optimized.json",
    [switch]$Verbose
)

Write-Host "Simple PowerShell Active Directory JSON RAG Optimizer" -ForegroundColor Green
Write-Host "=====================================================" -ForegroundColor Green

# Load the comprehensive data
if (-not (Test-Path $InputFile)) {
    Write-Error "Input file not found: $InputFile"
    exit 1
}

Write-Host "Loading comprehensive AD data from: $InputFile" -ForegroundColor Yellow
try {
    $rawData = Get-Content $InputFile -Raw | ConvertFrom-Json
    Write-Host "Successfully loaded data with $($rawData.scraped_commands.Count) commands" -ForegroundColor Green
}
catch {
    Write-Error "Failed to parse JSON file: $($_.Exception.Message)"
    exit 1
}

# Initialize RAG-optimized JSON structure
$ragOptimizedData = @{
    metadata = @{
        created_at = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        source_file = $InputFile
        total_commands = 0
        optimization_version = "1.0-Simple"
        description = "RAG-optimized PowerShell Active Directory command data for LLM integration"
        features = @(
            "Flattened searchable content",
            "Enhanced parameter information",
            "Contextual examples",
            "Search-friendly structure",
            "LLM-ready formatting"
        )
    }
    commands = @()
}

Write-Host "Processing and optimizing commands..." -ForegroundColor Yellow

$processedCount = 0
foreach ($command in $rawData.scraped_commands) {
    try {
        if ($command.status -eq "failed") {
            continue
        }
        
        # Safely get command name
        $commandName = ""
        if ($command.page_info -and $command.page_info.command_name) {
            $commandName = $command.page_info.command_name
        } elseif ($command.command) {
            $commandName = $command.command
        } else {
            continue
        }
        
        $processedCount++
        
        if ($Verbose) {
            Write-Host "  Processing: $commandName" -ForegroundColor Cyan
        }
        
        # Extract verb and noun
        $parts = $commandName -split '-'
        $verb = if ($parts.Count -gt 0) { $parts[0] } else { "" }
        $noun = if ($parts.Count -gt 1) { $parts[1] } else { "" }
        
        # Determine category based on noun
        $category = switch -Regex ($noun) {
            'User' { 'User Management' }
            'Group' { 'Group Management' }
            'Computer' { 'Computer Management' }
            'Domain|Forest' { 'Domain Management' }
            'Replication|Site' { 'Replication Management' }
            'ServiceAccount' { 'Service Account Management' }
            'OrganizationalUnit' { 'Organizational Unit Management' }
            'Policy|Authentication' { 'Policy Management' }
            'Account|Object' { 'Security Management' }
            default { 'General Operations' }
        }
        
        # Create optimized command entry
        $optimizedCommand = @{
            # Core identification
            command_name = $commandName
            verb = $verb
            noun = $noun
            module = "ActiveDirectory"
            category = $category
            
            # Content for search and LLM
            synopsis = if ($command.synopsis) { $command.synopsis } else { "" }
            description = if ($command.description) { $command.description } else { "" }
            
            # Combined searchable text
            searchable_content = "$commandName $verb $noun $($command.synopsis) $($command.description)" -replace '\s+', ' '
            
            # Use case information
            primary_purpose = switch ($verb) {
                'Get' { "Retrieve $noun information" }
                'New' { "Create new $noun" }
                'Set' { "Modify $noun properties" }
                'Remove' { "Delete $noun" }
                'Add' { "Add to $noun" }
                'Enable' { "Enable $noun" }
                'Disable' { "Disable $noun" }
                default { "Manage $noun" }
            }
            
            # Parameters - simplified structure
            parameters = @()
            required_parameters = @()
            optional_parameters = @()
            
            # Examples - simplified structure
            examples = @()
            
            # Additional context
            related_commands = @()
            source_url = if ($command.url) { $command.url } else { "" }
            scraped_at = if ($command.scraped_at) { $command.scraped_at } else { "" }
        }
        
        # Process parameters safely
        if ($command.parameters) {
            $paramNames = @()
            if ($command.parameters.GetType().Name -eq "PSCustomObject") {
                $paramNames = $command.parameters.PSObject.Properties.Name
            } elseif ($command.parameters.Keys) {
                $paramNames = $command.parameters.Keys
            }
            
            foreach ($paramName in $paramNames) {
                try {
                    $param = $command.parameters.$paramName
                    
                    $optimizedParam = @{
                        name = $paramName
                        description = if ($param.description) { $param.description } else { "" }
                        type = if ($param.properties -and $param.properties.Type) { $param.properties.Type } else { "String" }
                        mandatory = if ($param.properties -and $param.properties.Mandatory) { ($param.properties.Mandatory -eq "True") } else { $false }
                        position = if ($param.properties -and $param.properties.Position) { $param.properties.Position } else { "Named" }
                        pipeline_input = if ($param.properties -and $param.properties."Value from pipeline") { ($param.properties."Value from pipeline" -eq "True") } else { $false }
                        
                        # Searchable parameter content
                        searchable_text = "$paramName $($param.description)" -replace '\s+', ' '
                    }
                    
                    $optimizedCommand.parameters += $optimizedParam
                    
                    if ($optimizedParam.mandatory) {
                        $optimizedCommand.required_parameters += $paramName
                    } else {
                        $optimizedCommand.optional_parameters += $paramName
                    }
                }
                catch {
                    if ($Verbose) {
                        Write-Warning "Error processing parameter $paramName for $commandName"
                    }
                }
            }
        }
        
        # Process examples safely
        if ($command.examples -and $command.examples.Count -gt 0) {
            foreach ($example in $command.examples) {
                try {
                    $optimizedExample = @{
                        title = if ($example.title) { $example.title } else { "Example" }
                        description = if ($example.description) { $example.description } else { "" }
                        code = if ($example.code_blocks) { ($example.code_blocks -join "`n") } else { "" }
                        
                        # Extract scenario type
                        scenario = if ($example.title -match 'all|list') { 'List All' }
                                  elseif ($example.title -match 'specific|particular') { 'Get Specific' }
                                  elseif ($example.title -match 'filter|search') { 'Search Filter' }
                                  elseif ($example.title -match 'properties') { 'Property Management' }
                                  else { 'General Usage' }
                        
                        # Searchable example content
                        searchable_text = "$($example.title) $($example.description) $($example.code_blocks -join ' ')" -replace '\s+', ' '
                    }
                    
                    $optimizedCommand.examples += $optimizedExample
                }
                catch {
                    if ($Verbose) {
                        Write-Warning "Error processing example for $commandName"
                    }
                }
            }
        }
        
        # Add related commands (same noun, different verbs)
        @('Get', 'New', 'Set', 'Remove', 'Add', 'Enable', 'Disable') | ForEach-Object {
            if ($_ -ne $verb) {
                $optimizedCommand.related_commands += "$_-$noun"
            }
        }
        
        $ragOptimizedData.commands += $optimizedCommand
    }
    catch {
        if ($Verbose) {
            Write-Warning "Error processing command: $($_.Exception.Message)"
        }
    }
}

# Finalize metadata
$ragOptimizedData.metadata.total_commands = $processedCount

Write-Host "Saving RAG-optimized JSON..." -ForegroundColor Yellow

# Save the optimized JSON
try {
    $json = $ragOptimizedData | ConvertTo-Json -Depth 15
    $json | Out-File -FilePath $OutputFile -Encoding UTF8
    
    # Display summary
    Write-Host "`n" + "="*60 -ForegroundColor Green
    Write-Host "RAG OPTIMIZATION COMPLETE!" -ForegroundColor Green
    Write-Host "="*60 -ForegroundColor Green
    Write-Host "Input file: $InputFile" -ForegroundColor White
    Write-Host "Output file: $OutputFile" -ForegroundColor White
    Write-Host "Commands processed: $processedCount" -ForegroundColor Cyan
    Write-Host "File size: $([math]::Round((Get-Item $OutputFile).Length / 1MB, 2)) MB" -ForegroundColor Yellow
    
    # Show sample statistics
    $totalParams = ($ragOptimizedData.commands | ForEach-Object { $_.parameters.Count } | Measure-Object -Sum).Sum
    $totalExamples = ($ragOptimizedData.commands | ForEach-Object { $_.examples.Count } | Measure-Object -Sum).Sum
    
    Write-Host "`nContent Statistics:" -ForegroundColor Yellow
    Write-Host "  Total Parameters: $totalParams" -ForegroundColor Cyan
    Write-Host "  Total Examples: $totalExamples" -ForegroundColor Cyan
    Write-Host "  Categories: $($ragOptimizedData.commands | Group-Object category | Measure-Object).Count" -ForegroundColor Cyan
    
    Write-Host "`nRAG Optimization Features:" -ForegroundColor Yellow
    Write-Host "✓ Clean, searchable JSON structure" -ForegroundColor Green
    Write-Host "✓ Flattened parameter information" -ForegroundColor Green
    Write-Host "✓ Enhanced examples with scenarios" -ForegroundColor Green
    Write-Host "✓ Searchable content fields" -ForegroundColor Green
    Write-Host "✓ Command categorization" -ForegroundColor Green
    Write-Host "✓ Related command mapping" -ForegroundColor Green
    
    Write-Host "`nThe RAG-optimized JSON is ready for database conversion!" -ForegroundColor Green
}
catch {
    Write-Error "Failed to save optimized JSON: $($_.Exception.Message)"
}
