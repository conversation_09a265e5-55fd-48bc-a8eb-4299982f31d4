#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Enhances high-frequency PowerShell commands with real-world community usage examples

.DESCRIPTION
    This script adds practical, real-world examples to PowerShell commands with usage frequency 
    above 50, based on common IT administration scenarios found in community forums, 
    documentation, and best practices.

.PARAMETER InputFile
    Path to the RAG-optimized JSON file with frequency data

.PARAMETER OutputFile
    Path where the enhanced JSON file will be saved

.EXAMPLE
    .\enhance_high_frequency_examples.ps1 -InputFile "ad_powershell_final_rag_with_frequency.json" -OutputFile "ad_powershell_final_rag_enhanced.json"
#>

param(
    [Parameter(Mandatory = $true)]
    [string]$InputFile,
    
    [Parameter(Mandatory = $true)]
    [string]$OutputFile
)

# Define enhanced examples for high-frequency commands (frequency > 50)
function Get-EnhancedExamples {
    $examples = @{}

    $examples['Get-ADUser'] = @{
        real_world_examples = @(
            @{
                scenario = "Find locked out users for helpdesk"
                command = "Get-ADUser -Filter {LockedOut -eq `$true} -Properties LockedOut, LastLogonDate | Select Name, SamAccountName, LastLogonDate"
                description = "Quickly identify all locked user accounts to assist with helpdesk tickets"
            },
            @{
                scenario = "Audit users with expired passwords"
                command = "Get-ADUser -Filter {PasswordExpired -eq `$true} -Properties PasswordExpired, PasswordLastSet | Select Name, SamAccountName, PasswordLastSet"
                description = "Find users whose passwords have expired for security compliance"
            },
            @{
                scenario = "Find inactive users for cleanup"
                command = "Get-ADUser -Filter * -Properties LastLogonDate | Where-Object {`$_.LastLogonDate -lt (Get-Date).AddDays(-90)} | Select Name, SamAccountName, LastLogonDate"
                description = "Identify users who haven't logged in for 90+ days for account cleanup"
            },
            @{
                scenario = "Bulk export user information"
                command = "Get-ADUser -Filter * -Properties DisplayName, EmailAddress, Department, Manager, LastLogonDate | Export-CSV 'C:\\UserReport.csv' -NoTypeInformation"
                description = "Export all user data for reporting and analysis purposes"
            }
        )
    },

    $examples['Set-ADUser'] = @{
        real_world_examples = @(
            @{
                scenario = "Employee department transfer"
                command = "Set-ADUser -Identity 'jdoe' -Department 'IT' -Title 'System Administrator' -Manager 'CN=Jane Smith,OU=Users,DC=company,DC=com'"
                description = "Update user attributes when employee changes departments or roles"
            },
            @{
                scenario = "Bulk update employee information"
                command = "Import-CSV 'C:\\UserUpdates.csv' | ForEach-Object { Set-ADUser -Identity $_.SamAccountName -Department $_.Department -Title $_.Title }"
                description = "Mass update user attributes from CSV file for organizational changes"
            },
            @{
                scenario = "Set account expiration for contractors"
                command = "Set-ADUser -Identity 'contractor1' -AccountExpirationDate (Get-Date).AddDays(90)"
                description = "Set automatic account expiration for temporary employees and contractors"
            },
            @{
                scenario = "Update user contact information"
                command = "Set-ADUser -Identity 'jdoe' -EmailAddress '<EMAIL>' -OfficePhone '******-0123' -Office 'Building A, Room 101'"
                description = "Update user contact details after office moves or email migrations"
            },
            @{
                scenario = "Enable/disable user account features"
                command = "Set-ADUser -Identity 'jdoe' -CannotChangePassword $true -PasswordNeverExpires $false"
                description = "Configure specific account policies for service accounts or special users"
            }
        )
    },

    'Get-ADGroup' = @{
        real_world_examples = @(
            @{
                scenario = "Find security groups for access review"
                command = "Get-ADGroup -Filter {GroupCategory -eq 'Security'} -Properties Members | Where-Object {$_.Members.Count -gt 0} | Select Name, @{Name='MemberCount';Expression={$_.Members.Count}}"
                description = "List all security groups with members for access governance reviews"
            },
            @{
                scenario = "Search groups by naming convention"
                command = "Get-ADGroup -Filter {Name -like 'APP_*'} | Select Name, Description, GroupScope"
                description = "Find application-specific groups following naming conventions"
            },
            @{
                scenario = "Audit distribution groups"
                command = "Get-ADGroup -Filter {GroupCategory -eq 'Distribution'} -Properties Mail | Where-Object {$_.Mail -ne $null} | Select Name, Mail, Description"
                description = "List email-enabled distribution groups for Exchange management"
            },
            @{
                scenario = "Find empty groups for cleanup"
                command = "Get-ADGroup -Filter * -Properties Members | Where-Object {$_.Members.Count -eq 0} | Select Name, Description"
                description = "Identify unused groups that can be safely removed"
            }
        )
    },

    'Get-ADComputer' = @{
        real_world_examples = @(
            @{
                scenario = "Find computers not seen recently"
                command = "Get-ADComputer -Filter * -Properties LastLogonDate | Where-Object {$_.LastLogonDate -lt (Get-Date).AddDays(-30)} | Select Name, LastLogonDate, OperatingSystem"
                description = "Identify stale computer accounts for cleanup and security"
            },
            @{
                scenario = "Audit server operating systems"
                command = "Get-ADComputer -Filter {OperatingSystem -like '*Server*'} -Properties OperatingSystem, OperatingSystemVersion | Select Name, OperatingSystem, OperatingSystemVersion"
                description = "Inventory server operating systems for patch management"
            },
            @{
                scenario = "Find computers in specific OU"
                command = "Get-ADComputer -SearchBase 'OU=Workstations,DC=company,DC=com' -Filter * | Select Name, DNSHostName, Enabled"
                description = "List computers in specific organizational unit for targeted management"
            }
        )
    },

    'Get-ADGroupMember' = @{
        real_world_examples = @(
            @{
                scenario = "Audit privileged group membership"
                command = "Get-ADGroupMember -Identity 'Domain Admins' | Select Name, SamAccountName, ObjectClass | Sort-Object Name"
                description = "Review membership of high-privilege groups for security compliance"
            },
            @{
                scenario = "Export group membership report"
                command = "Get-ADGroupMember -Identity 'Finance Team' -Recursive | Select Name, SamAccountName | Export-CSV 'C:\\FinanceTeamMembers.csv' -NoTypeInformation"
                description = "Generate membership reports for departmental groups including nested groups"
            },
            @{
                scenario = "Find nested group memberships"
                command = "Get-ADGroupMember -Identity 'All Employees' -Recursive | Where-Object {$_.ObjectClass -eq 'group'} | Select Name"
                description = "Identify nested groups within a parent group for access analysis"
            }
        )
    },

    'Add-ADGroupMember' = @{
        real_world_examples = @(
            @{
                scenario = "Bulk add users to group from CSV"
                command = "Import-CSV 'C:\\NewHires.csv' | ForEach-Object { Add-ADGroupMember -Identity 'All Employees' -Members $_.SamAccountName }"
                description = "Add multiple new employees to company-wide groups during onboarding"
            },
            @{
                scenario = "Add user to multiple groups"
                command = "@('VPN Users', 'Email Access', 'File Share Access') | ForEach-Object { Add-ADGroupMember -Identity $_ -Members 'jdoe' }"
                description = "Grant standard access by adding user to multiple required groups"
            },
            @{
                scenario = "Add computer to security group"
                command = "Add-ADGroupMember -Identity 'Workstation Management' -Members 'COMPUTER01$'"
                description = "Add computer accounts to groups for GPO targeting or software deployment"
            }
        )
    },

    'Remove-ADGroupMember' = @{
        real_world_examples = @(
            @{
                scenario = "Employee termination cleanup"
                command = "Get-ADUser -Identity 'jdoe' -Properties MemberOf | ForEach-Object { $_.MemberOf | ForEach-Object { Remove-ADGroupMember -Identity $_ -Members 'jdoe' -Confirm:$false } }"
                description = "Remove terminated employee from all groups during offboarding process"
            },
            @{
                scenario = "Remove user from privileged groups"
                command = "@('Domain Admins', 'Enterprise Admins', 'Schema Admins') | ForEach-Object { Remove-ADGroupMember -Identity $_ -Members 'former-admin' -Confirm:$false }"
                description = "Revoke administrative privileges when role changes"
            },
            @{
                scenario = "Bulk remove users from project group"
                command = "Import-CSV 'C:\\ProjectEnd.csv' | ForEach-Object { Remove-ADGroupMember -Identity 'Project Alpha' -Members $_.SamAccountName -Confirm:$false }"
                description = "Remove multiple users from project-specific groups when project ends"
            }
        )
    },

    'New-ADUser' = @{
        real_world_examples = @(
            @{
                scenario = "Standard new employee creation"
                command = "New-ADUser -Name 'John Doe' -SamAccountName 'jdoe' -UserPrincipalName '<EMAIL>' -Path 'OU=Users,DC=company,DC=com' -AccountPassword (ConvertTo-SecureString 'TempPass123!' -AsPlainText -Force) -Enabled $true -Department 'IT' -Title 'System Administrator'"
                description = "Create new user account with standard attributes for employee onboarding"
            },
            @{
                scenario = "Bulk user creation from CSV"
                command = "Import-CSV 'C:\\NewEmployees.csv' | ForEach-Object { New-ADUser -Name $_.FullName -SamAccountName $_.Username -UserPrincipalName ($_.Username + '@company.com') -Path $_.OU -AccountPassword (ConvertTo-SecureString $_.TempPassword -AsPlainText -Force) -Enabled $true -Department $_.Department }"
                description = "Mass create user accounts from HR data for large onboarding batches"
            },
            @{
                scenario = "Service account creation"
                command = "New-ADUser -Name 'SQL Service Account' -SamAccountName 'svc-sql' -Path 'OU=Service Accounts,DC=company,DC=com' -AccountPassword (ConvertTo-SecureString 'ComplexServicePass123!' -AsPlainText -Force) -Enabled $true -PasswordNeverExpires $true -CannotChangePassword $true"
                description = "Create service account with appropriate security settings"
            }
        )
    },

    'Get-ADDefaultDomainPasswordPolicy' = @{
        real_world_examples = @(
            @{
                scenario = "Security compliance audit"
                command = "Get-ADDefaultDomainPasswordPolicy | Select-Object ComplexityEnabled, MinPasswordLength, MaxPasswordAge, MinPasswordAge, PasswordHistoryCount"
                description = "Review password policy settings for compliance with security standards"
            },
            @{
                scenario = "Password policy documentation"
                command = "Get-ADDefaultDomainPasswordPolicy | Format-List * | Out-File 'C:\\PasswordPolicyReport.txt'"
                description = "Document current password policy settings for security documentation"
            },
            @{
                scenario = "Compare policies across domains"
                command = "Get-ADDefaultDomainPasswordPolicy -Server 'domain1.com'; Get-ADDefaultDomainPasswordPolicy -Server 'domain2.com'"
                description = "Compare password policies between different domains in forest"
            }
        )
    },

    'Disable-ADAccount' = @{
        real_world_examples = @(
            @{
                scenario = "Employee termination"
                command = "Disable-ADAccount -Identity 'jdoe'; Set-ADUser -Identity 'jdoe' -Description 'Terminated $(Get-Date -Format 'yyyy-MM-dd')'"
                description = "Disable account and add termination date for audit trail"
            },
            @{
                scenario = "Bulk disable inactive accounts"
                command = "Get-ADUser -Filter * -Properties LastLogonDate | Where-Object {$_.LastLogonDate -lt (Get-Date).AddDays(-180)} | ForEach-Object { Disable-ADAccount -Identity $_.SamAccountName }"
                description = "Disable accounts that haven't been used for 6 months"
            },
            @{
                scenario = "Temporary account suspension"
                command = "Disable-ADAccount -Identity 'contractor1'; Set-ADUser -Identity 'contractor1' -Description 'Suspended pending investigation $(Get-Date -Format 'yyyy-MM-dd')'"
                description = "Temporarily disable account during security investigation"
            }
        )
    }
}

function Enhance-HighFrequencyCommands {
    param(
        [Parameter(Mandatory = $true)]
        [string]$InputPath,
        
        [Parameter(Mandatory = $true)]
        [string]$OutputPath
    )
    
    Write-Host "Loading RAG-optimized data from: $InputPath" -ForegroundColor Green
    
    if (-not (Test-Path $InputPath)) {
        throw "Input file not found: $InputPath"
    }
    
    # Load the JSON data
    $jsonContent = Get-Content -Path $InputPath -Raw -Encoding UTF8
    $data = $jsonContent | ConvertFrom-Json
    
    if (-not $data.commands) {
        throw "Invalid JSON format: 'commands' array not found"
    }
    
    Write-Host "Processing $($data.commands.Count) commands for enhancement..." -ForegroundColor Yellow
    
    $enhancedCount = 0
    
    # Process each command
    foreach ($command in $data.commands) {
        $commandName = $command.command_name
        $frequency = $command.usage_frequency
        
        # Only enhance commands with frequency > 50
        if ($frequency -gt 50 -and $EnhancedExamples.ContainsKey($commandName)) {
            $examples = $EnhancedExamples[$commandName].real_world_examples
            
            # Add real-world examples to the command
            $command | Add-Member -NotePropertyName "real_world_examples" -NotePropertyValue $examples -Force
            
            # Enhance the RAG document with real-world context
            $originalRagDoc = $command.rag_document
            $realWorldContext = "`n`nReal-World Usage Scenarios:`n"
            
            foreach ($example in $examples) {
                $realWorldContext += "- $($example.scenario): $($example.description)`n"
                $realWorldContext += "  Command: $($example.command)`n"
            }
            
            $command.rag_document = $originalRagDoc + $realWorldContext
            
            $enhancedCount++
            Write-Host "  ✓ Enhanced $commandName (frequency: $frequency) with $($examples.Count) real-world examples" -ForegroundColor Cyan
        }
    }
    
    # Update metadata
    if ($data.metadata) {
        $data.metadata | Add-Member -NotePropertyName "real_world_examples_added" -NotePropertyValue (Get-Date -Format "yyyy-MM-dd HH:mm:ss") -Force
        $data.metadata | Add-Member -NotePropertyName "enhanced_commands_count" -NotePropertyValue $enhancedCount -Force
        
        # Update features list
        if ($data.metadata.features) {
            $features = [System.Collections.ArrayList]$data.metadata.features
            if ($features -notcontains "real_world_examples") {
                $features.Add("real_world_examples") | Out-Null
                $data.metadata.features = $features.ToArray()
            }
        }
    }
    
    # Save the enhanced data
    Write-Host "Saving enhanced data to: $OutputPath" -ForegroundColor Green
    $enhancedJson = $data | ConvertTo-Json -Depth 25
    $enhancedJson | Out-File -FilePath $OutputPath -Encoding UTF8
    
    Write-Host "✅ Real-world examples added successfully!" -ForegroundColor Green
    Write-Host "   - Commands enhanced with real-world examples: $enhancedCount" -ForegroundColor White
    Write-Host "   - High-frequency commands (>50) now include practical usage scenarios" -ForegroundColor White
    Write-Host "   - Enhanced RAG documents with community-driven examples" -ForegroundColor White
}

# Main execution
try {
    Enhance-HighFrequencyCommands -InputPath $InputFile -OutputPath $OutputFile
} catch {
    Write-Error "Failed to enhance commands with real-world examples: $_"
    exit 1
}
