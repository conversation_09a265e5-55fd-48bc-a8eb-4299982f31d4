# Test script to scrape a single PowerShell command page from Microsoft Learn
# This will help us understand the full structure and optimize extraction

param(
    [string]$Url = "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser",
    [string]$OutputFile = "single_page_test.json",
    [switch]$SaveRawHtml
)

# Simple HTML decoding function (since System.Web.HttpUtility may not be available)
function Decode-Html {
    param([string]$HtmlText)

    if (-not $HtmlText) { return "" }

    $decoded = $HtmlText
    $decoded = $decoded -replace '&amp;', '&'
    $decoded = $decoded -replace '&lt;', '<'
    $decoded = $decoded -replace '&gt;', '>'
    $decoded = $decoded -replace '&quot;', '"'
    $decoded = $decoded -replace '&#39;', "'"
    $decoded = $decoded -replace '&nbsp;', ' '

    return $decoded
}

Write-Host "Testing single page scraper for: $Url" -ForegroundColor Green

try {
    # Get the page content
    Write-Host "Fetching page content..." -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri $Url -UseBasicParsing -TimeoutSec 30
    $content = $response.Content
    
    if ($SaveRawHtml) {
        $htmlFile = $OutputFile -replace '\.json$', '.html'
        $content | Out-File -FilePath $htmlFile -Encoding UTF8
        Write-Host "Raw HTML saved to: $htmlFile" -ForegroundColor Cyan
    }
    
    # Initialize comprehensive result structure
    $result = @{
        url = $Url
        scraped_at = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        page_info = @{
            title = ""
            module = ""
            command_name = ""
        }
        synopsis = ""
        description = ""
        syntax_blocks = @()
        parameters = @{}
        examples = @()
        inputs = @()
        outputs = @()
        notes = @()
        related_links = @()
        raw_sections = @{}
    }
    
    Write-Host "Extracting page information..." -ForegroundColor Yellow
    
    # Extract page title
    if ($content -match '<title>([^<]+)</title>') {
        $result.page_info.title = (Decode-Html $matches[1]).Trim()
        Write-Host "Title: $($result.page_info.title)" -ForegroundColor White
    }

    # Extract command name from URL or title
    if ($Url -match '/([^/]+)$') {
        $result.page_info.command_name = $matches[1]
    }

    # Extract module info
    if ($content -match 'Module:\s*<[^>]*>\s*<a[^>]*>([^<]+)</a>') {
        $result.page_info.module = $matches[1].Trim()
    }

    # Extract synopsis from meta description
    if ($content -match '<meta name="description" content="([^"]+)"') {
        $result.synopsis = Decode-Html $matches[1]
        Write-Host "Synopsis: $($result.synopsis)" -ForegroundColor White
    }
    
    Write-Host "Extracting main sections..." -ForegroundColor Yellow
    
    # Extract all major sections by finding h2 headers
    $sectionMatches = [regex]::Matches($content, '(?s)<h2[^>]*>([^<]+)</h2>(.*?)(?=<h2[^>]*>|<div[^>]*class="[^"]*feedback|$)')
    
    foreach ($sectionMatch in $sectionMatches) {
        $sectionTitle = $sectionMatch.Groups[1].Value.Trim()
        $sectionContent = $sectionMatch.Groups[2].Value
        
        Write-Host "Processing section: $sectionTitle" -ForegroundColor Cyan
        
        # Store raw section content for analysis
        $result.raw_sections[$sectionTitle] = $sectionContent
        
        switch ($sectionTitle.ToLower()) {
            "description" {
                # Extract description paragraphs
                $descMatches = [regex]::Matches($sectionContent, '<p>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</p>')
                $descriptions = @()
                foreach ($descMatch in $descMatches) {
                    $desc = (Decode-Html $descMatch.Groups[1].Value) -replace '<[^>]+>', '' -replace '\s+', ' '
                    $descriptions += $desc.Trim()
                }
                $result.description = $descriptions -join "`n`n"
            }
            
            "syntax" {
                # Extract syntax blocks with their headers
                $syntaxSections = [regex]::Matches($sectionContent, '(?s)<h3[^>]*>([^<]+)</h3>\s*<pre><code[^>]*>([^<]+)</code></pre>')
                foreach ($syntaxSection in $syntaxSections) {
                    $syntaxName = $syntaxSection.Groups[1].Value.Trim()
                    $syntaxCode = Decode-Html $syntaxSection.Groups[2].Value.Trim()

                    $result.syntax_blocks += @{
                        name = $syntaxName
                        code = $syntaxCode
                    }
                }
            }
            
            "examples" {
                # Extract examples with titles and descriptions
                $exampleSections = [regex]::Matches($sectionContent, '(?s)<h3[^>]*>([^<]+)</h3>(.*?)(?=<h3[^>]*>|$)')
                foreach ($exampleSection in $exampleSections) {
                    $exampleTitle = $exampleSection.Groups[1].Value.Trim()
                    $exampleContent = $exampleSection.Groups[2].Value
                    
                    # Extract code blocks
                    $codeMatches = [regex]::Matches($exampleContent, '<pre><code[^>]*>([^<]+)</code></pre>')
                    $codes = @()
                    foreach ($codeMatch in $codeMatches) {
                        $codes += Decode-Html $codeMatch.Groups[1].Value.Trim()
                    }

                    # Extract description paragraphs
                    $descMatches = [regex]::Matches($exampleContent, '<p>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</p>')
                    $descriptions = @()
                    foreach ($descMatch in $descMatches) {
                        $desc = (Decode-Html $descMatch.Groups[1].Value) -replace '<[^>]+>', '' -replace '\s+', ' '
                        $descriptions += $desc.Trim()
                    }
                    
                    $result.examples += @{
                        title = $exampleTitle
                        description = $descriptions -join "`n"
                        code_blocks = $codes
                    }
                }
            }
        }
    }
    
    Write-Host "Extracting parameters..." -ForegroundColor Yellow
    
    # Extract parameters section specifically
    $parametersSection = [regex]::Match($content, '(?s)<h2[^>]*>Parameters</h2>(.*?)(?=<h2[^>]*>|<div[^>]*class="[^"]*feedback|$)')
    if ($parametersSection.Success) {
        $paramContent = $parametersSection.Groups[1].Value
        
        # Find individual parameter sections
        $paramSections = [regex]::Matches($paramContent, '(?s)<h3[^>]*>([^<]+)</h3>(.*?)(?=<h3[^>]*>|$)')
        
        foreach ($paramSection in $paramSections) {
            $paramName = $paramSection.Groups[1].Value.Trim() -replace '^-', ''
            $paramDetails = $paramSection.Groups[2].Value
            
            Write-Host "  Processing parameter: $paramName" -ForegroundColor Gray
            
            $paramInfo = @{
                name = $paramName
                description = ""
                properties = @{}
                raw_content = $paramDetails
            }
            
            # Extract parameter description
            $descMatch = [regex]::Match($paramDetails, '<p>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</p>')
            if ($descMatch.Success) {
                $paramInfo.description = (Decode-Html $descMatch.Groups[1].Value) -replace '<[^>]+>', '' -replace '\s+', ' '
            }

            # Extract parameter properties from table
            $tableMatches = [regex]::Matches($paramDetails, '(?s)<table[^>]*>(.*?)</table>')
            foreach ($tableMatch in $tableMatches) {
                $tableContent = $tableMatch.Groups[1].Value
                $rowMatches = [regex]::Matches($tableContent, '(?s)<tr[^>]*><td[^>]*>([^<]+)</td><td[^>]*>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</td></tr>')

                foreach ($rowMatch in $rowMatches) {
                    $propName = $rowMatch.Groups[1].Value.Trim() -replace ':$', ''
                    $propValue = (Decode-Html $rowMatch.Groups[2].Value) -replace '<[^>]+>', '' -replace '\s+', ' '
                    $paramInfo.properties[$propName] = $propValue.Trim()
                }
            }
            
            $result.parameters[$paramName] = $paramInfo
        }
    }
    
    Write-Host "Saving results..." -ForegroundColor Yellow
    
    # Save the comprehensive result
    $json = $result | ConvertTo-Json -Depth 10
    $json | Out-File -FilePath $OutputFile -Encoding UTF8
    
    # Display summary
    Write-Host "`n" + "="*60 -ForegroundColor Green
    Write-Host "EXTRACTION COMPLETE!" -ForegroundColor Green
    Write-Host "="*60 -ForegroundColor Green
    Write-Host "Page Title: $($result.page_info.title)" -ForegroundColor White
    Write-Host "Command: $($result.page_info.command_name)" -ForegroundColor White
    Write-Host "Module: $($result.page_info.module)" -ForegroundColor White
    Write-Host "Syntax Blocks: $($result.syntax_blocks.Count)" -ForegroundColor Cyan
    Write-Host "Parameters: $($result.parameters.Count)" -ForegroundColor Cyan
    Write-Host "Examples: $($result.examples.Count)" -ForegroundColor Cyan
    Write-Host "Raw Sections: $($result.raw_sections.Count)" -ForegroundColor Cyan
    Write-Host "Output saved to: $OutputFile" -ForegroundColor White
    Write-Host "File size: $([math]::Round((Get-Item $OutputFile).Length / 1KB, 2)) KB" -ForegroundColor Cyan
    
    if ($result.parameters.Count -gt 0) {
        Write-Host "`nParameters found:" -ForegroundColor Yellow
        $result.parameters.Keys | Sort-Object | ForEach-Object {
            $param = $result.parameters[$_]
            Write-Host "  -$($_): $($param.description.Substring(0, [Math]::Min(80, $param.description.Length)))..." -ForegroundColor Gray
        }
    }
    
    if ($result.examples.Count -gt 0) {
        Write-Host "`nExamples found:" -ForegroundColor Yellow
        $result.examples | ForEach-Object {
            Write-Host "  $($_.title)" -ForegroundColor Gray
        }
    }
    
}
catch {
    Write-Error "Failed to scrape page: $($_.Exception.Message)"
    Write-Error $_.Exception.StackTrace
}
