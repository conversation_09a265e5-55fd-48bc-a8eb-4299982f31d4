BUILD COMMAND: e:\os\tools\CoreBuild\amd64\build.exe  -parent -c

Reading historical build information...
Reading historical build information completed in [0:00:00.000]
BuildExe GUID: {31EEA93D-E76C-4FBB-A9E7-3D3C348C4A5C}
Launching process: e:\os\tools\CoreBuild\amd64\tracer.exe  /skip:1 -f:.\buildfre.trc -guid:e:\os\src\registered_data.ini   /logPrefix:buildfre /c:"e:\os\tools\CoreBuild\amd64\buildc.exe" /s:localhost:29026  
1>  *************
1>'e:\os\tools\CoreBuild\amd64\tracer.exe  /skip:1 -f:.\buildfre.trc -guid:e:\os\src\registered_data.ini   /logPrefix:buildfre /c:"e:\os\tools\CoreBuild\amd64\buildc.exe" /s:localhost:29026  '
1>info: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      Processing Build Trace File Logger ded2b352-5dc7-49c8-8be8-4fe5a497c85f
Merging config files using BUILD_CONFIG_FILE=e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config
BUILD: (ActiveWorkLoad)*, ElapsedTime(s),Counter,Available Memory (%),Disk I/O (ms),Disk Usage (%),CPU (%),Previous,New,Last Max,Permitted Threads,ThreadPool Memory Footprint (bytes), Max Memory Allowed (bytes), Submitted Thread,Running Threads,WorkItems (Available), WorkItems (Waiting),Pass,Priority,Peak Memory Project (bytes),Directory,MachineName
1>  1>[0:00:00.219] [Pass0 ] (none) {1}
3001>Merging config files  *************
3001>'mergeconfigfilesforbuildexe.cmd '
3001>MergeConfigFilesForBuildExe.cmd: Merged config file current: [e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config].
Executing PreGraph commands  *************
1>Performing pregraph steps...  *************
1>'build_pre_graph -c'
1>(build_pre_graph.cmd) e:\os\tools\NodeJS.x64\node.exe e:\os\src\tools\nmakejs\Transpile.js
1>(build_pre_graph.cmd) e:\os\tools\NodeJS.x64\node.exe e:\os\obj\amd64fre\objfre\amd64\NMakeJS\PreGraph.js
1>(build_pre_graph.cmd) Determining best branch for vpack with prefix "cdg" and suffix "amd64fre"...
1>(build_pre_graph.cmd) Latest vPack "cdg.rs_wsd_cfe_adai.amd64fre" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27910.1000.2507241906
1>(build_pre_graph.cmd) Preferring this branch's vpack information
1>(build_pre_graph.cmd) Latest vPack "cdg.rs_wsd_cfe_adai.amd64fre" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27910.1000.2507241906
1>(build_pre_graph.cmd) Not pulling vpack cdg.rs_wsd_cfe_adai.amd64fre because it is up-to-date (matches the marker file at e:\os\cdg\Target.cdg-amd64fre.man).
1>(build_pre_graph.cmd) Completed successfully.
Pre-Graph completed in [0:00:00.641]
Executing preprocess commands  *************
1>warn: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      Task table not implemented
1>(build_pre_process.cmd) Determining best branch for vpack with prefix "publics" and suffix "amd64"...
1>(build_pre_process.cmd) Latest vPack "publics.rs_wsd_cfe_adai.amd64" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27910.1000.2507241822
1>(build_pre_process.cmd) Preferring this branch's vpack information
1>(build_pre_process.cmd) Latest vPack "publics.rs_wsd_cfe_adai.amd64" version from label file e:\os\src\sdpublic\misc\Labels\rs_wsd_cfe_adai_label.xml is 27910.1000.2507241822
1>(build_pre_process.cmd) No update necessary [elapsed=0s].
1>(build_pre_process.cmd) e:\os\tools\NodeJS.x64\node.exe e:\os\obj\amd64fre\objfre\amd64\NMakeJS\PreProcess.js
1>Skipping pull of Xbox BBT manifest
1>Projecting build config
1> e:\os\tools\perl64\bin\perl.exe e:\os\src\tools\project-buildconfigs.pl -config:e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config -buildOfficialBranch: -outputFolder:e:\os\obj\amd64fre\objfre\amd64\projectedconfig  -branchkeys: -keys:
1>(project-buildconfigs.pl) Done projecting build configuration keys
1>Skipping Source Link files generation
1> c:\windows\system32\cmd.exe /c del /Q e:\os\obj\amd64fre\objfre\amd64\CoreBuild\__SourceLinkConfiguration_uncached__.json 2>nul
1>Skipping PGI instrumentation.
1>Skipping OneCore Velocity import in a branch that builds OneCore
1>(build_pre_process.cmd) Completed successfully.
DBB MODE: Retrieving dependency information. Please wait...
Examining e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv (directory tree)
1>  2>[0:00:08.797] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib {5}
1>  3>[0:00:08.813] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell {7}
1>  1>[0:00:08.813] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server {4}
1>  2>[0:00:08.828] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll {6}
1>  1>[0:00:08.844] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\idl {8}
1>  2>[0:00:08.860] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll {9}
1>  1>[0:00:08.875] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller {10}
ScanSourceDirectories: Total dirs files scanned = 2
Scanning focus directories completed in [0:00:00.172]
Processing time to write metadata log file .\buildfre.metadata = 0.016 Secs

BUILD: Scanning directories in defined scope...
Examining e:\os\src (directory tree)
ScanSourceDirectories: Total dirs files scanned = 10
BUILD: Processing dependencies...
1>  1>[0:00:08.907] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\mcpprotocollib {11}
1>  1>[0:00:08.907] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib {12}
1>  1>[0:00:08.922] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib {13}
1>  1>[0:00:10.719] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk {14}
1>  1>[0:00:10.735] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib {15}
1>  1>[0:00:10.735] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib {16}
1>  1>[0:00:10.750] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr {17}
1>  1>[0:00:10.766] [Pass M] e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr {18}
Pre-Build completed in [0:00:09.797]
1>  1>[0:00:11.032] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib {19}
3001>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
3001>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib *************
3001>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  2>[0:00:11.032] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell {20}
3002>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
3002>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell *************
3002>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  3>[0:00:11.032] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server {21}
3003>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3003>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server *************
3003>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\server TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
3004>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
3004>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll *************
3004>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
3005>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\idl
3005>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\idl *************
3005>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\idl TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  4>[0:00:11.032] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll {22}
1>  5>[0:00:11.032] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\idl {23}
1>  6>[0:00:11.032] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll {24}
3006>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
3006>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll *************
3006>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  7>[0:00:11.047] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib {25}
3007>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib
3007>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib *************
3007>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\mcpserversample\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  8>[0:00:11.047] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib {26}
3008>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib
3008>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib *************
3008>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\llmclientlib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>BUILDC : Microsoft (R) Build Engine worker [Build 8.0.250519001+6b8e35f5c0ee29c4c18a354e33cc8b695d5695ad]
1>BUILDC : Copyright (C) Microsoft Corporation. All rights reserved.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Controller)
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Logger)
1>BUILDC (IsHost): Buildc is running on : RUPO-DELL and host is : localhost.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): c:\windows\system32\cmd.exe /c mergeconfigfilesforbuildexe.cmd  at (none).
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=1 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=2 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\server TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=3 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=4 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\idl TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=5 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\idl.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=6 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\mcpserversample\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=7 at e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PA  9>[0:00:11.047] [Pass0 ] e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr {27}
3009>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr
3009>Building generated files in e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr *************
3009>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\admcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
BUILD: (ActiveWorkLoad),10.81,,56,7,1,11,16,0,0,0,0,0,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib,RUPO-DELL
BUILD: (ActiveWorkLoad),10.81,,56,7,1,11,16,16,0,1,1,1,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\powershell,RUPO-DELL
BUILD: (ActiveWorkLoad),10.81,,56,7,1,11,16,16,0,2,2,2,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\server,RUPO-DELL
BUILD: (ActiveWorkLoad),10.81,,56,7,1,11,16,16,0,3,3,3,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\dll,RUPO-DELL
BUILD: (ActiveWorkLoad),10.81,,56,7,1,11,16,16,0,4,4,4,1,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\idl,RUPO-DELL
BUILD: (ActiveWorkLoad),10.81,,56,7,1,11,16,16,0,5,5,5,1,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll,RUPO-DELL
BUILD: (ActiveWorkLoad),10.83,,56,7,1,11,16,16,0,6,6,6,1,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\mcpserversample\lib,RUPO-DELL
BUILD: (ActiveWorkLoad),10.83,,56,7,1,11,16,16,0,7,7,7,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\llmclientlib,RUPO-DELL
BUILD: (ActiveWorkLoad),10.83,,56,7,1,11,16,16,0,8,8,8,0,0,PASS0,0,2000000000,onecore\ds\ds\src\aimx\prod\admcpsvr,RUPO-DELL
3008>Calculated LAYERINFO_MODULE='OneCoreDS'.
3008>makefile.def: TEMP=e:\os\obj\amd64fre\temp\d08d60bd01e85ffb61552e30f760e46b
3008>makefile.def: BUILDINGINDATT=
3008>[Core OS Undocking] NOT using package ''
3008>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib' (target 'llmclientlib', type 'LIBRARY', nt_target_version '0xA000011')
3008>ObjectsMac.ts: validation succeeded
3008>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib" (STL_VER_TELEMETRY)
3008>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3008>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64 already exists.
3008> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\_PASS0_Marker.log
3008> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64
3008> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                    -preserveext:.cpp.h.hxx                                            -scan:..\aimxsrv\inc\wpp.h                                             -DWPP_CHECK_INIT                                               -p:llmclientlib                                                    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)     -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)         -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)      -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)  -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)       llmclientlib.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64
3008> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\binplace_PASS0.rsp 2>nul
3008> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\llmclientlib" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3008>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\llmclientlib in pass PASS0
3009>Calculated LAYERINFO_MODULE='OneCoreDS'.
3009>makefile.def: TEMP=e:\os\obj\amd64fre\temp\7bd64a02e666506c9bcbf75bd47bc0ff
3009>makefile.def: BUILDINGINDATT=
3009>[Core OS Undocking] NOT using package ''
3009>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr' (target 'AdMcpSvr', type 'LIBRARY', nt_target_version '0xA000011')
3009>ObjectsMac.ts: validation succeeded
3009>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr" (STL_VER_TELEMETRY)
3009>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3009>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64 already exists.
3009> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\_PASS0_Marker.log
3009> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64
3009> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                                  -preserveext:.cpp.h.hxx                                                          -scan:..\aimxsrv\inc\wpp.h                                                    -DWPP_CHECK_INIT                                                                 -p:AIMXADMCPSRV                                                                  -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)                       -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)                           -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)                        -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)                    -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)                         AdMcpSvr.cpp  PowerShellFilterParser.cpp  GetADUserTool.cpp  GetADGroupTool.cpp  GetADComputerTool.cpp  GetADDomainTool.cpp  GetADForestTool.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64
3009> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\binplace_PASS0.rsp 2>nul
3009> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\admcpsvr" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3009>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\admcpsvr in pass PASS0
3001>Calculated LAYERINFO_MODULE='OneCoreDS'.
3001>makefile.def: TEMP=e:\os\obj\amd64fre\temp\9f766ea871e3e813d4d923bea429fce8
3001>makefile.def: BUILDINGINDATT=
3001>[Core OS Undocking] NOT using package ''
3001>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib' (target 'aimxclient_s', type 'LIBRARY', nt_target_version '0xA000011')
3001>ObjectsMac.ts: validation succeeded
3001>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib" (STL_VER_TELEMETRY)
3001>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3001>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64 already exists.
3001> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\_PASS0_Marker.log
3001> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64
3001> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                    -preserveext:.cpp.h.hxx                                            -scan:..\clientwpp.h                                             -DWPP_CHECK_INIT                                               -p:AIMXCLIENT                                                    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)     -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)         -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)      -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)  -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)      ..\aimxclient.cpp  ..\aimxrpcclient.cpp  ..\memory.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64
3001> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\binplace_PASS0.rsp 2>nul
3001> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3001>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib in pass PASS0
3007>Calculated LAYERINFO_MODULE='OneCoreDS'.
3007>makefile.def: TEMP=e:\os\obj\amd64fre\temp\62eef837d8bba2be7317ce3010e39a16
3007>makefile.def: BUILDINGINDATT=
3007>[Core OS Undocking] NOT using package ''
3007>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib' (target 'HelloMcpServer', type 'LIBRARY', nt_target_version '0xA000011')
3007>ObjectsMac.ts: validation succeeded
3007>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib" (STL_VER_TELEMETRY)
3007>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3007>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64 already exists.
3007> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64\_PASS0_Marker.log
3007> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64
3007> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                                  -preserveext:.cpp.h.hxx                                                          -scan:..\..\aimxsrv\inc\wpp.h                                                    -DWPP_CHECK_INIT                                                                 -p:AIMXMCPSRV                                                                    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)                       -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)                           -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)                        -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)                    -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)                         ..\HelloMcpServer.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64
3007> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64\binplace_PASS0.rsp 2>nul
3007> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\mcpserversample\lib" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3007>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\mcpserversample\lib in pass PASS0
3004>Calculated LAYERINFO_MODULE='OneCoreDS'.
3004>makefile.def: TEMP=e:\os\obj\amd64fre\temp\8c47d5eb5d3ee6ccc8e5bc86b361c18e
3004>makefile.def: BUILDINGINDATT=
3004>[Core OS Undocking] NOT using package ''
3004>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll' (target 'aimxsrv', type 'DYNLINK', nt_target_version '0xA000011')
3004>ObjectsMac.ts: validation succeeded
3004>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll" (STL_VER_TELEMETRY)
3004>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3004>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64 already exists.
3004> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\_PASS0_Marker.log
3004> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3004> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                -preserveext:.cpp.h.hxx                                        -scan:..\inc\wpp.h                                             -DWPP_CHECK_INIT                                               -p:AIMXSRV                                                     -dll DllMain                                                   -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)     -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)         -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)      -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)  -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)       dllmain.cpp  aimxservice.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3004> e:\os\tools\Windows.Desktop.Tools.amd64\tools\preprocessor.exe -o e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\mui.rcc -i e:\os\src\tools\mui.rcconfig
3004> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\binplace_PASS0.rsp 2>nul
3004> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\dll" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3004>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\dll in pass PASS0
3006>Calculated LAYERINFO_MODULE='OneCoreDS'.
3006>makefile.def: TEMP=e:\os\obj\amd64fre\temp\dfb98b5c9acb5aa63b4771067ebaa07e
3006>makefile.def: BUILDINGINDATT=
3006>[Core OS Undocking] NOT using package ''
3006>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll' (target 'aimxclient', type 'DYNLINK', nt_target_version '0xA000011')
3006>ObjectsMac.ts: validation succeeded
3006>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" (STL_VER_TELEMETRY)
3006>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3006>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64 already exists.
3006> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\_PASS0_Marker.log
3006> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3006> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                    -preserveext:.cpp.h.hxx                                            -scan:..\clientwpp.h                                             -DWPP_CHECK_INIT                                               -p:AIMXCLIENT                                                    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)     -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)         -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)      -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)  -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)      ..\aimxclient.cpp  ..\aimxrpcclient.cpp  ..\memory.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3006> e:\os\tools\Windows.Desktop.Tools.amd64\tools\preprocessor.exe -o e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\mui.rcc -i e:\os\src\tools\mui.rcconfig
3006> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\binplace_PASS0.rsp 2>nul
3006> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3006>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll in pass PASS0
3003>Calculated LAYERINFO_MODULE='OneCoreDS'.
3003>makefile.def: TEMP=e:\os\obj\amd64fre\temp\88ac833fe9d1b08e6764b11a7c42b5e7
3003>makefile.def: BUILDINGINDATT=
3003>[Core OS Undocking] NOT using package ''
3003>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server' (target 'aimxserver', type 'LIBRARY', nt_target_version '0xA000011')
3003>ObjectsMac.ts: validation succeeded
3003>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server" (STL_VER_TELEMETRY)
3003>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3003>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64 already exists.
3003> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\_PASS0_Marker.log
3003> set BUILDMSG=WPP Processing: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64
3003> e:\os\tools\EtwTools\tools\tracewpp.exe  -q    -ext:.cpp.h.hxx                                                    -preserveext:.cpp.h.hxx                                            -scan:..\inc\wpp.h                                             -DWPP_CHECK_INIT                                               -p:AIMXSRV                                                    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)     -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)         -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)      -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...)  -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)      aimxrpcserver.cpp  RequestHandler.cpp  Planner.cpp  Orchestrator.cpp  McpStdioClient.cpp  McpSvrMgr.cpp  McpToolManager.cpp  LLMInfer.cpp  AimxLLMConfig.cpp  InProcessMCPServerBase.cpp  InprocessMcpUtils.cpp  SystemPromptManager.cpp  ConversationManager.cpp  RagServiceManager.cpp  -cfgdir:e:\os\tools\EtwTools\tools\WppConfig\rev1  -odir:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64
3003> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\binplace_PASS0.rsp 2>nul
3003> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\server" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3003>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\server in pass PASS0
3005>Calculated LAYERINFO_MODULE='OneCoreDS'.
3005>makefile.def: TEMP=e:\os\obj\amd64fre\temp\d205b41762dac064f90cc43d6014781a
3005>makefile.def: BUILDINGINDATT=
3005>[Core OS Undocking] NOT using package ''
3005>ObjectsMac.ts: validation succeeded
3005>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3005>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\idl\objfre\amd64 already exists.
3005> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\idl\objfre\amd64\_PASS0_Marker.log
3005> e:\os\tools\MidlCompiler\tools\midl.exe  -sal /amd64  -target NT1012 -oldnames -out e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\idl\objfre\amd64 -Ie:\os\public\amd64fre\sdk\inc -Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore -Ie:\os\public\amd64fre\shared\inc -Ie:\os\public\amd64fre\onecore\external\shared\inc /prefix client "c_" /prefix server "s_" /export aimxrpc.idl
3005>Microsoft (R) 32b/64b MIDL Compiler Version 8.01.0628 
3005>Copyright (c) Microsoft Corporation. All rights reserved.
3005>64 bit Processing .\aimxrpc.idl
3005>aimxrpc.idl
3005>64 bit Processing e:\os\public\amd64fre\onecore\external\shared\inc\wtypes.idl
3005>wtypes.idl
3005>64 bit Processing e:\os\public\amd64fre\onecore\external\shared\inc\wtypesbase.idl
3005>wtypesbase.idl
3005>64 bit Processing e:\os\public\amd64fre\onecore\external\shared\inc\basetsd.h
3005>basetsd.h
3005>64 bit Processing e:\os\public\amd64fre\onecore\external\shared\inc\guiddef.h
3005>guiddef.h
3005>64 bit Processing .\aimxrpc.acf
3005>aimxrpc.acf
3005> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\idl\objfre\amd64\binplace_PASS0.rsp 2>nul
3005> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\idl" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3005>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\idl in pass PASS0
3002>Calculated LAYERINFO_MODULE='OneCoreDS'.
3002>makefile.def: TEMP=e:\os\obj\amd64fre\temp\6c08455455466bb44bf88264ae008789
3002>makefile.def: BUILDINGINDATT=
3002>[Core OS Undocking] NOT using package ''
3002>ObjectsMac.ts: validation succeeded
3002>Starting recursive call to NMAKE for _MAKING_ASMID_INC
3002>Starting _MAKING_ASMID_INC
3002>Calculated LAYERINFO_MODULE='OneCoreDS'.
3002>makefile.def: TEMP=e:\os\obj\amd64fre\temp\6c08455455466bb44bf88264ae008789
3002>makefile.def: BUILDINGINDATT=
3002>[Core OS Undocking] NOT using package ''
3002>ObjectsMac.ts: _objects.mac is not needed; macro validation will be skipped.
3002>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3002>Ending _MAKING_ASMID_INC
3002> set BUILDMSG=making _asmid.inc
3002> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_asmid.inc e:\os\obj\amd64fre\temp\6c08455455466bb44bf88264ae008789\_asmid.inc 2>nul
3002> e:\os\tools\LegacyTools\x86\idtool.exe -id aimxpsh,processorArchitecture=msil,version=10.0.0.0,publicKeyToken=31bf3856ad364e35 -GenerateMakefileInc -out e:\os\obj\amd64fre\temp\6c08455455466bb44bf88264ae008789\_asmid.inc
3002> e:\os\src\tools\onecoreuap\internal\AsmIdToAttribs\asmIdToAttribs.cmd "aimxpsh,processorArchitecture=msil,version=10.0.0.0,publicKeyToken=31bf3856ad364e35" e:\os\obj\amd64fre\temp\6c08455455466bb44bf88264ae008789\_asmid.inc tlbimp
3002> e:\os\src\tools\onecoreuap\internal\AsmIdToAttribs\asmIdToAttribs.cmd "aimxpsh,processorArchitecture=msil,version=10.0.0.0,publicKeyToken=31bf3856ad364e35" e:\os\obj\amd64fre\temp\6c08455455466bb44bf88264ae008789\_asmid.inc sn
3002> c:\windows\system32\cmd.exe /c echo _ASSEMBLY_IDENTITY=aimxpsh,processorArchitecture=msil,version=10.0.0.0,publicKeyToken=31bf3856ad364e35 >> e:\os\obj\amd64fre\temp\6c08455455466bb44bf88264ae008789\_asmid.inc
3002> c:\windows\system32\cmd.exe /c copy e:\os\obj\amd64fre\temp\6c08455455466bb44bf88264ae008789\_asmid.inc e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_asmid.inc >nul
3002>Finished recursively calling NMAKE for _MAKING_ASMID_INC
3002>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3002>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64 already exists.
3002> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_PASS0_Marker.log
3002> set BUILDMSG=making _asmid.xml
3002> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_asmid.xml e:\os\obj\amd64fre\temp\6c08455455466bb44bf88264ae008789\_asmid.xml 2>nul
3002> e:\os\tools\LegacyTools\x86\idtool.exe -id aimxpsh,processorArchitecture=msil,version=10.0.0.0,publicKeyToken=31bf3856ad364e35 -EmptyComponentManifest -out e:\os\obj\amd64fre\temp\6c08455455466bb44bf88264ae008789\_asmid.xml
3002> c:\windows\system32\cmd.exe /c copy e:\os\obj\amd64fre\temp\6c08455455466bb44bf88264ae008789\_asmid.xml e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_asmid.xml >nul
3002> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\binplace_PASS0.rsp 2>nul
3002> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\powershell" -Pass PASS0 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3002>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\powershell in pass PASS0
BUILD: Pass complete => PASS0
1>  1>[0:00:15.094] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib {28}
3201>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
3201>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib *************
3201>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  2>[0:00:15.094] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell {29}
3202>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
3202>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell *************
3202>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  3>[0:00:15.094] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll {30}
3203>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
3203>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll *************
3203>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
3204>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller
3204>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller *************
3204>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
3205>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\mcpprotocollib
3205>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\mcpprotocollib *************
3205>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\mcpprotocollib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  4>[0:00:15.094] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller {31}
1>  5>[0:00:15.094] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\mcpprotocollib {32}
1>  6>[0:00:15.110] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib {33}
1>  7>[0:00:15.110] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk {34}
1>  8>[0:00:15.110] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib {35}
3206>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib
3206>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib *************
3206>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\mcpserversample\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  9>[0:00:15.110] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr {36}
3207>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk
3207>Executing Pass1 MSBuild Tasks e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk *************
3207>'msbuild.cmd "vcpkg.proj" /nologo /p:BuildingInSeparatePasses=true /p:BuildingWithBuildExe=true /clp:NoSummary /verbosity:normal /Target:BuildCompiled /p:Pass=Compile /p:ObjectPath=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\ /p:TARGET_PLATFORM=windows /p:CODE_SETS_BUILDING=cs_windows:cs_xbox:cs_phone /p:CODE_SETS_TAGGED=cs_windows:cs_xbox'
1>SS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\llmclientlib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=8 at e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib.
1>BUILDC (BuildSocket): RegisterClient rupo-dell:0 (BuildC Worker)
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\os\src\tools\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\admcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=9 at e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=1 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=2 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=3 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=4 at e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\mcpprotocollib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=5 at e:\os\src\onecore\ds\ds\src\aimx\prod\mcpprotocollib.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\mcpserversample\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=6 at e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib.
1>BUILDC (PipeSpawn): c:\windows\system32\cmd.exe /c msbuild.cmd "vcpkg.proj" /nologo /p:BuildingInSeparatePasses=true /p:BuildingWithBuildExe=true /clp:NoSummary /verbosity:normal /Target:BuildCompiled /p:Pass=Compile /p:ObjectPath=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\ /p:TARGET_PLATFORM=windows /p:CODE_SETS_BUILDING=cs_windows:cs_xbox:cs_phone /p:CODE_SETS_TAGGED=cs_windows:cs_xbox /p:THREAD_ID=7 at e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk.
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\llmclientlib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox THREAD_ID=8 at e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib.
3208>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib
3208>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib *************
3208>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\llmclientlib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
3209>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr
3209>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr *************
3209>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\admcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
BUILD: (ActiveWorkLoad),14.88,,57,0,1,53,16,0,0,0,0,0,1,2,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib,RUPO-DELL
BUILD: (ActiveWorkLoad),14.88,,57,0,1,53,16,16,0,1,1,1,0,2,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\powershell,RUPO-DELL
BUILD: (ActiveWorkLoad),14.88,,57,0,1,53,16,16,0,2,2,2,0,2,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll,RUPO-DELL
BUILD: (ActiveWorkLoad),14.88,,57,0,1,53,16,16,0,3,3,3,1,2,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller,RUPO-DELL
BUILD: (ActiveWorkLoad),14.88,,57,0,1,53,16,16,0,4,4,4,1,2,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\mcpprotocollib,RUPO-DELL
BUILD: (ActiveWorkLoad),14.89,,57,0,1,53,16,16,0,5,5,5,1,2,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\mcpserversample\lib,RUPO-DELL
BUILD: (ActiveWorkLoad),14.89,,57,0,1,53,16,16,0,6,6,6,1,2,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\cpprestsdk,RUPO-DELL
BUILD: (ActiveWorkLoad),14.89,,57,0,1,53,16,16,0,7,7,7,1,2,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\llmclientlib,RUPO-DELL
BUILD: (ActiveWorkLoad),14.89,,57,0,1,53,16,16,0,8,8,8,0,2,PASS1,0,2000000000,onecore\ds\ds\src\aimx\prod\admcpsvr,RUPO-DELL
3204>Calculated LAYERINFO_MODULE='OneCoreDS'.
3204>makefile.def: TEMP=e:\os\obj\amd64fre\temp\298290ca10bf99cddd4e246cce04ebc3
3204>makefile.def: BUILDINGINDATT=
3204>[Core OS Undocking] NOT using package ''
3204>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller' (target 'Aimx.ServiceInstaller', type 'PROGRAM', nt_target_version '0xA000011')
3204>ObjectsMac.ts: validation succeeded
3204>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3204>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64 already exists.
3204> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\_PASS1_Marker.log
3204> e:\os\tools\Windows.Desktop.Tools.amd64\tools\preprocessor.exe -o e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\mui.rcc -i e:\os\src\tools\mui.rcconfig
3204> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\298290ca10bf99cddd4e246cce04ebc3\cl_1.rsp
3204>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3204>Copyright (C) Microsoft Corporation.  All rights reserved.
3204>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64/"
3204>   /FC
3204>   /Iamd64
3204>   /I.
3204>   /Ie:\os\src\data\MSRC
3204>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64
3204>   /Ie:\os\src\onecore\ds\inc
3204>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3204>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3204>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3204>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3204>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3204>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3204>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3204>   /Ie:\os\public\amd64fre\shared\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3204>   /Ie:\os\public\amd64fre\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3204>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3204>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3204>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3204>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3204>   /Ie:\os\public\amd64fre\shared\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3204>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3204>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3204>   /Ie:\os\public\amd64fre\internal\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3204>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3204>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3204>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3204>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3204>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3204>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3204>   /D_WIN64
3204>   /D_AMD64_
3204>   /DAMD64
3204>   /DCONDITION_HANDLING=1
3204>   /DNT_INST=0
3204>   /DWIN32=100
3204>   /D_NT1X_=100
3204>   /DWINNT=1
3204>   /D_WIN32_WINNT=0x0A00
3204>   /DWINVER=0x0A00
3204>   /D_WIN32_IE=0x0A00
3204>   /DWIN32_LEAN_AND_MEAN=1
3204>   /DDEVL=1
3204>   /DNDEBUG
3204>   /D_DLL=1
3204>   /D_MT=1
3204>   -DNT_IUM
3204>   -DUNICODE
3204>   -D_UNICODE
3204>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3204>   /D_USE_DEV11_CRT
3204>   -D_APISET_MINWIN_VERSION=0x0115
3204>   -D_APISET_MINCORE_VERSION=0x0114
3204>   /DFE_SB
3204>   /DFE_IME
3204>   /DNTDDI_VERSION=0x0A000011
3204>   /DWINBLUE_KBSPRING14
3204>   /DBUILD_WINDOWS
3204>   /DUNDOCKED_WINDOWS_UCRT
3204>   /D__WRL_CONFIGURATION_LEGACY__
3204>   /DBUILD_UMS_ENABLED=1
3204>   /DBUILD_WOW64_ENABLED=1
3204>   /DBUILD_ARM64X_ENABLED=0
3204>   /DEXECUTABLE_WRITES_SUPPORT=0
3204>   -D_USE_DECLSPECS_FOR_SAL=1
3204>   -DUNLOADABLE_DELAYLOAD_IMPLEMENTATION
3204>   -D__PLACEHOLDER_SAL=1
3204>   /c
3204>   /Zc:wchar_t-
3204>   /Zl
3204>   /Zp8
3204>   /Gy
3204>   /W4
3204>   /d1import_no_registry
3204>   /EHs-c-
3204>   /GR-
3204>   /GF
3204>   /GS
3204>   /Z7
3204>   /Oxs
3204>   /GL
3204>   /Z7
3204>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3204>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3204>   /w15043
3204>   /Zc:rvalueCast
3204>   -D_UCRT
3204>   -D_CONST_RETURN=
3204>   -D_CRT_SECURE_NO_WARNINGS
3204>   -D_CRT_NON_CONFORMING_SWPRINTFS
3204>   -D_CRT_NONSTDC_NO_WARNINGS
3204>   -D_NO_CPPLIB_VER
3204>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3204>   /D_CRT_STDIO_INLINE=extern
3204>   /D_NO_CRT_STDIO_INLINE
3204>   /D_ACRTIMP_ALT=
3204>   /D_ALLOW_MSC_VER_MISMATCH
3204>   /D_ALLOW_ITERATOR_DEBUG_LEVEL_MISMATCH
3204>   /D_ALLOW_RUNTIME_LIBRARY_MISMATCH
3204>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3204>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3204>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3204>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3204>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3204>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3204>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3204>   /D_HAS_STD_BYTE=0
3204>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3204>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3204>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3204>   /D_FULL_IOBUF
3204>   /d1initAll:Mask11
3204>   /d1initAll:FillPattern0
3204>   /d1nodatetime
3204>   /d1trimfile:e:\os\src\=BASEDIR
3204>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3204>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3204>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3204>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3204>   /d2AllowCompatibleILVersions
3204>   /d2Zi+
3204>   /ZH:SHA_256
3204>   /wd4986
3204>   /wd4987
3204>   /wd4471
3204>   /wd4369
3204>   /wd4309
3204>   /wd4754
3204>   /wd4427
3204>   /d2DeepThoughtInliner-
3204>   /d2implyavx512upperregs-
3204>   /Wv:19.23
3204>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\
3204>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3204>   /wl4002
3204>   /wl4003
3204>   /wl4005
3204>   /wl4006
3204>   /wl4007
3204>   /wl4008
3204>   /wl4010
3204>   /wl4013
3204>   /wl4015
3204>   /wl4018
3204>   /wl4020
3204>   /wl4022
3204>   /wl4024
3204>   /wl4025
3204>   /wl4026
3204>   /wl4027
3204>   /wl4028
3204>   /wl4029
3204>   /wl4030
3204>   /wl4031
3204>   /wl4033
3204>   /wl4034
3204>   /wl4036
3204>   /wl4038
3204>   /wl4041
3204>   /wl4042
3204>   /wl4045
3204>   /wl4047
3204>   /wl4048
3204>   /wl4049
3204>   /wl4056
3204>   /wl4066
3204>   /wl4067
3204>   /wl4068
3204>   /wl4073
3204>   /wl4074
3204>   /wl4075
3204>   /wl4076
3204>   /wl4077
3204>   /wl4079
3204>   /wl4080
3204>   /wl4081
3204>   /wl4083
3204>   /wl4085
3204>   /wl4086
3204>   /wl4087
3204>   /wl4088
3204>   /wl4089
3204>   /wl4090
3204>   /wl4091
3204>   /wl4094
3204>   /wl4096
3204>   /wl4097
3204>   /wl4098
3204>   /wl4099
3204>   /wl4101
3204>   /wl4102
3204>   /wl4109
3204>   /wl4112
3204>   /wl4113
3204>   /wl4114
3204>   /wl4115
3204>   /wl4116
3204>   /wl4117
3204>   /wl4119
3204>   /wl4120
3204>   /wl4122
3204>   /wl4124
3204>   /wl4129
3204>   /wl4133
3204>   /wl4138
3204>   /wl4141
3204>   /wl4142
3204>   /wl4143
3204>   /wl4144
3204>   /wl4145
3204>   /wl4150
3204>   /wl4153
3204>   /wl4154
3204>   /wl4155
3204>   /wl4156
3204>   /wl4157
3204>   /wl4158
3204>   /wl4159
3204>   /wl4160
3204>   /wl4161
3204>   /wl4162
3204>   /wl4163
3204>   /wl4164
3204>   /wl4166
3204>   /wl4167
3204>   /wl4168
3204>   /wl4172
3204>   /wl4174
3204>   /wl4175
3204>   /wl4176
3204>   /wl4177
3204>   /wl4178
3204>   /wl4180
3204>   /wl4182
3204>   /wl4183
3204>   /wl4185
3204>   /wl4186
3204>   /wl4187
3204>   /wl4190
3204>   /wl4192
3204>   /wl4197
3204>   /wl4200
3204>   /wl4213
3204>   /wl4215
3204>   /wl4216
3204>   /wl4218
3204>   /wl4223
3204>   /wl4224
3204>   /wl4226
3204>   /wl4227
3204>   /wl4228
3204>   /wl4229
3204>   /wl4230
3204>   /wl4237
3204>   /wl4240
3204>   /wl4243
3204>   /wl4244
3204>   /wl4250
3204>   /wl4251
3204>   /wl4258
3204>   /wl4267
3204>   /wl4269
3204>   /wl4272
3204>   /wl4273
3204>   /wl4274
3204>   /wl4275
3204>   /wl4276
3204>   /wl4278
3204>   /wl4280
3204>   /wl4281
3204>   /wl4282
3204>   /wl4283
3204>   /wl4285
3204>   /wl4286
3204>   /wl4288
3204>   /wl4290
3204>   /wl4291
3204>   /wl4293
3204>   /wl4297
3204>   /wl4302
3204>   /wl4305
3204>   /wl4306
3204>   /wl4307
3204>   /wl4309
3204>   /wl4310
3204>   /wl4311
3204>   /wl4312
3204>   /wl4313
3204>   /wl4316
3204>   /wl4319
3204>   /wl4325
3204>   /wl4326
3204>   /wl4329
3204>   /wl4333
3204>   /wl4334
3204>   /wl4335
3204>   /wl4340
3204>   /wl4344
3204>   /wl4346
3204>   /wl4348
3204>   /wl4353
3204>   /wl4356
3204>   /wl4357
3204>   /wl4358
3204>   /wl4359
3204>   /wl4364
3204>   /wl4368
3204>   /wl4369
3204>   /wl4373
3204>   /wl4374
3204>   /wl4375
3204>   /wl4376
3204>   /wl4377
3204>   /wl4378
3204>   /wl4379
3204>   /wl4381
3204>   /wl4382
3204>   /wl4383
3204>   /wl4384
3204>   /wl4390
3204>   /wl4391
3204>   /wl4392
3204>   /wl4393
3204>   /wl4394
3204>   /wl4395
3204>   /wl4396
3204>   /wl4397
3204>   /wl4398
3204>   /wl4399
3204>   /wl4600
3204>   /wl4401
3204>   /wl4402
3204>   /wl4403
3204>   /wl4404
3204>   /wl4405
3204>   /wl4406
3204>   /wl4407
3204>   /wl4409
3204>   /wl4410
3204>   /wl4411
3204>   /wl4414
3204>   /wl4420
3204>   /wl4430
3204>   /wl4436
3204>   /wl4439
3204>   /wl4440
3204>   /wl4441
3204>   /wl4445
3204>   /wl4461
3204>   /wl4462
3204>   /wl4470
3204>   /wl4473
3204>   /wl4477
3204>   /wl4484
3204>   /wl4485
3204>   /wl4486
3204>   /wl4488
3204>   /wl4489
3204>   /wl4490
3204>   /wl4502
3204>   /wl4503
3204>   /wl4506
3204>   /wl4508
3204>   /wl4511
3204>   /wl4518
3204>   /wl4521
3204>   /wl4522
3204>   /wl4523
3204>   /wl4526
3204>   /wl4530
3204>   /wl4534
3204>   /wl4535
3204>   /wl4537
3204>   /wl4538
3204>   /wl4540
3204>   /wl4541
3204>   /wl4543
3204>   /wl4544
3204>   /wl4550
3204>   /wl4551
3204>   /wl4552
3204>   /wl4553
3204>   /wl4554
3204>   /wl4556
3204>   /wl4558
3204>   /wl4561
3204>   /wl4566
3204>   /wl4570
3204>   /wl4572
3204>   /wl4580
3204>   /wl4581
3204>   /wl4584
3204>   /wl4596
3204>   /wl4597
3204>   /wl4602
3204>   /wl4603
3204>   /wl4606
3204>   /wl4612
3204>   /wl4613
3204>   /wl4615
3204>   /wl4616
3204>   /wl4618
3204>   /wl4620
3204>   /wl4621
3204>   /wl4622
3204>   /wl4624
3204>   /wl4627
3204>   /wl4630
3204>   /wl4632
3204>   /wl4633
3204>   /wl4635
3204>   /wl4636
3204>   /wl4637
3204>   /wl4638
3204>   /wl4641
3204>   /wl4645
3204>   /wl4646
3204>   /wl4650
3204>   /wl4651
3204>   /wl4652
3204>   /wl4653
3204>   /wl4655
3204>   /wl4656
3204>   /wl4657
3204>   /wl4659
3204>   /wl4661
3204>   /wl4662
3204>   /wl4667
3204>   /wl4669
3204>   /wl4674
3204>   /wl4677
3204>   /wl4678
3204>   /wl4679
3204>   /wl4683
3204>   /wl4684
3204>   /wl4685
3204>   /wl4687
3204>   /wl4688
3204>   /wl4691
3204>   /wl4693
3204>   /wl4694
3204>   /wl4698
3204>   /wl4711
3204>   /wl4715
3204>   /wl4716
3204>   /wl4717
3204>   /wl4722
3204>   /wl4723
3204>   /wl4724
3204>   /wl4727
3204>   /wl4730
3204>   /wl4731
3204>   /wl4733
3204>   /wl4739
3204>   /wl4742
3204>   /wl4743
3204>   /wl4744
3204>   /wl4747
3204>   /wl4750
3204>   /wl4756
3204>   /wl4768
3204>   /wl4772
3204>   /wl4788
3204>   /wl4793
3204>   /wl4794
3204>   /wl4799
3204>   /wl4803
3204>   /wl4804
3204>   /wl4805
3204>   /wl4806
3204>   /wl4807
3204>   /wl4810
3204>   /wl4811
3204>   /wl4812
3204>   /wl4813
3204>   /wl4817
3204>   /wl4819
3204>   /wl4821
3204>   /wl4823
3204>   /wl4829
3204>   /wl4834
3204>   /wl4835
3204>   /wl4838
3204>   /wl4839
3204>   /wl4867
3204>   /wl4900
3204>   /wl4910
3204>   /wl4912
3204>   /wl4920
3204>   /wl4925
3204>   /wl4926
3204>   /wl4927
3204>   /wl4929
3204>   /wl4930
3204>   /wl4935
3204>   /wl4936
3204>   /wl4939
3204>   /wl4944
3204>   /wl4945
3204>   /wl4947
3204>   /wl4948
3204>   /wl4949
3204>   /wl4950
3204>   /wl4951
3204>   /wl4952
3204>   /wl4953
3204>   /wl4956
3204>   /wl4957
3204>   /wl4958
3204>   /wl4959
3204>   /wl4961
3204>   /wl4964
3204>   /wl4965
3204>   /wl4972
3204>   /wl4984
3204>   /wl4995
3204>   /wl4996
3204>   /wl4997
3204>   /wl4999
3204>   /wl5033
3204>   /wl5037
3204>   /wl5046
3204>   /wl5050
3204>   /wl5055
3204>   /wl5056
3204>   /wl5105
3204>   /wl5208
3204>   /d2Qvec-mathlib-
3204>   /d2Qvec-sse2only
3204>   /Gw
3204>   /Zc:checkGwOdr
3204>   /d1ignorePragmaWarningError
3204>   /wd4316
3204>   /wd4973
3204>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3204>   /d2FH4
3204>   /Brepro
3204>   -D_HAS_MAGIC_STATICS=1
3204>   /Qspectre
3204>   /wd5045
3204>   /d2guardspecanalysismode:v1_0
3204>   /d2guardspecmode2
3204>   /guard:cf
3204>   /d2guardcfgfuncptr-
3204>   /d2guardcfgdispatch
3204>   /guard:ehcont
3204>   -D__PLACEHOLDER_SAL=1
3204>   -wd4425
3204>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3204>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3204>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3204>   /std:c++17
3204>   .\main.cpp 
3204>main.cpp
3204> set _createfile=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\delayload.txt
3204>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Macros-PASS1.txt
3204>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Macros-PASS1.txt
3204> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3204>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller in pass PASS1
3208>Calculated LAYERINFO_MODULE='OneCoreDS'.
3208>makefile.def: TEMP=e:\os\obj\amd64fre\temp\d580aea3025d785793a2c72b43a9beb2
3208>makefile.def: BUILDINGINDATT=
3208>[Core OS Undocking] NOT using package ''
3208>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib' (target 'llmclientlib', type 'LIBRARY', nt_target_version '0xA000011')
3208>ObjectsMac.ts: validation succeeded
3208>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib" (STL_VER_TELEMETRY)
3208>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3208>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64 already exists.
3208> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\_PASS1_Marker.log
3208> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\d580aea3025d785793a2c72b43a9beb2\cl_1.rsp
3208>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3208>Copyright (C) Microsoft Corporation.  All rights reserved.
3208>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64/"
3208>   /FC
3208>   /Iamd64
3208>   /I.
3208>   /Ie:\os\src\data\MSRC
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3208>   /Ie:\os\public\amd64fre\onecoreuap\restricted\windows\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3208>   /I..\common
3208>   /I..\common\nlohmann-json\include
3208>   /I..\common\hnswlib
3208>   /I..\aimxsrv\inc
3208>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\..\aimxsrv\idl\objfre\amd64
3208>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64
3208>   /Ie:\os\src\onecore\ds\inc
3208>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3208>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3208>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3208>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3208>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3208>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3208>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3208>   /Ie:\os\public\amd64fre\shared\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3208>   /Ie:\os\public\amd64fre\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3208>   /Ie:\os\public\amd64fre\shared\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3208>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3208>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3208>   /Ie:\os\public\amd64fre\internal\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3208>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3208>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3208>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3208>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3208>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3208>   /D_WIN64
3208>   /D_AMD64_
3208>   /DAMD64
3208>   /DCONDITION_HANDLING=1
3208>   /DNT_INST=0
3208>   /DWIN32=100
3208>   /D_NT1X_=100
3208>   /DWINNT=1
3208>   /D_WIN32_WINNT=0x0A00
3208>   /DWINVER=0x0A00
3208>   /D_WIN32_IE=0x0A00
3208>   /DWIN32_LEAN_AND_MEAN=1
3208>   /DDEVL=1
3208>   /DNDEBUG
3208>   /D_STL120_
3208>   /D_STL140_
3208>   /D_DLL=1
3208>   /D_MT=1
3208>   -DNT_IUM
3208>   -D_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR
3208>   -DUNICODE
3208>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3208>   /D_USE_DEV11_CRT
3208>   -D_APISET_MINWIN_VERSION=0x0115
3208>   -D_APISET_MINCORE_VERSION=0x0114
3208>   /DFE_SB
3208>   /DFE_IME
3208>   /DNTDDI_VERSION=0x0A000011
3208>   /DWINBLUE_KBSPRING14
3208>   /DBUILD_WINDOWS
3208>   /DUNDOCKED_WINDOWS_UCRT
3208>   /D__WRL_CONFIGURATION_LEGACY__
3208>   /DBUILD_UMS_ENABLED=1
3208>   /DBUILD_WOW64_ENABLED=1
3208>   /DBUILD_ARM64X_ENABLED=0
3208>   /DEXECUTABLE_WRITES_SUPPORT=0
3208>   -D_USE_DECLSPECS_FOR_SAL=1
3208>   /DRUN_WPP
3208>   -D__PLACEHOLDER_SAL=1
3208>   /c
3208>   /Zc:wchar_t-
3208>   /Zl
3208>   /Zp8
3208>   /Gy
3208>   /W4
3208>   /d1import_no_registry
3208>   /EHsc
3208>   /GR-
3208>   /GF
3208>   /GS
3208>   /Z7
3208>   /Oxs
3208>   /GL
3208>   /Z7
3208>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3208>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3208>   /w15043
3208>   /Zc:rvalueCast
3208>   -D_UCRT
3208>   -D_CONST_RETURN=
3208>   -D_CRT_SECURE_NO_WARNINGS
3208>   -D_CRT_NON_CONFORMING_SWPRINTFS
3208>   -D_CRT_NONSTDC_NO_WARNINGS
3208>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3208>   /D_CRT_STDIO_INLINE=extern
3208>   /D_NO_CRT_STDIO_INLINE
3208>   /D_ACRTIMP_ALT=
3208>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3208>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3208>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3208>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3208>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3208>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3208>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3208>   /D_HAS_STD_BYTE=0
3208>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3208>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3208>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3208>   /D_FULL_IOBUF
3208>   /d1initAll:Mask11
3208>   /d1initAll:FillPattern0
3208>   /d1nodatetime
3208>   /d1trimfile:e:\os\src\=BASEDIR
3208>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3208>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3208>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3208>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3208>   /d2AllowCompatibleILVersions
3208>   /d2Zi+
3208>   /ZH:SHA_256
3208>   /wd4986
3208>   /wd4987
3208>   /wd4471
3208>   /wd4369
3208>   /wd4309
3208>   /wd4754
3208>   /wd4427
3208>   /d2DeepThoughtInliner-
3208>   /d2implyavx512upperregs-
3208>   /Wv:19.23
3208>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\
3208>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3208>   /wl4002
3208>   /wl4003
3208>   /wl4005
3208>   /wl4006
3208>   /wl4007
3208>   /wl4008
3208>   /wl4010
3208>   /wl4013
3208>   /wl4015
3208>   /wl4018
3208>   /wl4020
3208>   /wl4022
3208>   /wl4024
3208>   /wl4025
3208>   /wl4026
3208>   /wl4027
3208>   /wl4028
3208>   /wl4029
3208>   /wl4030
3208>   /wl4031
3208>   /wl4033
3208>   /wl4034
3208>   /wl4036
3208>   /wl4038
3208>   /wl4041
3208>   /wl4042
3208>   /wl4045
3208>   /wl4047
3208>   /wl4048
3208>   /wl4049
3208>   /wl4056
3208>   /wl4066
3208>   /wl4067
3208>   /wl4068
3208>   /wl4073
3208>   /wl4074
3208>   /wl4075
3208>   /wl4076
3208>   /wl4077
3208>   /wl4079
3208>   /wl4080
3208>   /wl4081
3208>   /wl4083
3208>   /wl4085
3208>   /wl4086
3208>   /wl4087
3208>   /wl4088
3208>   /wl4089
3208>   /wl4090
3208>   /wl4091
3208>   /wl4094
3208>   /wl4096
3208>   /wl4097
3208>   /wl4098
3208>   /wl4099
3208>   /wl4101
3208>   /wl4102
3208>   /wl4109
3208>   /wl4112
3208>   /wl4113
3208>   /wl4114
3208>   /wl4115
3208>   /wl4116
3208>   /wl4117
3208>   /wl4119
3208>   /wl4120
3208>   /wl4122
3208>   /wl4124
3208>   /wl4129
3208>   /wl4133
3208>   /wl4138
3208>   /wl4141
3208>   /wl4142
3208>   /wl4143
3208>   /wl4144
3208>   /wl4145
3208>   /wl4150
3208>   /wl4153
3208>   /wl4154
3208>   /wl4155
3208>   /wl4156
3208>   /wl4157
3208>   /wl4158
3208>   /wl4159
3208>   /wl4160
3208>   /wl4161
3208>   /wl4162
3208>   /wl4163
3208>   /wl4164
3208>   /wl4166
3208>   /wl4167
3208>   /wl4168
3208>   /wl4172
3208>   /wl4174
3208>   /wl4175
3208>   /wl4176
3208>   /wl4177
3208>   /wl4178
3208>   /wl4180
3208>   /wl4182
3208>   /wl4183
3208>   /wl4185
3208>   /wl4186
3208>   /wl4187
3208>   /wl4190
3208>   /wl4192
3208>   /wl4197
3208>   /wl4200
3208>   /wl4213
3208>   /wl4215
3208>   /wl4216
3208>   /wl4218
3208>   /wl4223
3208>   /wl4224
3208>   /wl4226
3208>   /wl4227
3208>   /wl4228
3208>   /wl4229
3208>   /wl4230
3208>   /wl4237
3208>   /wl4240
3208>   /wl4243
3208>   /wl4244
3208>   /wl4250
3208>   /wl4251
3208>   /wl4258
3208>   /wl4267
3208>   /wl4269
3208>   /wl4272
3208>   /wl4273
3208>   /wl4274
3208>   /wl4275
3208>   /wl4276
3208>   /wl4278
3208>   /wl4280
3208>   /wl4281
3208>   /wl4282
3208>   /wl4283
3208>   /wl4285
3208>   /wl4286
3208>   /wl4288
3208>   /wl4290
3208>   /wl4291
3208>   /wl4293
3208>   /wl4297
3208>   /wl4302
3208>   /wl4305
3208>   /wl4306
3208>   /wl4307
3208>   /wl4309
3208>   /wl4310
3208>   /wl4311
3208>   /wl4312
3208>   /wl4313
3208>   /wl4316
3208>   /wl4319
3208>   /wl4325
3208>   /wl4326
3208>   /wl4329
3208>   /wl4333
3208>   /wl4334
3208>   /wl4335
3208>   /wl4340
3208>   /wl4344
3208>   /wl4346
3208>   /wl4348
3208>   /wl4353
3208>   /wl4356
3208>   /wl4357
3208>   /wl4358
3208>   /wl4359
3208>   /wl4364
3208>   /wl4368
3208>   /wl4369
3208>   /wl4373
3208>   /wl4374
3208>   /wl4375
3208>   /wl4376
3208>   /wl4377
3208>   /wl4378
3208>   /wl4379
3208>   /wl4381
3208>   /wl4382
3208>   /wl4383
3208>   /wl4384
3208>   /wl4390
3208>   /wl4391
3208>   /wl4392
3208>   /wl4393
3208>   /wl4394
3208>   /wl4395
3208>   /wl4396
3208>   /wl4397
3208>   /wl4398
3208>   /wl4399
3208>   /wl4600
3208>   /wl4401
3208>   /wl4402
3208>   /wl4403
3208>   /wl4404
3208>   /wl4405
3208>   /wl4406
3208>   /wl4407
3208>   /wl4409
3208>   /wl4410
3208>   /wl4411
3208>   /wl4414
3208>   /wl4420
3208>   /wl4430
3208>   /wl4436
3208>   /wl4439
3208>   /wl4440
3208>   /wl4441
3208>   /wl4445
3208>   /wl4461
3208>   /wl4462
3208>   /wl4470
3208>   /wl4473
3208>   /wl4477
3208>   /wl4484
3208>   /wl4485
3208>   /wl4486
3208>   /wl4488
3208>   /wl4489
3208>   /wl4490
3208>   /wl4502
3208>   /wl4503
3208>   /wl4506
3208>   /wl4508
3208>   /wl4511
3208>   /wl4518
3208>   /wl4521
3208>   /wl4522
3208>   /wl4523
3208>   /wl4526
3208>   /wl4530
3208>   /wl4534
3208>   /wl4535
3208>   /wl4537
3208>   /wl4538
3208>   /wl4540
3208>   /wl4541
3208>   /wl4543
3208>   /wl4544
3208>   /wl4550
3208>   /wl4551
3208>   /wl4552
3208>   /wl4553
3208>   /wl4554
3208>   /wl4556
3208>   /wl4558
3208>   /wl4561
3208>   /wl4566
3208>   /wl4570
3208>   /wl4572
3208>   /wl4580
3208>   /wl4581
3208>   /wl4584
3208>   /wl4596
3208>   /wl4597
3208>   /wl4602
3208>   /wl4603
3208>   /wl4606
3208>   /wl4612
3208>   /wl4613
3208>   /wl4615
3208>   /wl4616
3208>   /wl4618
3208>   /wl4620
3208>   /wl4621
3208>   /wl4622
3208>   /wl4624
3208>   /wl4627
3208>   /wl4630
3208>   /wl4632
3208>   /wl4633
3208>   /wl4635
3208>   /wl4636
3208>   /wl4637
3208>   /wl4638
3208>   /wl4641
3208>   /wl4645
3208>   /wl4646
3208>   /wl4650
3208>   /wl4651
3208>   /wl4652
3208>   /wl4653
3208>   /wl4655
3208>   /wl4656
3208>   /wl4657
3208>   /wl4659
3208>   /wl4661
3208>   /wl4662
3208>   /wl4667
3208>   /wl4669
3208>   /wl4674
3208>   /wl4677
3208>   /wl4678
3208>   /wl4679
3208>   /wl4683
3208>   /wl4684
3208>   /wl4685
3208>   /wl4687
3208>   /wl4688
3208>   /wl4691
3208>   /wl4693
3208>   /wl4694
3208>   /wl4698
3208>   /wl4711
3208>   /wl4715
3208>   /wl4716
3208>   /wl4717
3208>   /wl4722
3208>   /wl4723
3208>   /wl4724
3208>   /wl4727
3208>   /wl4730
3208>   /wl4731
3208>   /wl4733
3208>   /wl4739
3208>   /wl4742
3208>   /wl4743
3208>   /wl4744
3208>   /wl4747
3208>   /wl4750
3208>   /wl4756
3208>   /wl4768
3208>   /wl4772
3208>   /wl4788
3208>   /wl4793
3208>   /wl4794
3208>   /wl4799
3208>   /wl4803
3208>   /wl4804
3208>   /wl4805
3208>   /wl4806
3208>   /wl4807
3208>   /wl4810
3208>   /wl4811
3208>   /wl4812
3208>   /wl4813
3208>   /wl4817
3208>   /wl4819
3208>   /wl4821
3208>   /wl4823
3208>   /wl4829
3208>   /wl4834
3208>   /wl4835
3208>   /wl4838
3208>   /wl4839
3208>   /wl4867
3208>   /wl4900
3208>   /wl4910
3208>   /wl4912
3208>   /wl4920
3208>   /wl4925
3208>   /wl4926
3208>   /wl4927
3208>   /wl4929
3208>   /wl4930
3208>   /wl4935
3208>   /wl4936
3208>   /wl4939
3208>   /wl4944
3208>   /wl4945
3208>   /wl4947
3208>   /wl4948
3208>   /wl4949
3208>   /wl4950
3208>   /wl4951
3208>   /wl4952
3208>   /wl4953
3208>   /wl4956
3208>   /wl4957
3208>   /wl4958
3208>   /wl4959
3208>   /wl4961
3208>   /wl4964
3208>   /wl4965
3208>   /wl4972
3208>   /wl4984
3208>   /wl4995
3208>   /wl4996
3208>   /wl4997
3208>   /wl4999
3208>   /wl5033
3208>   /wl5037
3208>   /wl5046
3208>   /wl5050
3208>   /wl5055
3208>   /wl5056
3208>   /wl5105
3208>   /wl5208
3208>   /d2Qvec-mathlib-
3208>   /d2Qvec-sse2only
3208>   /Gw
3208>   /Zc:checkGwOdr
3208>   /d1ignorePragmaWarningError
3208>   /wd4316
3208>   /wd4973
3208>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3208>   /d2FH4
3208>   /Brepro
3208>   -D_HAS_MAGIC_STATICS=1
3208>   /Qspectre
3208>   /wd5045
3208>   /d2guardspecanalysismode:v1_0
3208>   /d2guardspecmode2
3208>   /guard:cf
3208>   /d2guardcfgfuncptr-
3208>   /d2guardcfgdispatch
3208>   /guard:ehcont
3208>   -D__PLACEHOLDER_SAL=1
3208>   -wd4425
3208>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3208>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3208>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3208>   /std:c++17
3208>   .\llmclientlib.cpp 
3208>llmclientlib.cpp
3208> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\llmclientlib.lib /IGNORE:4078,4221,4281,4006,4198   /nodefaultlib /machine:amd64 /ltcg /Brepro @e:\os\obj\amd64fre\temp\d580aea3025d785793a2c72b43a9beb2\lib_1.rsp
3208>Microsoft (R) Library Manager Version 14.42.34444.100
3208>Copyright (C) Microsoft Corporation.  All rights reserved.
3208>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\llmclientlib.obj 
3208>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\Macros-PASS1.txt
3208>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\Macros-PASS1.txt
3208> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\llmclientlib" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3208>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\llmclientlib in pass PASS1
3206>Calculated LAYERINFO_MODULE='OneCoreDS'.
3206>makefile.def: TEMP=e:\os\obj\amd64fre\temp\41e735da71e2b5b691e2e28edd7b6ecf
3206>makefile.def: BUILDINGINDATT=
3206>[Core OS Undocking] NOT using package ''
3206>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib' (target 'HelloMcpServer', type 'LIBRARY', nt_target_version '0xA000011')
3206>ObjectsMac.ts: validation succeeded
3206>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib" (STL_VER_TELEMETRY)
3206>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3206>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64 already exists.
3206> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64\_PASS1_Marker.log
3206> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\41e735da71e2b5b691e2e28edd7b6ecf\cl_1.rsp
3206>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3206>Copyright (C) Microsoft Corporation.  All rights reserved.
3206>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64/"
3206>   /FC
3206>   /Iamd64
3206>   /I.
3206>   /Ie:\os\src\data\MSRC
3206>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3206>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3206>   /Ie:\os\public\amd64fre\onecoreuap\restricted\windows\inc
3206>   /I..\..\common
3206>   /I..\..\llmclientlib
3206>   /I..\..\aimxsrv\inc
3206>   /I..\..\aimxsrv\server
3206>   /I..\..\aimxsrv\client
3206>   /I..\..\McpProtocolLib
3206>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\..\..\aimxsrv\idl\objfre\amd64
3206>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3206>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64
3206>   /Ie:\os\src\onecore\ds\inc
3206>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3206>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3206>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3206>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3206>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3206>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3206>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3206>   /Ie:\os\public\amd64fre\shared\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3206>   /Ie:\os\public\amd64fre\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3206>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3206>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3206>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3206>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3206>   /Ie:\os\public\amd64fre\shared\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3206>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3206>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3206>   /Ie:\os\public\amd64fre\internal\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3206>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3206>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3206>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3206>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3206>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3206>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3206>   /D_WIN64
3206>   /D_AMD64_
3206>   /DAMD64
3206>   /DCONDITION_HANDLING=1
3206>   /DNT_INST=0
3206>   /DWIN32=100
3206>   /D_NT1X_=100
3206>   /DWINNT=1
3206>   /D_WIN32_WINNT=0x0A00
3206>   /DWINVER=0x0A00
3206>   /D_WIN32_IE=0x0A00
3206>   /DWIN32_LEAN_AND_MEAN=1
3206>   /DDEVL=1
3206>   /DNDEBUG
3206>   /D_STL120_
3206>   /D_STL140_
3206>   /D_DLL=1
3206>   /D_MT=1
3206>   -DNT_IUM
3206>   -DWIN32
3206>   -D_WIN32
3206>   -DUNICODE
3206>   -D_UNICODE
3206>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3206>   /D_USE_DEV11_CRT
3206>   -D_APISET_MINWIN_VERSION=0x0115
3206>   -D_APISET_MINCORE_VERSION=0x0114
3206>   /DFE_SB
3206>   /DFE_IME
3206>   /DNTDDI_VERSION=0x0A000011
3206>   /DWINBLUE_KBSPRING14
3206>   /DBUILD_WINDOWS
3206>   /DUNDOCKED_WINDOWS_UCRT
3206>   /D__WRL_CONFIGURATION_LEGACY__
3206>   /DBUILD_UMS_ENABLED=1
3206>   /DBUILD_WOW64_ENABLED=1
3206>   /DBUILD_ARM64X_ENABLED=0
3206>   /DEXECUTABLE_WRITES_SUPPORT=0
3206>   -D_USE_DECLSPECS_FOR_SAL=1
3206>   /DRUN_WPP
3206>   -D__PLACEHOLDER_SAL=1
3206>   /c
3206>   /Zc:wchar_t-
3206>   /Zl
3206>   /Zp8
3206>   /Gy
3206>   /W4
3206>   /wd4244
3206>   /EHsc
3206>   /d1import_no_registry
3206>   /EHsc
3206>   /GR-
3206>   /GF
3206>   /GS
3206>   /Z7
3206>   /Oxs
3206>   /GL
3206>   /Z7
3206>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3206>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3206>   /w15043
3206>   /Zc:rvalueCast
3206>   -D_UCRT
3206>   -D_CONST_RETURN=
3206>   -D_CRT_SECURE_NO_WARNINGS
3206>   -D_CRT_NON_CONFORMING_SWPRINTFS
3206>   -D_CRT_NONSTDC_NO_WARNINGS
3206>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3206>   /D_CRT_STDIO_INLINE=extern
3206>   /D_NO_CRT_STDIO_INLINE
3206>   /D_ACRTIMP_ALT=
3206>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3206>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3206>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3206>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3206>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3206>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3206>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3206>   /D_HAS_STD_BYTE=0
3206>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3206>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3206>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3206>   /D_FULL_IOBUF
3206>   /d1initAll:Mask11
3206>   /d1initAll:FillPattern0
3206>   /d1nodatetime
3206>   /d1trimfile:e:\os\src\=BASEDIR
3206>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3206>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3206>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3206>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3206>   /d2AllowCompatibleILVersions
3206>   /d2Zi+
3206>   /ZH:SHA_256
3206>   /wd4986
3206>   /wd4987
3206>   /wd4471
3206>   /wd4369
3206>   /wd4309
3206>   /wd4754
3206>   /wd4427
3206>   /d2DeepThoughtInliner-
3206>   /d2implyavx512upperregs-
3206>   /Wv:19.23
3206>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64\
3206>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3206>   /wl4002
3206>   /wl4003
3206>   /wl4005
3206>   /wl4006
3206>   /wl4007
3206>   /wl4008
3206>   /wl4010
3206>   /wl4013
3206>   /wl4015
3206>   /wl4018
3206>   /wl4020
3206>   /wl4022
3206>   /wl4024
3206>   /wl4025
3206>   /wl4026
3206>   /wl4027
3206>   /wl4028
3206>   /wl4029
3206>   /wl4030
3206>   /wl4031
3206>   /wl4033
3206>   /wl4034
3206>   /wl4036
3206>   /wl4038
3206>   /wl4041
3206>   /wl4042
3206>   /wl4045
3206>   /wl4047
3206>   /wl4048
3206>   /wl4049
3206>   /wl4056
3206>   /wl4066
3206>   /wl4067
3206>   /wl4068
3206>   /wl4073
3206>   /wl4074
3206>   /wl4075
3206>   /wl4076
3206>   /wl4077
3206>   /wl4079
3206>   /wl4080
3206>   /wl4081
3206>   /wl4083
3206>   /wl4085
3206>   /wl4086
3206>   /wl4087
3206>   /wl4088
3206>   /wl4089
3206>   /wl4090
3206>   /wl4091
3206>   /wl4094
3206>   /wl4096
3206>   /wl4097
3206>   /wl4098
3206>   /wl4099
3206>   /wl4101
3206>   /wl4102
3206>   /wl4109
3206>   /wl4112
3206>   /wl4113
3206>   /wl4114
3206>   /wl4115
3206>   /wl4116
3206>   /wl4117
3206>   /wl4119
3206>   /wl4120
3206>   /wl4122
3206>   /wl4124
3206>   /wl4129
3206>   /wl4133
3206>   /wl4138
3206>   /wl4141
3206>   /wl4142
3206>   /wl4143
3206>   /wl4144
3206>   /wl4145
3206>   /wl4150
3206>   /wl4153
3206>   /wl4154
3206>   /wl4155
3206>   /wl4156
3206>   /wl4157
3206>   /wl4158
3206>   /wl4159
3206>   /wl4160
3206>   /wl4161
3206>   /wl4162
3206>   /wl4163
3206>   /wl4164
3206>   /wl4166
3206>   /wl4167
3206>   /wl4168
3206>   /wl4172
3206>   /wl4174
3206>   /wl4175
3206>   /wl4176
3206>   /wl4177
3206>   /wl4178
3206>   /wl4180
3206>   /wl4182
3206>   /wl4183
3206>   /wl4185
3206>   /wl4186
3206>   /wl4187
3206>   /wl4190
3206>   /wl4192
3206>   /wl4197
3206>   /wl4200
3206>   /wl4213
3206>   /wl4215
3206>   /wl4216
3206>   /wl4218
3206>   /wl4223
3206>   /wl4224
3206>   /wl4226
3206>   /wl4227
3206>   /wl4228
3206>   /wl4229
3206>   /wl4230
3206>   /wl4237
3206>   /wl4240
3206>   /wl4243
3206>   /wl4244
3206>   /wl4250
3206>   /wl4251
3206>   /wl4258
3206>   /wl4267
3206>   /wl4269
3206>   /wl4272
3206>   /wl4273
3206>   /wl4274
3206>   /wl4275
3206>   /wl4276
3206>   /wl4278
3206>   /wl4280
3206>   /wl4281
3206>   /wl4282
3206>   /wl4283
3206>   /wl4285
3206>   /wl4286
3206>   /wl4288
3206>   /wl4290
3206>   /wl4291
3206>   /wl4293
3206>   /wl4297
3206>   /wl4302
3206>   /wl4305
3206>   /wl4306
3206>   /wl4307
3206>   /wl4309
3206>   /wl4310
3206>   /wl4311
3206>   /wl4312
3206>   /wl4313
3206>   /wl4316
3206>   /wl4319
3206>   /wl4325
3206>   /wl4326
3206>   /wl4329
3206>   /wl4333
3206>   /wl4334
3206>   /wl4335
3206>   /wl4340
3206>   /wl4344
3206>   /wl4346
3206>   /wl4348
3206>   /wl4353
3206>   /wl4356
3206>   /wl4357
3206>   /wl4358
3206>   /wl4359
3206>   /wl4364
3206>   /wl4368
3206>   /wl4369
3206>   /wl4373
3206>   /wl4374
3206>   /wl4375
3206>   /wl4376
3206>   /wl4377
3206>   /wl4378
3206>   /wl4379
3206>   /wl4381
3206>   /wl4382
3206>   /wl4383
3206>   /wl4384
3206>   /wl4390
3206>   /wl4391
3206>   /wl4392
3206>   /wl4393
3206>   /wl4394
3206>   /wl4395
3206>   /wl4396
3206>   /wl4397
3206>   /wl4398
3206>   /wl4399
3206>   /wl4600
3206>   /wl4401
3206>   /wl4402
3206>   /wl4403
3206>   /wl4404
3206>   /wl4405
3206>   /wl4406
3206>   /wl4407
3206>   /wl4409
3206>   /wl4410
3206>   /wl4411
3206>   /wl4414
3206>   /wl4420
3206>   /wl4430
3206>   /wl4436
3206>   /wl4439
3206>   /wl4440
3206>   /wl4441
3206>   /wl4445
3206>   /wl4461
3206>   /wl4462
3206>   /wl4470
3206>   /wl4473
3206>   /wl4477
3206>   /wl4484
3206>   /wl4485
3206>   /wl4486
3206>   /wl4488
3206>   /wl4489
3206>   /wl4490
3206>   /wl4502
3206>   /wl4503
3206>   /wl4506
3206>   /wl4508
3206>   /wl4511
3206>   /wl4518
3206>   /wl4521
3206>   /wl4522
3206>   /wl4523
3206>   /wl4526
3206>   /wl4530
3206>   /wl4534
3206>   /wl4535
3206>   /wl4537
3206>   /wl4538
3206>   /wl4540
3206>   /wl4541
3206>   /wl4543
3206>   /wl4544
3206>   /wl4550
3206>   /wl4551
3206>   /wl4552
3206>   /wl4553
3206>   /wl4554
3206>   /wl4556
3206>   /wl4558
3206>   /wl4561
3206>   /wl4566
3206>   /wl4570
3206>   /wl4572
3206>   /wl4580
3206>   /wl4581
3206>   /wl4584
3206>   /wl4596
3206>   /wl4597
3206>   /wl4602
3206>   /wl4603
3206>   /wl4606
3206>   /wl4612
3206>   /wl4613
3206>   /wl4615
3206>   /wl4616
3206>   /wl4618
3206>   /wl4620
3206>   /wl4621
3206>   /wl4622
3206>   /wl4624
3206>   /wl4627
3206>   /wl4630
3206>   /wl4632
3206>   /wl4633
3206>   /wl4635
3206>   /wl4636
3206>   /wl4637
3206>   /wl4638
3206>   /wl4641
3206>   /wl4645
3206>   /wl4646
3206>   /wl4650
3206>   /wl4651
3206>   /wl4652
3206>   /wl4653
3206>   /wl4655
3206>   /wl4656
3206>   /wl4657
3206>   /wl4659
3206>   /wl4661
3206>   /wl4662
3206>   /wl4667
3206>   /wl4669
3206>   /wl4674
3206>   /wl4677
3206>   /wl4678
3206>   /wl4679
3206>   /wl4683
3206>   /wl4684
3206>   /wl4685
3206>   /wl4687
3206>   /wl4688
3206>   /wl4691
3206>   /wl4693
3206>   /wl4694
3206>   /wl4698
3206>   /wl4711
3206>   /wl4715
3206>   /wl4716
3206>   /wl4717
3206>   /wl4722
3206>   /wl4723
3206>   /wl4724
3206>   /wl4727
3206>   /wl4730
3206>   /wl4731
3206>   /wl4733
3206>   /wl4739
3206>   /wl4742
3206>   /wl4743
3206>   /wl4744
3206>   /wl4747
3206>   /wl4750
3206>   /wl4756
3206>   /wl4768
3206>   /wl4772
3206>   /wl4788
3206>   /wl4793
3206>   /wl4794
3206>   /wl4799
3206>   /wl4803
3206>   /wl4804
3206>   /wl4805
3206>   /wl4806
3206>   /wl4807
3206>   /wl4810
3206>   /wl4811
3206>   /wl4812
3206>   /wl4813
3206>   /wl4817
3206>   /wl4819
3206>   /wl4821
3206>   /wl4823
3206>   /wl4829
3206>   /wl4834
3206>   /wl4835
3206>   /wl4838
3206>   /wl4839
3206>   /wl4867
3206>   /wl4900
3206>   /wl4910
3206>   /wl4912
3206>   /wl4920
3206>   /wl4925
3206>   /wl4926
3206>   /wl4927
3206>   /wl4929
3206>   /wl4930
3206>   /wl4935
3206>   /wl4936
3206>   /wl4939
3206>   /wl4944
3206>   /wl4945
3206>   /wl4947
3206>   /wl4948
3206>   /wl4949
3206>   /wl4950
3206>   /wl4951
3206>   /wl4952
3206>   /wl4953
3206>   /wl4956
3206>   /wl4957
3206>   /wl4958
3206>   /wl4959
3206>   /wl4961
3206>   /wl4964
3206>   /wl4965
3206>   /wl4972
3206>   /wl4984
3206>   /wl4995
3206>   /wl4996
3206>   /wl4997
3206>   /wl4999
3206>   /wl5033
3206>   /wl5037
3206>   /wl5046
3206>   /wl5050
3206>   /wl5055
3206>   /wl5056
3206>   /wl5105
3206>   /wl5208
3206>   /d2Qvec-mathlib-
3206>   /d2Qvec-sse2only
3206>   /Gw
3206>   /Zc:checkGwOdr
3206>   /d1ignorePragmaWarningError
3206>   /wd4316
3206>   /wd4973
3206>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3206>   /d2FH4
3206>   /Brepro
3206>   -D_HAS_MAGIC_STATICS=1
3206>   /Qspectre
3206>   /wd5045
3206>   /d2guardspecanalysismode:v1_0
3206>   /d2guardspecmode2
3206>   /guard:cf
3206>   /d2guardcfgfuncptr-
3206>   /d2guardcfgdispatch
3206>   /guard:ehcont
3206>   -D__PLACEHOLDER_SAL=1
3206>   -wd4425
3206>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3206>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3206>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3206>   /std:c++17
3206>   ..\hellomcpserver.cpp 
3206>hellomcpserver.cpp
3206> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64\HelloMcpServer.lib /IGNORE:4078,4221,4281,4006,4198   /nodefaultlib /machine:amd64 /ltcg /Brepro @e:\os\obj\amd64fre\temp\41e735da71e2b5b691e2e28edd7b6ecf\lib_1.rsp
3206>Microsoft (R) Library Manager Version 14.42.34444.100
3206>Copyright (C) Microsoft Corporation.  All rights reserved.
3206>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64\hellomcpserver.obj 
3206>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64\Macros-PASS1.txt
3206>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64\Macros-PASS1.txt
3206> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\mcpserversample\lib" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3206>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\mcpserversample\lib in pass PASS1
3202>Calculated LAYERINFO_MODULE='OneCoreDS'.
3202>makefile.def: TEMP=e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770
3202>makefile.def: BUILDINGINDATT=
3202>[Core OS Undocking] NOT using package ''
3202>ObjectsMac.ts: validation succeeded
3202>Starting recursive call to NMAKE for _MAKING_ASMID_INC
3202>Starting _MAKING_ASMID_INC
3202>Calculated LAYERINFO_MODULE='OneCoreDS'.
3202>makefile.def: TEMP=e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770
3202>makefile.def: BUILDINGINDATT=
3202>[Core OS Undocking] NOT using package ''
3202>ObjectsMac.ts: _objects.mac is not needed; macro validation will be skipped.
3202>BUILDMSG: Checking if we need to generate coffbase.mac file
3202>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3202>Ending _MAKING_ASMID_INC
3202>'e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_asmid.inc' is up-to-date
3202>Finished recursively calling NMAKE for _MAKING_ASMID_INC
3202>BUILDMSG: Checking if we need to generate coffbase.mac file
3202>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3202>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64 already exists.
3202> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_PASS1_Marker.log
3202> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_generated.cs e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770\_generated.cs 2>nul
3202> set createfile=e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770\_generated.cs
3202> e:\os\src\tools\onecoreuap\internal\AsmIdToAttribs\asmIdToAttribs.cmd "aimxpsh,processorArchitecture=msil,version=10.0.0.0,publicKeyToken=31bf3856ad364e35" e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770\_generated.cs csharp
3202> c:\windows\system32\cmd.exe /c copy e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770\_generated.cs e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_generated.cs >nul
3202> e:\os\tools\dotnet-runtime-latest-win-x64\dotnet.exe --roll-forward LatestMajor e:\os\tools\dotnet-x64-latest-roslyn\bincore\csc.dll /deterministic /nologo /nostdlib /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\mscorlib.dll /noconfig /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll  
3202> /target:library 
3202> /baseaddress:0x10000000 
3202> /optimize+ 
3202> /debug:pdbonly 
3202> /warnaserror+ 
3202> /unsafe- 
3202> /checksumalgorithm:SHA256 
3202> /platform:anycpu 
3202> /nowarn:1699 
3202> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.metadata_dll 
3202> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Core.metadata_dll 
3202> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.DirectoryServices.metadata_dll 
3202> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.DirectoryServices.Protocols.metadata_dll 
3202> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.IO.metadata_dll 
3202> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Management.metadata_dll 
3202> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Runtime.metadata_dll 
3202> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Runtime.InteropServices.metadata_dll 
3202> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Runtime.Serialization.metadata_dll 
3202> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Runtime.Serialization.Json.metadata_dll 
3202> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\clr48\System.Xml.metadata_dll 
3202> /r:e:\os\public\amd64fre\onecore\internal\sdk\ref\System.Management.Automation.metadata_dll 
3202> NativeMethods.cs 
3202> AimxServerCmdLets.cs 
3202> FoundryLocalWizard.cs 
3202> e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_generated.cs
3202> c:\windows\system32\cmd.exe /c e:\os\src\tools\urtrun.cmd 4.Latest e:\os\tools\FixTSVersionStringAppend\bin\FixTsVersionStringAppend\release\FixTSVersionStringAppend.exe /fts:e:\os\tools\FixTS\FixTS.exe /pe=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll
3202>[e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll]
3202> Lang: 0 (0)
3202>Key FileVersion value changed to 10.0.27910.1000,0.0.65535.65535,0.0.10011.16384,0.0.65535.0,BUILDINFO
3202>Key ProductVersion value changed to 10.0.27910.1000,0.0.65535.65535,0.0.10011.16384,0.0.65535.0,BUILDINFO
3202>processing version resource language 0000 (0000)
3202> e:\os\tools\Windows.Desktop.Tools.amd64\tools\imagecfg.exe /q /l /e e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll
3202> e:\os\tools\deferredbinplace\DeferredBinplace.exe  e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell  TRUE  e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\binplace_PASS1.rsp  @e:\os\tools\binplace\binplace.exe  /R e:\os\bin\amd64fre\.  /s e:\os\bin\amd64fre\Symbols.pri\. /j /:DBG /:NOCV  -f -:LOGPDB /:CVTCIL /:SYMBAD e:\os\src\tools\symbad.txt   /:DEST retail      e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll
3202>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll
3202> c:\windows\system32\cmd.exe /c if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll.mui (  echo Build_Status  LN_MUI_STS: LGNSTS_MANAGED aimxpsh.dll  )
3202>Build_Status  LN_MUI_STS: LGNSTS_MANAGED aimxpsh.dll  
3202> set BUILDMSG=making aimxpsh.asmmeta_temp e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.dll
3202> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta_temp e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770\aimxpsh.asmmeta_temp 2>nul
3202> c:\windows\system32\cmd.exe /c copy e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770\aimxpsh.asmmeta_temp e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta_temp >nul
3202> set BUILDMSG=making aimxpsh.asmmeta
3202> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta asmmeta.cmd /CompareAndUpdateAsmmeta:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta_temp /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta /OBJ_PATH:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell /O:objfre\amd64
3202> c:\windows\system32\cmd.exe /c if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta.changed
3202> c:\windows\system32\cmd.exe /c if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta copy e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta_temp e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta
3202>        1 file(s) copied.
3202> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta_temp del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta_temp
3202> set BUILDMSG=making aimxpsh.metadata_dll in Managed.AsmMeta.nmake under _O_BINARY_METADATA target
3202> c:\windows\system32\cmd.exe /c "@if exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta.changed (e:\os\src\tools\ilasm.cmd /DET /nologo /quiet /dll /out:e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770\aimxpsh.metadata_dll e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta || @echo error : error ilasm error) else if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.metadata_dll (e:\os\src\tools\ilasm.cmd /DET /nologo /quiet /dll /out:e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770\aimxpsh.metadata_dll e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta || @echo error : error ilasm error)"
3202>e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell>e:\os\tools\ilasm\ilasm.exe /DET /nologo /quiet /dll /out:e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770\aimxpsh.metadata_dll e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta 
3202> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770\aimxpsh.metadata_dll c:\windows\system32\cmd.exe /c copy e:\os\obj\amd64fre\temp\773842eeb8d9da17f23f187763abd770\aimxpsh.metadata_dll e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.metadata_dll >nul
3202> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.asmmeta.changed
3202>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\Macros-PASS1.txt
3202>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\Macros-PASS1.txt
3202> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\powershell" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3202>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\powershell in pass PASS1
3201>Calculated LAYERINFO_MODULE='OneCoreDS'.
3201>makefile.def: TEMP=e:\os\obj\amd64fre\temp\00546f64bef0ebc1f7af97cc116bc5e6
3201>makefile.def: BUILDINGINDATT=
3201>[Core OS Undocking] NOT using package ''
3201>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib' (target 'aimxclient_s', type 'LIBRARY', nt_target_version '0xA000011')
3201>ObjectsMac.ts: validation succeeded
3201>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib" (STL_VER_TELEMETRY)
3201>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3201>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64 already exists.
3201> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\_PASS1_Marker.log
3201> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\00546f64bef0ebc1f7af97cc116bc5e6\tmp_16280_1753418469747893700.tmp /Tpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\pch_hdr.src
3201>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>cl 
3201>   /Iamd64
3201>   /I.
3201>   /Ie:\os\src\data\MSRC
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3201>   /I..\
3201>   /I..\..\..\common
3201>   /I..\..\..\common\nlohmann
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\..\..\idl\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\security\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3201>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\ntos
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3201>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3201>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3201>   /Ie:\os\public\amd64fre\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3201>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /D_WIN64
3201>   /D_AMD64_
3201>   /DAMD64
3201>   /DCONDITION_HANDLING=1
3201>   /DNT_INST=0
3201>   /DWIN32=100
3201>   /D_NT1X_=100
3201>   /DWINNT=1
3201>   /D_WIN32_WINNT=0x0A00
3201>   /DWINVER=0x0A00
3201>   /D_WIN32_IE=0x0A00
3201>   /DWIN32_LEAN_AND_MEAN=1
3201>   /DDEVL=1
3201>   /DNDEBUG
3201>   /D_STL120_
3201>   /D_STL140_
3201>   /D_DLL=1
3201>   /D_MT=1
3201>   -DNT_IUM
3201>   -DWIN32
3201>   -D_WIN32
3201>   -DUNICODE
3201>   -D_UNICODE
3201>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3201>   /D_USE_DEV11_CRT
3201>   -D_APISET_MINWIN_VERSION=0x0115
3201>   -D_APISET_MINCORE_VERSION=0x0114
3201>   /DFE_SB
3201>   /DFE_IME
3201>   /DNTDDI_VERSION=0x0A000011
3201>   /DWINBLUE_KBSPRING14
3201>   /DBUILD_WINDOWS
3201>   /DUNDOCKED_WINDOWS_UCRT
3201>   /D__WRL_CONFIGURATION_LEGACY__
3201>   /DBUILD_UMS_ENABLED=1
3201>   /DBUILD_WOW64_ENABLED=1
3201>   /DBUILD_ARM64X_ENABLED=0
3201>   /DEXECUTABLE_WRITES_SUPPORT=0
3201>   -D_USE_DECLSPECS_FOR_SAL=1
3201>   /DRUN_WPP
3201>   -D__PLACEHOLDER_SAL=1
3201>   /D_ATL_STATIC_REGISTRY
3201>   /c
3201>   /Zc:wchar_t-
3201>   /Zl
3201>   /Zp8
3201>   /Gy
3201>   /W4
3201>   /d1import_no_registry
3201>   /EHsc
3201>   /GR-
3201>   /GF
3201>   /GS
3201>   /Z7
3201>   /Oxs
3201>   /GL
3201>   /Z7
3201>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3201>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3201>   /w15043
3201>   /Zc:rvalueCast
3201>   /Zo
3201>   -D_UCRT
3201>   -D_CONST_RETURN=
3201>   -D_CRT_SECURE_NO_WARNINGS
3201>   -D_CRT_NON_CONFORMING_SWPRINTFS
3201>   -D_CRT_NONSTDC_NO_WARNINGS
3201>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3201>   /D_CRT_STDIO_INLINE=extern
3201>   /D_NO_CRT_STDIO_INLINE
3201>   /D_ACRTIMP_ALT=
3201>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3201>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3201>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3201>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3201>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_HAS_STD_BYTE=0
3201>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_FULL_IOBUF
3201>   /d1initAll:Mask11
3201>   /d1initAll:FillPattern0
3201>   /d1nodatetime
3201>   /d1trimfile:e:\os\src\=BASEDIR
3201>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3201>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3201>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3201>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3201>   /d2AllowCompatibleILVersions
3201>   /d2Zi+
3201>   /ZH:SHA_256
3201>   /wd4986
3201>   /wd4987
3201>   /wd4471
3201>   /wd4369
3201>   /wd4309
3201>   /wd4754
3201>   /wd4427
3201>   /d2DeepThoughtInliner-
3201>   /d2implyavx512upperregs-
3201>   /Wv:19.23
3201>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\
3201>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3201>   /wl4002
3201>   /wl4003
3201>   /wl4005
3201>   /wl4006
3201>   /wl4007
3201>   /wl4008
3201>   /wl4010
3201>   /wl4013
3201>   /wl4015
3201>   /wl4018
3201>   /wl4020
3201>   /wl4022
3201>   /wl4024
3201>   /wl4025
3201>   /wl4026
3201>   /wl4027
3201>   /wl4028
3201>   /wl4029
3201>   /wl4030
3201>   /wl4031
3201>   /wl4033
3201>   /wl4034
3201>   /wl4036
3201>   /wl4038
3201>   /wl4041
3201>   /wl4042
3201>   /wl4045
3201>   /wl4047
3201>   /wl4048
3201>   /wl4049
3201>   /wl4056
3201>   /wl4066
3201>   /wl4067
3201>   /wl4068
3201>   /wl4073
3201>   /wl4074
3201>   /wl4075
3201>   /wl4076
3201>   /wl4077
3201>   /wl4079
3201>   /wl4080
3201>   /wl4081
3201>   /wl4083
3201>   /wl4085
3201>   /wl4086
3201>   /wl4087
3201>   /wl4088
3201>   /wl4089
3201>   /wl4090
3201>   /wl4091
3201>   /wl4094
3201>   /wl4096
3201>   /wl4097
3201>   /wl4098
3201>   /wl4099
3201>   /wl4101
3201>   /wl4102
3201>   /wl4109
3201>   /wl4112
3201>   /wl4113
3201>   /wl4114
3201>   /wl4115
3201>   /wl4116
3201>   /wl4117
3201>   /wl4119
3201>   /wl4120
3201>   /wl4122
3201>   /wl4124
3201>   /wl4129
3201>   /wl4133
3201>   /wl4138
3201>   /wl4141
3201>   /wl4142
3201>   /wl4143
3201>   /wl4144
3201>   /wl4145
3201>   /wl4150
3201>   /wl4153
3201>   /wl4154
3201>   /wl4155
3201>   /wl4156
3201>   /wl4157
3201>   /wl4158
3201>   /wl4159
3201>   /wl4160
3201>   /wl4161
3201>   /wl4162
3201>   /wl4163
3201>   /wl4164
3201>   /wl4166
3201>   /wl4167
3201>   /wl4168
3201>   /wl4172
3201>   /wl4174
3201>   /wl4175
3201>   /wl4176
3201>   /wl4177
3201>   /wl4178
3201>   /wl4180
3201>   /wl4182
3201>   /wl4183
3201>   /wl4185
3201>   /wl4186
3201>   /wl4187
3201>   /wl4190
3201>   /wl4192
3201>   /wl4197
3201>   /wl4200
3201>   /wl4213
3201>   /wl4215
3201>   /wl4216
3201>   /wl4218
3201>   /wl4223
3201>   /wl4224
3201>   /wl4226
3201>   /wl4227
3201>   /wl4228
3201>   /wl4229
3201>   /wl4230
3201>   /wl4237
3201>   /wl4240
3201>   /wl4243
3201>   /wl4244
3201>   /wl4250
3201>   /wl4251
3201>   /wl4258
3201>   /wl4267
3201>   /wl4269
3201>   /wl4272
3201>   /wl4273
3201>   /wl4274
3201>   /wl4275
3201>   /wl4276
3201>   /wl4278
3201>   /wl4280
3201>   /wl4281
3201>   /wl4282
3201>   /wl4283
3201>   /wl4285
3201>   /wl4286
3201>   /wl4288
3201>   /wl4290
3201>   /wl4291
3201>   /wl4293
3201>   /wl4297
3201>   /wl4302
3201>   /wl4305
3201>   /wl4306
3201>   /wl4307
3201>   /wl4309
3201>   /wl4310
3201>   /wl4311
3201>   /wl4312
3201>   /wl4313
3201>   /wl4316
3201>   /wl4319
3201>   /wl4325
3201>   /wl4326
3201>   /wl4329
3201>   /wl4333
3201>   /wl4334
3201>   /wl4335
3201>   /wl4340
3201>   /wl4344
3201>   /wl4346
3201>   /wl4348
3201>   /wl4353
3201>   /wl4356
3201>   /wl4357
3201>   /wl4358
3201>   /wl4359
3201>   /wl4364
3201>   /wl4368
3201>   /wl4369
3201>   /wl4373
3201>   /wl4374
3201>   /wl4375
3201>   /wl4376
3201>   /wl4377
3201>   /wl4378
3201>   /wl4379
3201>   /wl4381
3201>   /wl4382
3201>   /wl4383
3201>   /wl4384
3201>   /wl4390
3201>   /wl4391
3201>   /wl4392
3201>   /wl4393
3201>   /wl4394
3201>   /wl4395
3201>   /wl4396
3201>   /wl4397
3201>   /wl4398
3201>   /wl4399
3201>   /wl4600
3201>   /wl4401
3201>   /wl4402
3201>   /wl4403
3201>   /wl4404
3201>   /wl4405
3201>   /wl4406
3201>   /wl4407
3201>   /wl4409
3201>   /wl4410
3201>   /wl4411
3201>   /wl4414
3201>   /wl4420
3201>   /wl4430
3201>   /wl4436
3201>   /wl4439
3201>   /wl4440
3201>   /wl4441
3201>   /wl4445
3201>   /wl4461
3201>   /wl4462
3201>   /wl4470
3201>   /wl4473
3201>   /wl4477
3201>   /wl4484
3201>   /wl4485
3201>   /wl4486
3201>   /wl4488
3201>   /wl4489
3201>   /wl4490
3201>   /wl4502
3201>   /wl4503
3201>   /wl4506
3201>   /wl4508
3201>   /wl4511
3201>   /wl4518
3201>   /wl4521
3201>   /wl4522
3201>   /wl4523
3201>   /wl4526
3201>   /wl4530
3201>   /wl4534
3201>   /wl4535
3201>   /wl4537
3201>   /wl4538
3201>   /wl4540
3201>   /wl4541
3201>   /wl4543
3201>   /wl4544
3201>   /wl4550
3201>   /wl4551
3201>   /wl4552
3201>   /wl4553
3201>   /wl4554
3201>   /wl4556
3201>   /wl4558
3201>   /wl4561
3201>   /wl4566
3201>   /wl4570
3201>   /wl4572
3201>   /wl4580
3201>   /wl4581
3201>   /wl4584
3201>   /wl4596
3201>   /wl4597
3201>   /wl4602
3201>   /wl4603
3201>   /wl4606
3201>   /wl4612
3201>   /wl4613
3201>   /wl4615
3201>   /wl4616
3201>   /wl4618
3201>   /wl4620
3201>   /wl4621
3201>   /wl4622
3201>   /wl4624
3201>   /wl4627
3201>   /wl4630
3201>   /wl4632
3201>   /wl4633
3201>   /wl4635
3201>   /wl4636
3201>   /wl4637
3201>   /wl4638
3201>   /wl4641
3201>   /wl4645
3201>   /wl4646
3201>   /wl4650
3201>   /wl4651
3201>   /wl4652
3201>   /wl4653
3201>   /wl4655
3201>   /wl4656
3201>   /wl4657
3201>   /wl4659
3201>   /wl4661
3201>   /wl4662
3201>   /wl4667
3201>   /wl4669
3201>   /wl4674
3201>   /wl4677
3201>   /wl4678
3201>   /wl4679
3201>   /wl4683
3201>   /wl4684
3201>   /wl4685
3201>   /wl4687
3201>   /wl4688
3201>   /wl4691
3201>   /wl4693
3201>   /wl4694
3201>   /wl4698
3201>   /wl4711
3201>   /wl4715
3201>   /wl4716
3201>   /wl4717
3201>   /wl4722
3201>   /wl4723
3201>   /wl4724
3201>   /wl4727
3201>   /wl4730
3201>   /wl4731
3201>   /wl4733
3201>   /wl4739
3201>   /wl4742
3201>   /wl4743
3201>   /wl4744
3201>   /wl4747
3201>   /wl4750
3201>   /wl4756
3201>   /wl4768
3201>   /wl4772
3201>   /wl4788
3201>   /wl4793
3201>   /wl4794
3201>   /wl4799
3201>   /wl4803
3201>   /wl4804
3201>   /wl4805
3201>   /wl4806
3201>   /wl4807
3201>   /wl4810
3201>   /wl4811
3201>   /wl4812
3201>   /wl4813
3201>   /wl4817
3201>   /wl4819
3201>   /wl4821
3201>   /wl4823
3201>   /wl4829
3201>   /wl4834
3201>   /wl4835
3201>   /wl4838
3201>   /wl4839
3201>   /wl4867
3201>   /wl4900
3201>   /wl4910
3201>   /wl4912
3201>   /wl4920
3201>   /wl4925
3201>   /wl4926
3201>   /wl4927
3201>   /wl4929
3201>   /wl4930
3201>   /wl4935
3201>   /wl4936
3201>   /wl4939
3201>   /wl4944
3201>   /wl4945
3201>   /wl4947
3201>   /wl4948
3201>   /wl4949
3201>   /wl4950
3201>   /wl4951
3201>   /wl4952
3201>   /wl4953
3201>   /wl4956
3201>   /wl4957
3201>   /wl4958
3201>   /wl4959
3201>   /wl4961
3201>   /wl4964
3201>   /wl4965
3201>   /wl4972
3201>   /wl4984
3201>   /wl4995
3201>   /wl4996
3201>   /wl4997
3201>   /wl4999
3201>   /wl5033
3201>   /wl5037
3201>   /wl5046
3201>   /wl5050
3201>   /wl5055
3201>   /wl5056
3201>   /wl5105
3201>   /wl5208
3201>   /d2Qvec-mathlib-
3201>   /d2Qvec-sse2only
3201>   /Gw
3201>   /Zc:checkGwOdr
3201>   /d1ignorePragmaWarningError
3201>   /wd4316
3201>   /wd4973
3201>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3201>   /d2FH4
3201>   /Brepro
3201>   -D_HAS_MAGIC_STATICS=1
3201>   /Qspectre
3201>   /wd5045
3201>   /d2guardspecanalysismode:v1_0
3201>   /d2guardspecmode2
3201>   /guard:cf
3201>   /d2guardcfgfuncptr-
3201>   /d2guardcfgdispatch
3201>   /guard:ehcont
3201>   -D__PLACEHOLDER_SAL=1
3201>   -wd4425
3201>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3201>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3201>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3201>   /std:c++17
3201>   /Ylaimxclient_s
3201>   /Ycpch.hxx
3201>   /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\pch.pch
3201>   /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\pch.obj"
3201>pch_hdr.src
3201> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\00546f64bef0ebc1f7af97cc116bc5e6\cl_1.rsp
3201>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64/"
3201>   /FC
3201>   /Iamd64
3201>   /I.
3201>   /Ie:\os\src\data\MSRC
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3201>   /I..\
3201>   /I..\..\..\common
3201>   /I..\..\..\common\nlohmann
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\..\..\idl\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\security\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3201>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\ntos
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3201>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3201>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3201>   /Ie:\os\public\amd64fre\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3201>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /D_WIN64
3201>   /D_AMD64_
3201>   /DAMD64
3201>   /DCONDITION_HANDLING=1
3201>   /DNT_INST=0
3201>   /DWIN32=100
3201>   /D_NT1X_=100
3201>   /DWINNT=1
3201>   /D_WIN32_WINNT=0x0A00
3201>   /DWINVER=0x0A00
3201>   /D_WIN32_IE=0x0A00
3201>   /DWIN32_LEAN_AND_MEAN=1
3201>   /DDEVL=1
3201>   /DNDEBUG
3201>   /D_STL120_
3201>   /D_STL140_
3201>   /D_DLL=1
3201>   /D_MT=1
3201>   -DNT_IUM
3201>   -DWIN32
3201>   -D_WIN32
3201>   -DUNICODE
3201>   -D_UNICODE
3201>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3201>   /D_USE_DEV11_CRT
3201>   -D_APISET_MINWIN_VERSION=0x0115
3201>   -D_APISET_MINCORE_VERSION=0x0114
3201>   /DFE_SB
3201>   /DFE_IME
3201>   /DNTDDI_VERSION=0x0A000011
3201>   /DWINBLUE_KBSPRING14
3201>   /DBUILD_WINDOWS
3201>   /DUNDOCKED_WINDOWS_UCRT
3201>   /D__WRL_CONFIGURATION_LEGACY__
3201>   /DBUILD_UMS_ENABLED=1
3201>   /DBUILD_WOW64_ENABLED=1
3201>   /DBUILD_ARM64X_ENABLED=0
3201>   /DEXECUTABLE_WRITES_SUPPORT=0
3201>   -D_USE_DECLSPECS_FOR_SAL=1
3201>   /DRUN_WPP
3201>   -D__PLACEHOLDER_SAL=1
3201>   /D_ATL_STATIC_REGISTRY
3201>   /c
3201>   /Zc:wchar_t-
3201>   /Zl
3201>   /Zp8
3201>   /Gy
3201>   /W4
3201>   /d1import_no_registry
3201>   /EHsc
3201>   /GR-
3201>   /GF
3201>   /GS
3201>   /Z7
3201>   /Oxs
3201>   /GL
3201>   /Z7
3201>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3201>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3201>   /w15043
3201>   /Zc:rvalueCast
3201>   /Zo
3201>   -D_UCRT
3201>   -D_CONST_RETURN=
3201>   -D_CRT_SECURE_NO_WARNINGS
3201>   -D_CRT_NON_CONFORMING_SWPRINTFS
3201>   -D_CRT_NONSTDC_NO_WARNINGS
3201>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3201>   /D_CRT_STDIO_INLINE=extern
3201>   /D_NO_CRT_STDIO_INLINE
3201>   /D_ACRTIMP_ALT=
3201>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3201>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3201>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3201>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3201>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_HAS_STD_BYTE=0
3201>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_FULL_IOBUF
3201>   /d1initAll:Mask11
3201>   /d1initAll:FillPattern0
3201>   /d1nodatetime
3201>   /d1trimfile:e:\os\src\=BASEDIR
3201>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3201>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3201>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3201>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3201>   /d2AllowCompatibleILVersions
3201>   /d2Zi+
3201>   /ZH:SHA_256
3201>   /wd4986
3201>   /wd4987
3201>   /wd4471
3201>   /wd4369
3201>   /wd4309
3201>   /wd4754
3201>   /wd4427
3201>   /d2DeepThoughtInliner-
3201>   /d2implyavx512upperregs-
3201>   /Wv:19.23
3201>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\
3201>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3201>   /wl4002
3201>   /wl4003
3201>   /wl4005
3201>   /wl4006
3201>   /wl4007
3201>   /wl4008
3201>   /wl4010
3201>   /wl4013
3201>   /wl4015
3201>   /wl4018
3201>   /wl4020
3201>   /wl4022
3201>   /wl4024
3201>   /wl4025
3201>   /wl4026
3201>   /wl4027
3201>   /wl4028
3201>   /wl4029
3201>   /wl4030
3201>   /wl4031
3201>   /wl4033
3201>   /wl4034
3201>   /wl4036
3201>   /wl4038
3201>   /wl4041
3201>   /wl4042
3201>   /wl4045
3201>   /wl4047
3201>   /wl4048
3201>   /wl4049
3201>   /wl4056
3201>   /wl4066
3201>   /wl4067
3201>   /wl4068
3201>   /wl4073
3201>   /wl4074
3201>   /wl4075
3201>   /wl4076
3201>   /wl4077
3201>   /wl4079
3201>   /wl4080
3201>   /wl4081
3201>   /wl4083
3201>   /wl4085
3201>   /wl4086
3201>   /wl4087
3201>   /wl4088
3201>   /wl4089
3201>   /wl4090
3201>   /wl4091
3201>   /wl4094
3201>   /wl4096
3201>   /wl4097
3201>   /wl4098
3201>   /wl4099
3201>   /wl4101
3201>   /wl4102
3201>   /wl4109
3201>   /wl4112
3201>   /wl4113
3201>   /wl4114
3201>   /wl4115
3201>   /wl4116
3201>   /wl4117
3201>   /wl4119
3201>   /wl4120
3201>   /wl4122
3201>   /wl4124
3201>   /wl4129
3201>   /wl4133
3201>   /wl4138
3201>   /wl4141
3201>   /wl4142
3201>   /wl4143
3201>   /wl4144
3201>   /wl4145
3201>   /wl4150
3201>   /wl4153
3201>   /wl4154
3201>   /wl4155
3201>   /wl4156
3201>   /wl4157
3201>   /wl4158
3201>   /wl4159
3201>   /wl4160
3201>   /wl4161
3201>   /wl4162
3201>   /wl4163
3201>   /wl4164
3201>   /wl4166
3201>   /wl4167
3201>   /wl4168
3201>   /wl4172
3201>   /wl4174
3201>   /wl4175
3201>   /wl4176
3201>   /wl4177
3201>   /wl4178
3201>   /wl4180
3201>   /wl4182
3201>   /wl4183
3201>   /wl4185
3201>   /wl4186
3201>   /wl4187
3201>   /wl4190
3201>   /wl4192
3201>   /wl4197
3201>   /wl4200
3201>   /wl4213
3201>   /wl4215
3201>   /wl4216
3201>   /wl4218
3201>   /wl4223
3201>   /wl4224
3201>   /wl4226
3201>   /wl4227
3201>   /wl4228
3201>   /wl4229
3201>   /wl4230
3201>   /wl4237
3201>   /wl4240
3201>   /wl4243
3201>   /wl4244
3201>   /wl4250
3201>   /wl4251
3201>   /wl4258
3201>   /wl4267
3201>   /wl4269
3201>   /wl4272
3201>   /wl4273
3201>   /wl4274
3201>   /wl4275
3201>   /wl4276
3201>   /wl4278
3201>   /wl4280
3201>   /wl4281
3201>   /wl4282
3201>   /wl4283
3201>   /wl4285
3201>   /wl4286
3201>   /wl4288
3201>   /wl4290
3201>   /wl4291
3201>   /wl4293
3201>   /wl4297
3201>   /wl4302
3201>   /wl4305
3201>   /wl4306
3201>   /wl4307
3201>   /wl4309
3201>   /wl4310
3201>   /wl4311
3201>   /wl4312
3201>   /wl4313
3201>   /wl4316
3201>   /wl4319
3201>   /wl4325
3201>   /wl4326
3201>   /wl4329
3201>   /wl4333
3201>   /wl4334
3201>   /wl4335
3201>   /wl4340
3201>   /wl4344
3201>   /wl4346
3201>   /wl4348
3201>   /wl4353
3201>   /wl4356
3201>   /wl4357
3201>   /wl4358
3201>   /wl4359
3201>   /wl4364
3201>   /wl4368
3201>   /wl4369
3201>   /wl4373
3201>   /wl4374
3201>   /wl4375
3201>   /wl4376
3201>   /wl4377
3201>   /wl4378
3201>   /wl4379
3201>   /wl4381
3201>   /wl4382
3201>   /wl4383
3201>   /wl4384
3201>   /wl4390
3201>   /wl4391
3201>   /wl4392
3201>   /wl4393
3201>   /wl4394
3201>   /wl4395
3201>   /wl4396
3201>   /wl4397
3201>   /wl4398
3201>   /wl4399
3201>   /wl4600
3201>   /wl4401
3201>   /wl4402
3201>   /wl4403
3201>   /wl4404
3201>   /wl4405
3201>   /wl4406
3201>   /wl4407
3201>   /wl4409
3201>   /wl4410
3201>   /wl4411
3201>   /wl4414
3201>   /wl4420
3201>   /wl4430
3201>   /wl4436
3201>   /wl4439
3201>   /wl4440
3201>   /wl4441
3201>   /wl4445
3201>   /wl4461
3201>   /wl4462
3201>   /wl4470
3201>   /wl4473
3201>   /wl4477
3201>   /wl4484
3201>   /wl4485
3201>   /wl4486
3201>   /wl4488
3201>   /wl4489
3201>   /wl4490
3201>   /wl4502
3201>   /wl4503
3201>   /wl4506
3201>   /wl4508
3201>   /wl4511
3201>   /wl4518
3201>   /wl4521
3201>   /wl4522
3201>   /wl4523
3201>   /wl4526
3201>   /wl4530
3201>   /wl4534
3201>   /wl4535
3201>   /wl4537
3201>   /wl4538
3201>   /wl4540
3201>   /wl4541
3201>   /wl4543
3201>   /wl4544
3201>   /wl4550
3201>   /wl4551
3201>   /wl4552
3201>   /wl4553
3201>   /wl4554
3201>   /wl4556
3201>   /wl4558
3201>   /wl4561
3201>   /wl4566
3201>   /wl4570
3201>   /wl4572
3201>   /wl4580
3201>   /wl4581
3201>   /wl4584
3201>   /wl4596
3201>   /wl4597
3201>   /wl4602
3201>   /wl4603
3201>   /wl4606
3201>   /wl4612
3201>   /wl4613
3201>   /wl4615
3201>   /wl4616
3201>   /wl4618
3201>   /wl4620
3201>   /wl4621
3201>   /wl4622
3201>   /wl4624
3201>   /wl4627
3201>   /wl4630
3201>   /wl4632
3201>   /wl4633
3201>   /wl4635
3201>   /wl4636
3201>   /wl4637
3201>   /wl4638
3201>   /wl4641
3201>   /wl4645
3201>   /wl4646
3201>   /wl4650
3201>   /wl4651
3201>   /wl4652
3201>   /wl4653
3201>   /wl4655
3201>   /wl4656
3201>   /wl4657
3201>   /wl4659
3201>   /wl4661
3201>   /wl4662
3201>   /wl4667
3201>   /wl4669
3201>   /wl4674
3201>   /wl4677
3201>   /wl4678
3201>   /wl4679
3201>   /wl4683
3201>   /wl4684
3201>   /wl4685
3201>   /wl4687
3201>   /wl4688
3201>   /wl4691
3201>   /wl4693
3201>   /wl4694
3201>   /wl4698
3201>   /wl4711
3201>   /wl4715
3201>   /wl4716
3201>   /wl4717
3201>   /wl4722
3201>   /wl4723
3201>   /wl4724
3201>   /wl4727
3201>   /wl4730
3201>   /wl4731
3201>   /wl4733
3201>   /wl4739
3201>   /wl4742
3201>   /wl4743
3201>   /wl4744
3201>   /wl4747
3201>   /wl4750
3201>   /wl4756
3201>   /wl4768
3201>   /wl4772
3201>   /wl4788
3201>   /wl4793
3201>   /wl4794
3201>   /wl4799
3201>   /wl4803
3201>   /wl4804
3201>   /wl4805
3201>   /wl4806
3201>   /wl4807
3201>   /wl4810
3201>   /wl4811
3201>   /wl4812
3201>   /wl4813
3201>   /wl4817
3201>   /wl4819
3201>   /wl4821
3201>   /wl4823
3201>   /wl4829
3201>   /wl4834
3201>   /wl4835
3201>   /wl4838
3201>   /wl4839
3201>   /wl4867
3201>   /wl4900
3201>   /wl4910
3201>   /wl4912
3201>   /wl4920
3201>   /wl4925
3201>   /wl4926
3201>   /wl4927
3201>   /wl4929
3201>   /wl4930
3201>   /wl4935
3201>   /wl4936
3201>   /wl4939
3201>   /wl4944
3201>   /wl4945
3201>   /wl4947
3201>   /wl4948
3201>   /wl4949
3201>   /wl4950
3201>   /wl4951
3201>   /wl4952
3201>   /wl4953
3201>   /wl4956
3201>   /wl4957
3201>   /wl4958
3201>   /wl4959
3201>   /wl4961
3201>   /wl4964
3201>   /wl4965
3201>   /wl4972
3201>   /wl4984
3201>   /wl4995
3201>   /wl4996
3201>   /wl4997
3201>   /wl4999
3201>   /wl5033
3201>   /wl5037
3201>   /wl5046
3201>   /wl5050
3201>   /wl5055
3201>   /wl5056
3201>   /wl5105
3201>   /wl5208
3201>   /d2Qvec-mathlib-
3201>   /d2Qvec-sse2only
3201>   /Gw
3201>   /Zc:checkGwOdr
3201>   /d1ignorePragmaWarningError
3201>   /wd4316
3201>   /wd4973
3201>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3201>   /d2FH4
3201>   /Brepro
3201>   -D_HAS_MAGIC_STATICS=1
3201>   /Qspectre
3201>   /wd5045
3201>   /d2guardspecanalysismode:v1_0
3201>   /d2guardspecmode2
3201>   /guard:cf
3201>   /d2guardcfgfuncptr-
3201>   /d2guardcfgdispatch
3201>   /guard:ehcont
3201>   -D__PLACEHOLDER_SAL=1
3201>   -wd4425
3201>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3201>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3201>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3201>   /std:c++17
3201>   /Yupch.hxx /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\pch.pch
3201>   ..\aimxclient.cpp ..\aimxrpcclient.cpp ..\memory.cpp 
3201>aimxclient.cpp
3201>aimxrpcclient.cpp
3201>memory.cpp
3201> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\aimxclient_s.lib /IGNORE:4078,4221,4281,4006,4198   /nodefaultlib /machine:amd64 /ltcg /Brepro @e:\os\obj\amd64fre\temp\00546f64bef0ebc1f7af97cc116bc5e6\lib_1.rsp
3201>Microsoft (R) Library Manager Version 14.42.34444.100
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\pch.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\aimxclient.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\aimxrpcclient.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\memory.obj 
3201>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\Macros-PASS1.txt
3201>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\Macros-PASS1.txt
3201> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3201>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib in pass PASS1
3203>Calculated LAYERINFO_MODULE='OneCoreDS'.
3203>makefile.def: TEMP=e:\os\obj\amd64fre\temp\80622f894f22a1fda359c78ec02cadce
3203>makefile.def: BUILDINGINDATT=
3203>[Core OS Undocking] NOT using package ''
3203>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll' (target 'aimxclient', type 'DYNLINK', nt_target_version '0xA000011')
3203>ObjectsMac.ts: validation succeeded
3203>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" (STL_VER_TELEMETRY)
3203>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3203>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64 already exists.
3203> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\_PASS1_Marker.log
3203> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\80622f894f22a1fda359c78ec02cadce\tmp_45032_1753418469749105800.tmp /Tpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\pch_hdr.src
3203>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3203>Copyright (C) Microsoft Corporation.  All rights reserved.
3203>cl 
3203>   /Iamd64
3203>   /I.
3203>   /Ie:\os\src\data\MSRC
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3203>   /I..\
3203>   /I..\..\..\common
3203>   /I..\..\..\common\nlohmann
3203>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
3203>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3203>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\..\..\idl\objfre\amd64
3203>   /Ie:\os\src\onecore\ds\security\inc
3203>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3203>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3203>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3203>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\ntos
3203>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3203>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3203>   /Ie:\os\public\amd64fre\internal\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3203>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\inc
3203>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\client
3203>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3203>   /Ie:\os\src\onecore\ds\inc
3203>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3203>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3203>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3203>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3203>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3203>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3203>   /Ie:\os\public\amd64fre\shared\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3203>   /Ie:\os\public\amd64fre\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3203>   /Ie:\os\public\amd64fre\shared\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3203>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3203>   /Ie:\os\public\amd64fre\internal\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3203>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3203>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3203>   /D_WIN64
3203>   /D_AMD64_
3203>   /DAMD64
3203>   /DCONDITION_HANDLING=1
3203>   /DNT_INST=0
3203>   /DWIN32=100
3203>   /D_NT1X_=100
3203>   /DWINNT=1
3203>   /D_WIN32_WINNT=0x0A00
3203>   /DWINVER=0x0A00
3203>   /D_WIN32_IE=0x0A00
3203>   /DWIN32_LEAN_AND_MEAN=1
3203>   /DDEVL=1
3203>   /DNDEBUG
3203>   /D_STL120_
3203>   /D_STL140_
3203>   /D_DLL=1
3203>   /D_MT=1
3203>   -DNT_IUM
3203>   -DWIN32
3203>   -D_WIN32
3203>   -DUNICODE
3203>   -D_UNICODE
3203>   -DAIMXCLIENT_EXPORTS
3203>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3203>   /D_USE_DEV11_CRT
3203>   -D_APISET_MINWIN_VERSION=0x0115
3203>   -D_APISET_MINCORE_VERSION=0x0114
3203>   /DFE_SB
3203>   /DFE_IME
3203>   /DNTDDI_VERSION=0x0A000011
3203>   /DWINBLUE_KBSPRING14
3203>   /DBUILD_WINDOWS
3203>   /DUNDOCKED_WINDOWS_UCRT
3203>   /D__WRL_CONFIGURATION_LEGACY__
3203>   /DBUILD_UMS_ENABLED=1
3203>   /DBUILD_WOW64_ENABLED=1
3203>   /DBUILD_ARM64X_ENABLED=0
3203>   /DEXECUTABLE_WRITES_SUPPORT=0
3203>   -D_USE_DECLSPECS_FOR_SAL=1
3203>   /DRUN_WPP
3203>   -D__PLACEHOLDER_SAL=1
3203>   /D_ATL_STATIC_REGISTRY
3203>   /D_WINDLL
3203>   /c
3203>   /Zc:wchar_t-
3203>   /Zl
3203>   /Zp8
3203>   /Gy
3203>   /W4
3203>   /d1import_no_registry
3203>   /EHsc
3203>   /GR-
3203>   /GF
3203>   /GS
3203>   /Z7
3203>   /Oxs
3203>   /GL
3203>   /Z7
3203>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3203>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3203>   /w15043
3203>   /Zc:rvalueCast
3203>   /Zo
3203>   -D_UCRT
3203>   -D_CONST_RETURN=
3203>   -D_CRT_SECURE_NO_WARNINGS
3203>   -D_CRT_NON_CONFORMING_SWPRINTFS
3203>   -D_CRT_NONSTDC_NO_WARNINGS
3203>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3203>   /D_CRT_STDIO_INLINE=extern
3203>   /D_NO_CRT_STDIO_INLINE
3203>   /D_ACRTIMP_ALT=
3203>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3203>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3203>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3203>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3203>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3203>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3203>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3203>   /D_HAS_STD_BYTE=0
3203>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3203>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3203>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3203>   /D_FULL_IOBUF
3203>   /d1initAll:Mask11
3203>   /d1initAll:FillPattern0
3203>   /d1nodatetime
3203>   /d1trimfile:e:\os\src\=BASEDIR
3203>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3203>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3203>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3203>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3203>   /d2AllowCompatibleILVersions
3203>   /d2Zi+
3203>   /ZH:SHA_256
3203>   /wd4986
3203>   /wd4987
3203>   /wd4471
3203>   /wd4369
3203>   /wd4309
3203>   /wd4754
3203>   /wd4427
3203>   /d2DeepThoughtInliner-
3203>   /d2implyavx512upperregs-
3203>   /Wv:19.23
3203>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\
3203>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3203>   /wl4002
3203>   /wl4003
3203>   /wl4005
3203>   /wl4006
3203>   /wl4007
3203>   /wl4008
3203>   /wl4010
3203>   /wl4013
3203>   /wl4015
3203>   /wl4018
3203>   /wl4020
3203>   /wl4022
3203>   /wl4024
3203>   /wl4025
3203>   /wl4026
3203>   /wl4027
3203>   /wl4028
3203>   /wl4029
3203>   /wl4030
3203>   /wl4031
3203>   /wl4033
3203>   /wl4034
3203>   /wl4036
3203>   /wl4038
3203>   /wl4041
3203>   /wl4042
3203>   /wl4045
3203>   /wl4047
3203>   /wl4048
3203>   /wl4049
3203>   /wl4056
3203>   /wl4066
3203>   /wl4067
3203>   /wl4068
3203>   /wl4073
3203>   /wl4074
3203>   /wl4075
3203>   /wl4076
3203>   /wl4077
3203>   /wl4079
3203>   /wl4080
3203>   /wl4081
3203>   /wl4083
3203>   /wl4085
3203>   /wl4086
3203>   /wl4087
3203>   /wl4088
3203>   /wl4089
3203>   /wl4090
3203>   /wl4091
3203>   /wl4094
3203>   /wl4096
3203>   /wl4097
3203>   /wl4098
3203>   /wl4099
3203>   /wl4101
3203>   /wl4102
3203>   /wl4109
3203>   /wl4112
3203>   /wl4113
3203>   /wl4114
3203>   /wl4115
3203>   /wl4116
3203>   /wl4117
3203>   /wl4119
3203>   /wl4120
3203>   /wl4122
3203>   /wl4124
3203>   /wl4129
3203>   /wl4133
3203>   /wl4138
3203>   /wl4141
3203>   /wl4142
3203>   /wl4143
3203>   /wl4144
3203>   /wl4145
3203>   /wl4150
3203>   /wl4153
3203>   /wl4154
3203>   /wl4155
3203>   /wl4156
3203>   /wl4157
3203>   /wl4158
3203>   /wl4159
3203>   /wl4160
3203>   /wl4161
3203>   /wl4162
3203>   /wl4163
3203>   /wl4164
3203>   /wl4166
3203>   /wl4167
3203>   /wl4168
3203>   /wl4172
3203>   /wl4174
3203>   /wl4175
3203>   /wl4176
3203>   /wl4177
3203>   /wl4178
3203>   /wl4180
3203>   /wl4182
3203>   /wl4183
3203>   /wl4185
3203>   /wl4186
3203>   /wl4187
3203>   /wl4190
3203>   /wl4192
3203>   /wl4197
3203>   /wl4200
3203>   /wl4213
3203>   /wl4215
3203>   /wl4216
3203>   /wl4218
3203>   /wl4223
3203>   /wl4224
3203>   /wl4226
3203>   /wl4227
3203>   /wl4228
3203>   /wl4229
3203>   /wl4230
3203>   /wl4237
3203>   /wl4240
3203>   /wl4243
3203>   /wl4244
3203>   /wl4250
3203>   /wl4251
3203>   /wl4258
3203>   /wl4267
3203>   /wl4269
3203>   /wl4272
3203>   /wl4273
3203>   /wl4274
3203>   /wl4275
3203>   /wl4276
3203>   /wl4278
3203>   /wl4280
3203>   /wl4281
3203>   /wl4282
3203>   /wl4283
3203>   /wl4285
3203>   /wl4286
3203>   /wl4288
3203>   /wl4290
3203>   /wl4291
3203>   /wl4293
3203>   /wl4297
3203>   /wl4302
3203>   /wl4305
3203>   /wl4306
3203>   /wl4307
3203>   /wl4309
3203>   /wl4310
3203>   /wl4311
3203>   /wl4312
3203>   /wl4313
3203>   /wl4316
3203>   /wl4319
3203>   /wl4325
3203>   /wl4326
3203>   /wl4329
3203>   /wl4333
3203>   /wl4334
3203>   /wl4335
3203>   /wl4340
3203>   /wl4344
3203>   /wl4346
3203>   /wl4348
3203>   /wl4353
3203>   /wl4356
3203>   /wl4357
3203>   /wl4358
3203>   /wl4359
3203>   /wl4364
3203>   /wl4368
3203>   /wl4369
3203>   /wl4373
3203>   /wl4374
3203>   /wl4375
3203>   /wl4376
3203>   /wl4377
3203>   /wl4378
3203>   /wl4379
3203>   /wl4381
3203>   /wl4382
3203>   /wl4383
3203>   /wl4384
3203>   /wl4390
3203>   /wl4391
3203>   /wl4392
3203>   /wl4393
3203>   /wl4394
3203>   /wl4395
3203>   /wl4396
3203>   /wl4397
3203>   /wl4398
3203>   /wl4399
3203>   /wl4600
3203>   /wl4401
3203>   /wl4402
3203>   /wl4403
3203>   /wl4404
3203>   /wl4405
3203>   /wl4406
3203>   /wl4407
3203>   /wl4409
3203>   /wl4410
3203>   /wl4411
3203>   /wl4414
3203>   /wl4420
3203>   /wl4430
3203>   /wl4436
3203>   /wl4439
3203>   /wl4440
3203>   /wl4441
3203>   /wl4445
3203>   /wl4461
3203>   /wl4462
3203>   /wl4470
3203>   /wl4473
3203>   /wl4477
3203>   /wl4484
3203>   /wl4485
3203>   /wl4486
3203>   /wl4488
3203>   /wl4489
3203>   /wl4490
3203>   /wl4502
3203>   /wl4503
3203>   /wl4506
3203>   /wl4508
3203>   /wl4511
3203>   /wl4518
3203>   /wl4521
3203>   /wl4522
3203>   /wl4523
3203>   /wl4526
3203>   /wl4530
3203>   /wl4534
3203>   /wl4535
3203>   /wl4537
3203>   /wl4538
3203>   /wl4540
3203>   /wl4541
3203>   /wl4543
3203>   /wl4544
3203>   /wl4550
3203>   /wl4551
3203>   /wl4552
3203>   /wl4553
3203>   /wl4554
3203>   /wl4556
3203>   /wl4558
3203>   /wl4561
3203>   /wl4566
3203>   /wl4570
3203>   /wl4572
3203>   /wl4580
3203>   /wl4581
3203>   /wl4584
3203>   /wl4596
3203>   /wl4597
3203>   /wl4602
3203>   /wl4603
3203>   /wl4606
3203>   /wl4612
3203>   /wl4613
3203>   /wl4615
3203>   /wl4616
3203>   /wl4618
3203>   /wl4620
3203>   /wl4621
3203>   /wl4622
3203>   /wl4624
3203>   /wl4627
3203>   /wl4630
3203>   /wl4632
3203>   /wl4633
3203>   /wl4635
3203>   /wl4636
3203>   /wl4637
3203>   /wl4638
3203>   /wl4641
3203>   /wl4645
3203>   /wl4646
3203>   /wl4650
3203>   /wl4651
3203>   /wl4652
3203>   /wl4653
3203>   /wl4655
3203>   /wl4656
3203>   /wl4657
3203>   /wl4659
3203>   /wl4661
3203>   /wl4662
3203>   /wl4667
3203>   /wl4669
3203>   /wl4674
3203>   /wl4677
3203>   /wl4678
3203>   /wl4679
3203>   /wl4683
3203>   /wl4684
3203>   /wl4685
3203>   /wl4687
3203>   /wl4688
3203>   /wl4691
3203>   /wl4693
3203>   /wl4694
3203>   /wl4698
3203>   /wl4711
3203>   /wl4715
3203>   /wl4716
3203>   /wl4717
3203>   /wl4722
3203>   /wl4723
3203>   /wl4724
3203>   /wl4727
3203>   /wl4730
3203>   /wl4731
3203>   /wl4733
3203>   /wl4739
3203>   /wl4742
3203>   /wl4743
3203>   /wl4744
3203>   /wl4747
3203>   /wl4750
3203>   /wl4756
3203>   /wl4768
3203>   /wl4772
3203>   /wl4788
3203>   /wl4793
3203>   /wl4794
3203>   /wl4799
3203>   /wl4803
3203>   /wl4804
3203>   /wl4805
3203>   /wl4806
3203>   /wl4807
3203>   /wl4810
3203>   /wl4811
3203>   /wl4812
3203>   /wl4813
3203>   /wl4817
3203>   /wl4819
3203>   /wl4821
3203>   /wl4823
3203>   /wl4829
3203>   /wl4834
3203>   /wl4835
3203>   /wl4838
3203>   /wl4839
3203>   /wl4867
3203>   /wl4900
3203>   /wl4910
3203>   /wl4912
3203>   /wl4920
3203>   /wl4925
3203>   /wl4926
3203>   /wl4927
3203>   /wl4929
3203>   /wl4930
3203>   /wl4935
3203>   /wl4936
3203>   /wl4939
3203>   /wl4944
3203>   /wl4945
3203>   /wl4947
3203>   /wl4948
3203>   /wl4949
3203>   /wl4950
3203>   /wl4951
3203>   /wl4952
3203>   /wl4953
3203>   /wl4956
3203>   /wl4957
3203>   /wl4958
3203>   /wl4959
3203>   /wl4961
3203>   /wl4964
3203>   /wl4965
3203>   /wl4972
3203>   /wl4984
3203>   /wl4995
3203>   /wl4996
3203>   /wl4997
3203>   /wl4999
3203>   /wl5033
3203>   /wl5037
3203>   /wl5046
3203>   /wl5050
3203>   /wl5055
3203>   /wl5056
3203>   /wl5105
3203>   /wl5208
3203>   /d2Qvec-mathlib-
3203>   /d2Qvec-sse2only
3203>   /Gw
3203>   /Zc:checkGwOdr
3203>   /d1ignorePragmaWarningError
3203>   /wd4316
3203>   /wd4973
3203>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3203>   /d2FH4
3203>   /Brepro
3203>   -D_HAS_MAGIC_STATICS=1
3203>   /Qspectre
3203>   /wd5045
3203>   /d2guardspecanalysismode:v1_0
3203>   /d2guardspecmode2
3203>   /guard:cf
3203>   /d2guardcfgfuncptr-
3203>   /d2guardcfgdispatch
3203>   /guard:ehcont
3203>   -D__PLACEHOLDER_SAL=1
3203>   -wd4425
3203>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3203>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3203>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3203>   /std:c++17
3203>   /Ylaimxclient
3203>   /Ycpch.hxx
3203>   /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\pch.pch
3203>   /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\pch.obj"
3203>pch_hdr.src
3203> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\80622f894f22a1fda359c78ec02cadce\cl_1.rsp
3203>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3203>Copyright (C) Microsoft Corporation.  All rights reserved.
3203>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64/"
3203>   /FC
3203>   /Iamd64
3203>   /I.
3203>   /Ie:\os\src\data\MSRC
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3203>   /I..\
3203>   /I..\..\..\common
3203>   /I..\..\..\common\nlohmann
3203>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
3203>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3203>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\..\..\idl\objfre\amd64
3203>   /Ie:\os\src\onecore\ds\security\inc
3203>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3203>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3203>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3203>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\ntos
3203>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3203>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3203>   /Ie:\os\public\amd64fre\internal\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3203>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\inc
3203>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\client
3203>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3203>   /Ie:\os\src\onecore\ds\inc
3203>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3203>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3203>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3203>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3203>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3203>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3203>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3203>   /Ie:\os\public\amd64fre\shared\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3203>   /Ie:\os\public\amd64fre\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3203>   /Ie:\os\public\amd64fre\shared\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3203>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3203>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3203>   /Ie:\os\public\amd64fre\internal\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3203>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3203>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3203>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3203>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3203>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3203>   /D_WIN64
3203>   /D_AMD64_
3203>   /DAMD64
3203>   /DCONDITION_HANDLING=1
3203>   /DNT_INST=0
3203>   /DWIN32=100
3203>   /D_NT1X_=100
3203>   /DWINNT=1
3203>   /D_WIN32_WINNT=0x0A00
3203>   /DWINVER=0x0A00
3203>   /D_WIN32_IE=0x0A00
3203>   /DWIN32_LEAN_AND_MEAN=1
3203>   /DDEVL=1
3203>   /DNDEBUG
3203>   /D_STL120_
3203>   /D_STL140_
3203>   /D_DLL=1
3203>   /D_MT=1
3203>   -DNT_IUM
3203>   -DWIN32
3203>   -D_WIN32
3203>   -DUNICODE
3203>   -D_UNICODE
3203>   -DAIMXCLIENT_EXPORTS
3203>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3203>   /D_USE_DEV11_CRT
3203>   -D_APISET_MINWIN_VERSION=0x0115
3203>   -D_APISET_MINCORE_VERSION=0x0114
3203>   /DFE_SB
3203>   /DFE_IME
3203>   /DNTDDI_VERSION=0x0A000011
3203>   /DWINBLUE_KBSPRING14
3203>   /DBUILD_WINDOWS
3203>   /DUNDOCKED_WINDOWS_UCRT
3203>   /D__WRL_CONFIGURATION_LEGACY__
3203>   /DBUILD_UMS_ENABLED=1
3203>   /DBUILD_WOW64_ENABLED=1
3203>   /DBUILD_ARM64X_ENABLED=0
3203>   /DEXECUTABLE_WRITES_SUPPORT=0
3203>   -D_USE_DECLSPECS_FOR_SAL=1
3203>   /DRUN_WPP
3203>   -D__PLACEHOLDER_SAL=1
3203>   /D_ATL_STATIC_REGISTRY
3203>   /D_WINDLL
3203>   /c
3203>   /Zc:wchar_t-
3203>   /Zl
3203>   /Zp8
3203>   /Gy
3203>   /W4
3203>   /d1import_no_registry
3203>   /EHsc
3203>   /GR-
3203>   /GF
3203>   /GS
3203>   /Z7
3203>   /Oxs
3203>   /GL
3203>   /Z7
3203>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3203>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3203>   /w15043
3203>   /Zc:rvalueCast
3203>   /Zo
3203>   -D_UCRT
3203>   -D_CONST_RETURN=
3203>   -D_CRT_SECURE_NO_WARNINGS
3203>   -D_CRT_NON_CONFORMING_SWPRINTFS
3203>   -D_CRT_NONSTDC_NO_WARNINGS
3203>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3203>   /D_CRT_STDIO_INLINE=extern
3203>   /D_NO_CRT_STDIO_INLINE
3203>   /D_ACRTIMP_ALT=
3203>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3203>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3203>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3203>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3203>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3203>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3203>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3203>   /D_HAS_STD_BYTE=0
3203>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3203>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3203>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3203>   /D_FULL_IOBUF
3203>   /d1initAll:Mask11
3203>   /d1initAll:FillPattern0
3203>   /d1nodatetime
3203>   /d1trimfile:e:\os\src\=BASEDIR
3203>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3203>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3203>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3203>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3203>   /d2AllowCompatibleILVersions
3203>   /d2Zi+
3203>   /ZH:SHA_256
3203>   /wd4986
3203>   /wd4987
3203>   /wd4471
3203>   /wd4369
3203>   /wd4309
3203>   /wd4754
3203>   /wd4427
3203>   /d2DeepThoughtInliner-
3203>   /d2implyavx512upperregs-
3203>   /Wv:19.23
3203>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\
3203>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3203>   /wl4002
3203>   /wl4003
3203>   /wl4005
3203>   /wl4006
3203>   /wl4007
3203>   /wl4008
3203>   /wl4010
3203>   /wl4013
3203>   /wl4015
3203>   /wl4018
3203>   /wl4020
3203>   /wl4022
3203>   /wl4024
3203>   /wl4025
3203>   /wl4026
3203>   /wl4027
3203>   /wl4028
3203>   /wl4029
3203>   /wl4030
3203>   /wl4031
3203>   /wl4033
3203>   /wl4034
3203>   /wl4036
3203>   /wl4038
3203>   /wl4041
3203>   /wl4042
3203>   /wl4045
3203>   /wl4047
3203>   /wl4048
3203>   /wl4049
3203>   /wl4056
3203>   /wl4066
3203>   /wl4067
3203>   /wl4068
3203>   /wl4073
3203>   /wl4074
3203>   /wl4075
3203>   /wl4076
3203>   /wl4077
3203>   /wl4079
3203>   /wl4080
3203>   /wl4081
3203>   /wl4083
3203>   /wl4085
3203>   /wl4086
3203>   /wl4087
3203>   /wl4088
3203>   /wl4089
3203>   /wl4090
3203>   /wl4091
3203>   /wl4094
3203>   /wl4096
3203>   /wl4097
3203>   /wl4098
3203>   /wl4099
3203>   /wl4101
3203>   /wl4102
3203>   /wl4109
3203>   /wl4112
3203>   /wl4113
3203>   /wl4114
3203>   /wl4115
3203>   /wl4116
3203>   /wl4117
3203>   /wl4119
3203>   /wl4120
3203>   /wl4122
3203>   /wl4124
3203>   /wl4129
3203>   /wl4133
3203>   /wl4138
3203>   /wl4141
3203>   /wl4142
3203>   /wl4143
3203>   /wl4144
3203>   /wl4145
3203>   /wl4150
3203>   /wl4153
3203>   /wl4154
3203>   /wl4155
3203>   /wl4156
3203>   /wl4157
3203>   /wl4158
3203>   /wl4159
3203>   /wl4160
3203>   /wl4161
3203>   /wl4162
3203>   /wl4163
3203>   /wl4164
3203>   /wl4166
3203>   /wl4167
3203>   /wl4168
3203>   /wl4172
3203>   /wl4174
3203>   /wl4175
3203>   /wl4176
3203>   /wl4177
3203>   /wl4178
3203>   /wl4180
3203>   /wl4182
3203>   /wl4183
3203>   /wl4185
3203>   /wl4186
3203>   /wl4187
3203>   /wl4190
3203>   /wl4192
3203>   /wl4197
3203>   /wl4200
3203>   /wl4213
3203>   /wl4215
3203>   /wl4216
3203>   /wl4218
3203>   /wl4223
3203>   /wl4224
3203>   /wl4226
3203>   /wl4227
3203>   /wl4228
3203>   /wl4229
3203>   /wl4230
3203>   /wl4237
3203>   /wl4240
3203>   /wl4243
3203>   /wl4244
3203>   /wl4250
3203>   /wl4251
3203>   /wl4258
3203>   /wl4267
3203>   /wl4269
3203>   /wl4272
3203>   /wl4273
3203>   /wl4274
3203>   /wl4275
3203>   /wl4276
3203>   /wl4278
3203>   /wl4280
3203>   /wl4281
3203>   /wl4282
3203>   /wl4283
3203>   /wl4285
3203>   /wl4286
3203>   /wl4288
3203>   /wl4290
3203>   /wl4291
3203>   /wl4293
3203>   /wl4297
3203>   /wl4302
3203>   /wl4305
3203>   /wl4306
3203>   /wl4307
3203>   /wl4309
3203>   /wl4310
3203>   /wl4311
3203>   /wl4312
3203>   /wl4313
3203>   /wl4316
3203>   /wl4319
3203>   /wl4325
3203>   /wl4326
3203>   /wl4329
3203>   /wl4333
3203>   /wl4334
3203>   /wl4335
3203>   /wl4340
3203>   /wl4344
3203>   /wl4346
3203>   /wl4348
3203>   /wl4353
3203>   /wl4356
3203>   /wl4357
3203>   /wl4358
3203>   /wl4359
3203>   /wl4364
3203>   /wl4368
3203>   /wl4369
3203>   /wl4373
3203>   /wl4374
3203>   /wl4375
3203>   /wl4376
3203>   /wl4377
3203>   /wl4378
3203>   /wl4379
3203>   /wl4381
3203>   /wl4382
3203>   /wl4383
3203>   /wl4384
3203>   /wl4390
3203>   /wl4391
3203>   /wl4392
3203>   /wl4393
3203>   /wl4394
3203>   /wl4395
3203>   /wl4396
3203>   /wl4397
3203>   /wl4398
3203>   /wl4399
3203>   /wl4600
3203>   /wl4401
3203>   /wl4402
3203>   /wl4403
3203>   /wl4404
3203>   /wl4405
3203>   /wl4406
3203>   /wl4407
3203>   /wl4409
3203>   /wl4410
3203>   /wl4411
3203>   /wl4414
3203>   /wl4420
3203>   /wl4430
3203>   /wl4436
3203>   /wl4439
3203>   /wl4440
3203>   /wl4441
3203>   /wl4445
3203>   /wl4461
3203>   /wl4462
3203>   /wl4470
3203>   /wl4473
3203>   /wl4477
3203>   /wl4484
3203>   /wl4485
3203>   /wl4486
3203>   /wl4488
3203>   /wl4489
3203>   /wl4490
3203>   /wl4502
3203>   /wl4503
3203>   /wl4506
3203>   /wl4508
3203>   /wl4511
3203>   /wl4518
3203>   /wl4521
3203>   /wl4522
3203>   /wl4523
3203>   /wl4526
3203>   /wl4530
3203>   /wl4534
3203>   /wl4535
3203>   /wl4537
3203>   /wl4538
3203>   /wl4540
3203>   /wl4541
3203>   /wl4543
3203>   /wl4544
3203>   /wl4550
3203>   /wl4551
3203>   /wl4552
3203>   /wl4553
3203>   /wl4554
3203>   /wl4556
3203>   /wl4558
3203>   /wl4561
3203>   /wl4566
3203>   /wl4570
3203>   /wl4572
3203>   /wl4580
3203>   /wl4581
3203>   /wl4584
3203>   /wl4596
3203>   /wl4597
3203>   /wl4602
3203>   /wl4603
3203>   /wl4606
3203>   /wl4612
3203>   /wl4613
3203>   /wl4615
3203>   /wl4616
3203>   /wl4618
3203>   /wl4620
3203>   /wl4621
3203>   /wl4622
3203>   /wl4624
3203>   /wl4627
3203>   /wl4630
3203>   /wl4632
3203>   /wl4633
3203>   /wl4635
3203>   /wl4636
3203>   /wl4637
3203>   /wl4638
3203>   /wl4641
3203>   /wl4645
3203>   /wl4646
3203>   /wl4650
3203>   /wl4651
3203>   /wl4652
3203>   /wl4653
3203>   /wl4655
3203>   /wl4656
3203>   /wl4657
3203>   /wl4659
3203>   /wl4661
3203>   /wl4662
3203>   /wl4667
3203>   /wl4669
3203>   /wl4674
3203>   /wl4677
3203>   /wl4678
3203>   /wl4679
3203>   /wl4683
3203>   /wl4684
3203>   /wl4685
3203>   /wl4687
3203>   /wl4688
3203>   /wl4691
3203>   /wl4693
3203>   /wl4694
3203>   /wl4698
3203>   /wl4711
3203>   /wl4715
3203>   /wl4716
3203>   /wl4717
3203>   /wl4722
3203>   /wl4723
3203>   /wl4724
3203>   /wl4727
3203>   /wl4730
3203>   /wl4731
3203>   /wl4733
3203>   /wl4739
3203>   /wl4742
3203>   /wl4743
3203>   /wl4744
3203>   /wl4747
3203>   /wl4750
3203>   /wl4756
3203>   /wl4768
3203>   /wl4772
3203>   /wl4788
3203>   /wl4793
3203>   /wl4794
3203>   /wl4799
3203>   /wl4803
3203>   /wl4804
3203>   /wl4805
3203>   /wl4806
3203>   /wl4807
3203>   /wl4810
3203>   /wl4811
3203>   /wl4812
3203>   /wl4813
3203>   /wl4817
3203>   /wl4819
3203>   /wl4821
3203>   /wl4823
3203>   /wl4829
3203>   /wl4834
3203>   /wl4835
3203>   /wl4838
3203>   /wl4839
3203>   /wl4867
3203>   /wl4900
3203>   /wl4910
3203>   /wl4912
3203>   /wl4920
3203>   /wl4925
3203>   /wl4926
3203>   /wl4927
3203>   /wl4929
3203>   /wl4930
3203>   /wl4935
3203>   /wl4936
3203>   /wl4939
3203>   /wl4944
3203>   /wl4945
3203>   /wl4947
3203>   /wl4948
3203>   /wl4949
3203>   /wl4950
3203>   /wl4951
3203>   /wl4952
3203>   /wl4953
3203>   /wl4956
3203>   /wl4957
3203>   /wl4958
3203>   /wl4959
3203>   /wl4961
3203>   /wl4964
3203>   /wl4965
3203>   /wl4972
3203>   /wl4984
3203>   /wl4995
3203>   /wl4996
3203>   /wl4997
3203>   /wl4999
3203>   /wl5033
3203>   /wl5037
3203>   /wl5046
3203>   /wl5050
3203>   /wl5055
3203>   /wl5056
3203>   /wl5105
3203>   /wl5208
3203>   /d2Qvec-mathlib-
3203>   /d2Qvec-sse2only
3203>   /Gw
3203>   /Zc:checkGwOdr
3203>   /d1ignorePragmaWarningError
3203>   /wd4316
3203>   /wd4973
3203>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3203>   /d2FH4
3203>   /Brepro
3203>   -D_HAS_MAGIC_STATICS=1
3203>   /Qspectre
3203>   /wd5045
3203>   /d2guardspecanalysismode:v1_0
3203>   /d2guardspecmode2
3203>   /guard:cf
3203>   /d2guardcfgfuncptr-
3203>   /d2guardcfgdispatch
3203>   /guard:ehcont
3203>   -D__PLACEHOLDER_SAL=1
3203>   -wd4425
3203>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3203>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3203>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3203>   /std:c++17
3203>   /Yupch.hxx /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\pch.pch
3203>   ..\aimxclient.cpp ..\aimxrpcclient.cpp ..\memory.cpp 
3203>aimxclient.cpp
3203>aimxrpcclient.cpp
3203>memory.cpp
3203> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.lib @e:\os\obj\amd64fre\temp\80622f894f22a1fda359c78ec02cadce\lib_1.rsp
3203>Microsoft (R) Library Manager Version 14.42.34444.100
3203>Copyright (C) Microsoft Corporation.  All rights reserved.
3203>/IGNORE:4078,4221,4281,4006,4198 
3203>/nodefaultlib 
3203>/machine:amd64 
3203>/ltcg 
3203>/Brepro 
3203>/def:aimxclient.def 
3203>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.obj 
3203>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxrpcclient.obj 
3203>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\memory.obj 
3203>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\pch.obj 
3203>   Creating library e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.lib and object e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.exp
3203>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\Macros-PASS1.txt
3203>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\Macros-PASS1.txt
3203> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3203>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll in pass PASS1
3205>Calculated LAYERINFO_MODULE='OneCoreDS'.
3205>makefile.def: TEMP=e:\os\obj\amd64fre\temp\7726ce71621e698e0ad7e6c5e6e8a399
3205>makefile.def: BUILDINGINDATT=
3205>[Core OS Undocking] NOT using package ''
3205>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\mcpprotocollib' (target 'McpProtocolLib', type 'LIBRARY', nt_target_version '0xA000011')
3205>ObjectsMac.ts: validation succeeded
3205>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\mcpprotocollib" (STL_VER_TELEMETRY)
3205>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3205>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\objfre\amd64 already exists.
3205> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\objfre\amd64\_PASS1_Marker.log
3205> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\7726ce71621e698e0ad7e6c5e6e8a399\cl_1.rsp
3205>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3205>Copyright (C) Microsoft Corporation.  All rights reserved.
3205>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\objfre\amd64/"
3205>   /FC
3205>   /Iamd64
3205>   /I.
3205>   /Ie:\os\src\data\MSRC
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3205>   /Ie:\os\public\amd64fre\onecoreuap\restricted\windows\inc
3205>   /I..\common
3205>   /I..\llmclientlib
3205>   /I..\aimxsrv\inc
3205>   /I..\aimxsrv\server
3205>   /I..\aimxsrv\client
3205>   /I..\..\McpProtocolLib
3205>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\..\aimxsrv\idl\objfre\amd64
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\base\inc\appmodel\runtime\winrt
3205>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\objfre\amd64
3205>   /Ie:\os\src\onecore\ds\inc
3205>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3205>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3205>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3205>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3205>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3205>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3205>   /Ie:\os\public\amd64fre\shared\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3205>   /Ie:\os\public\amd64fre\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3205>   /Ie:\os\public\amd64fre\shared\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3205>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3205>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3205>   /Ie:\os\public\amd64fre\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3205>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3205>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3205>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3205>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3205>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3205>   /D_WIN64
3205>   /D_AMD64_
3205>   /DAMD64
3205>   /DCONDITION_HANDLING=1
3205>   /DNT_INST=0
3205>   /DWIN32=100
3205>   /D_NT1X_=100
3205>   /DWINNT=1
3205>   /D_WIN32_WINNT=0x0A00
3205>   /DWINVER=0x0A00
3205>   /D_WIN32_IE=0x0A00
3205>   /DWIN32_LEAN_AND_MEAN=1
3205>   /DDEVL=1
3205>   /DNDEBUG
3205>   /D_STL120_
3205>   /D_STL140_
3205>   /D_DLL=1
3205>   /D_MT=1
3205>   -DNT_IUM
3205>   -DWIN32
3205>   -D_WIN32
3205>   -DUNICODE
3205>   -D_UNICODE
3205>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3205>   /D_USE_DEV11_CRT
3205>   -D_APISET_MINWIN_VERSION=0x0115
3205>   -D_APISET_MINCORE_VERSION=0x0114
3205>   /DFE_SB
3205>   /DFE_IME
3205>   /DNTDDI_VERSION=0x0A000011
3205>   /DWINBLUE_KBSPRING14
3205>   /DBUILD_WINDOWS
3205>   /DUNDOCKED_WINDOWS_UCRT
3205>   /D__WRL_CONFIGURATION_LEGACY__
3205>   /DBUILD_UMS_ENABLED=1
3205>   /DBUILD_WOW64_ENABLED=1
3205>   /DBUILD_ARM64X_ENABLED=0
3205>   /DEXECUTABLE_WRITES_SUPPORT=0
3205>   -D_USE_DECLSPECS_FOR_SAL=1
3205>   -D__PLACEHOLDER_SAL=1
3205>   /c
3205>   /Zc:wchar_t-
3205>   /Zl
3205>   /Zp8
3205>   /Gy
3205>   /W4
3205>   /wd4244
3205>   /EHsc
3205>   /d1import_no_registry
3205>   /EHsc
3205>   /GR-
3205>   /GF
3205>   /GS
3205>   /Z7
3205>   /Oxs
3205>   /GL
3205>   /Z7
3205>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3205>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3205>   /w15043
3205>   /Zc:rvalueCast
3205>   -D_UCRT
3205>   -D_CONST_RETURN=
3205>   -D_CRT_SECURE_NO_WARNINGS
3205>   -D_CRT_NON_CONFORMING_SWPRINTFS
3205>   -D_CRT_NONSTDC_NO_WARNINGS
3205>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3205>   /D_CRT_STDIO_INLINE=extern
3205>   /D_NO_CRT_STDIO_INLINE
3205>   /D_ACRTIMP_ALT=
3205>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3205>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3205>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3205>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3205>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3205>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3205>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3205>   /D_HAS_STD_BYTE=0
3205>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3205>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3205>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3205>   /D_FULL_IOBUF
3205>   /d1initAll:Mask11
3205>   /d1initAll:FillPattern0
3205>   /d1nodatetime
3205>   /d1trimfile:e:\os\src\=BASEDIR
3205>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3205>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3205>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3205>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3205>   /d2AllowCompatibleILVersions
3205>   /d2Zi+
3205>   /ZH:SHA_256
3205>   /wd4986
3205>   /wd4987
3205>   /wd4471
3205>   /wd4369
3205>   /wd4309
3205>   /wd4754
3205>   /wd4427
3205>   /d2DeepThoughtInliner-
3205>   /d2implyavx512upperregs-
3205>   /Wv:19.23
3205>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\objfre\amd64\
3205>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3205>   /wl4002
3205>   /wl4003
3205>   /wl4005
3205>   /wl4006
3205>   /wl4007
3205>   /wl4008
3205>   /wl4010
3205>   /wl4013
3205>   /wl4015
3205>   /wl4018
3205>   /wl4020
3205>   /wl4022
3205>   /wl4024
3205>   /wl4025
3205>   /wl4026
3205>   /wl4027
3205>   /wl4028
3205>   /wl4029
3205>   /wl4030
3205>   /wl4031
3205>   /wl4033
3205>   /wl4034
3205>   /wl4036
3205>   /wl4038
3205>   /wl4041
3205>   /wl4042
3205>   /wl4045
3205>   /wl4047
3205>   /wl4048
3205>   /wl4049
3205>   /wl4056
3205>   /wl4066
3205>   /wl4067
3205>   /wl4068
3205>   /wl4073
3205>   /wl4074
3205>   /wl4075
3205>   /wl4076
3205>   /wl4077
3205>   /wl4079
3205>   /wl4080
3205>   /wl4081
3205>   /wl4083
3205>   /wl4085
3205>   /wl4086
3205>   /wl4087
3205>   /wl4088
3205>   /wl4089
3205>   /wl4090
3205>   /wl4091
3205>   /wl4094
3205>   /wl4096
3205>   /wl4097
3205>   /wl4098
3205>   /wl4099
3205>   /wl4101
3205>   /wl4102
3205>   /wl4109
3205>   /wl4112
3205>   /wl4113
3205>   /wl4114
3205>   /wl4115
3205>   /wl4116
3205>   /wl4117
3205>   /wl4119
3205>   /wl4120
3205>   /wl4122
3205>   /wl4124
3205>   /wl4129
3205>   /wl4133
3205>   /wl4138
3205>   /wl4141
3205>   /wl4142
3205>   /wl4143
3205>   /wl4144
3205>   /wl4145
3205>   /wl4150
3205>   /wl4153
3205>   /wl4154
3205>   /wl4155
3205>   /wl4156
3205>   /wl4157
3205>   /wl4158
3205>   /wl4159
3205>   /wl4160
3205>   /wl4161
3205>   /wl4162
3205>   /wl4163
3205>   /wl4164
3205>   /wl4166
3205>   /wl4167
3205>   /wl4168
3205>   /wl4172
3205>   /wl4174
3205>   /wl4175
3205>   /wl4176
3205>   /wl4177
3205>   /wl4178
3205>   /wl4180
3205>   /wl4182
3205>   /wl4183
3205>   /wl4185
3205>   /wl4186
3205>   /wl4187
3205>   /wl4190
3205>   /wl4192
3205>   /wl4197
3205>   /wl4200
3205>   /wl4213
3205>   /wl4215
3205>   /wl4216
3205>   /wl4218
3205>   /wl4223
3205>   /wl4224
3205>   /wl4226
3205>   /wl4227
3205>   /wl4228
3205>   /wl4229
3205>   /wl4230
3205>   /wl4237
3205>   /wl4240
3205>   /wl4243
3205>   /wl4244
3205>   /wl4250
3205>   /wl4251
3205>   /wl4258
3205>   /wl4267
3205>   /wl4269
3205>   /wl4272
3205>   /wl4273
3205>   /wl4274
3205>   /wl4275
3205>   /wl4276
3205>   /wl4278
3205>   /wl4280
3205>   /wl4281
3205>   /wl4282
3205>   /wl4283
3205>   /wl4285
3205>   /wl4286
3205>   /wl4288
3205>   /wl4290
3205>   /wl4291
3205>   /wl4293
3205>   /wl4297
3205>   /wl4302
3205>   /wl4305
3205>   /wl4306
3205>   /wl4307
3205>   /wl4309
3205>   /wl4310
3205>   /wl4311
3205>   /wl4312
3205>   /wl4313
3205>   /wl4316
3205>   /wl4319
3205>   /wl4325
3205>   /wl4326
3205>   /wl4329
3205>   /wl4333
3205>   /wl4334
3205>   /wl4335
3205>   /wl4340
3205>   /wl4344
3205>   /wl4346
3205>   /wl4348
3205>   /wl4353
3205>   /wl4356
3205>   /wl4357
3205>   /wl4358
3205>   /wl4359
3205>   /wl4364
3205>   /wl4368
3205>   /wl4369
3205>   /wl4373
3205>   /wl4374
3205>   /wl4375
3205>   /wl4376
3205>   /wl4377
3205>   /wl4378
3205>   /wl4379
3205>   /wl4381
3205>   /wl4382
3205>   /wl4383
3205>   /wl4384
3205>   /wl4390
3205>   /wl4391
3205>   /wl4392
3205>   /wl4393
3205>   /wl4394
3205>   /wl4395
3205>   /wl4396
3205>   /wl4397
3205>   /wl4398
3205>   /wl4399
3205>   /wl4600
3205>   /wl4401
3205>   /wl4402
3205>   /wl4403
3205>   /wl4404
3205>   /wl4405
3205>   /wl4406
3205>   /wl4407
3205>   /wl4409
3205>   /wl4410
3205>   /wl4411
3205>   /wl4414
3205>   /wl4420
3205>   /wl4430
3205>   /wl4436
3205>   /wl4439
3205>   /wl4440
3205>   /wl4441
3205>   /wl4445
3205>   /wl4461
3205>   /wl4462
3205>   /wl4470
3205>   /wl4473
3205>   /wl4477
3205>   /wl4484
3205>   /wl4485
3205>   /wl4486
3205>   /wl4488
3205>   /wl4489
3205>   /wl4490
3205>   /wl4502
3205>   /wl4503
3205>   /wl4506
3205>   /wl4508
3205>   /wl4511
3205>   /wl4518
3205>   /wl4521
3205>   /wl4522
3205>   /wl4523
3205>   /wl4526
3205>   /wl4530
3205>   /wl4534
3205>   /wl4535
3205>   /wl4537
3205>   /wl4538
3205>   /wl4540
3205>   /wl4541
3205>   /wl4543
3205>   /wl4544
3205>   /wl4550
3205>   /wl4551
3205>   /wl4552
3205>   /wl4553
3205>   /wl4554
3205>   /wl4556
3205>   /wl4558
3205>   /wl4561
3205>   /wl4566
3205>   /wl4570
3205>   /wl4572
3205>   /wl4580
3205>   /wl4581
3205>   /wl4584
3205>   /wl4596
3205>   /wl4597
3205>   /wl4602
3205>   /wl4603
3205>   /wl4606
3205>   /wl4612
3205>   /wl4613
3205>   /wl4615
3205>   /wl4616
3205>   /wl4618
3205>   /wl4620
3205>   /wl4621
3205>   /wl4622
3205>   /wl4624
3205>   /wl4627
3205>   /wl4630
3205>   /wl4632
3205>   /wl4633
3205>   /wl4635
3205>   /wl4636
3205>   /wl4637
3205>   /wl4638
3205>   /wl4641
3205>   /wl4645
3205>   /wl4646
3205>   /wl4650
3205>   /wl4651
3205>   /wl4652
3205>   /wl4653
3205>   /wl4655
3205>   /wl4656
3205>   /wl4657
3205>   /wl4659
3205>   /wl4661
3205>   /wl4662
3205>   /wl4667
3205>   /wl4669
3205>   /wl4674
3205>   /wl4677
3205>   /wl4678
3205>   /wl4679
3205>   /wl4683
3205>   /wl4684
3205>   /wl4685
3205>   /wl4687
3205>   /wl4688
3205>   /wl4691
3205>   /wl4693
3205>   /wl4694
3205>   /wl4698
3205>   /wl4711
3205>   /wl4715
3205>   /wl4716
3205>   /wl4717
3205>   /wl4722
3205>   /wl4723
3205>   /wl4724
3205>   /wl4727
3205>   /wl4730
3205>   /wl4731
3205>   /wl4733
3205>   /wl4739
3205>   /wl4742
3205>   /wl4743
3205>   /wl4744
3205>   /wl4747
3205>   /wl4750
3205>   /wl4756
3205>   /wl4768
3205>   /wl4772
3205>   /wl4788
3205>   /wl4793
3205>   /wl4794
3205>   /wl4799
3205>   /wl4803
3205>   /wl4804
3205>   /wl4805
3205>   /wl4806
3205>   /wl4807
3205>   /wl4810
3205>   /wl4811
3205>   /wl4812
3205>   /wl4813
3205>   /wl4817
3205>   /wl4819
3205>   /wl4821
3205>   /wl4823
3205>   /wl4829
3205>   /wl4834
3205>   /wl4835
3205>   /wl4838
3205>   /wl4839
3205>   /wl4867
3205>   /wl4900
3205>   /wl4910
3205>   /wl4912
3205>   /wl4920
3205>   /wl4925
3205>   /wl4926
3205>   /wl4927
3205>   /wl4929
3205>   /wl4930
3205>   /wl4935
3205>   /wl4936
3205>   /wl4939
3205>   /wl4944
3205>   /wl4945
3205>   /wl4947
3205>   /wl4948
3205>   /wl4949
3205>   /wl4950
3205>   /wl4951
3205>   /wl4952
3205>   /wl4953
3205>   /wl4956
3205>   /wl4957
3205>   /wl4958
3205>   /wl4959
3205>   /wl4961
3205>   /wl4964
3205>   /wl4965
3205>   /wl4972
3205>   /wl4984
3205>   /wl4995
3205>   /wl4996
3205>   /wl4997
3205>   /wl4999
3205>   /wl5033
3205>   /wl5037
3205>   /wl5046
3205>   /wl5050
3205>   /wl5055
3205>   /wl5056
3205>   /wl5105
3205>   /wl5208
3205>   /d2Qvec-mathlib-
3205>   /d2Qvec-sse2only
3205>   /Gw
3205>   /Zc:checkGwOdr
3205>   /d1ignorePragmaWarningError
3205>   /wd4316
3205>   /wd4973
3205>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3205>   /d2FH4
3205>   /Brepro
3205>   -D_HAS_MAGIC_STATICS=1
3205>   /Qspectre
3205>   /wd5045
3205>   /d2guardspecanalysismode:v1_0
3205>   /d2guardspecmode2
3205>   /guard:cf
3205>   /d2guardcfgfuncptr-
3205>   /d2guardcfgdispatch
3205>   /guard:ehcont
3205>   -D__PLACEHOLDER_SAL=1
3205>   -wd4425
3205>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3205>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3205>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3205>   /std:c++17
3205>   .\mcpjsonrpc.cpp .\mcpserverutils.cpp 
3205>mcpjsonrpc.cpp
3205>mcpserverutils.cpp
3205> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\objfre\amd64\McpProtocolLib.lib /IGNORE:4078,4221,4281,4006,4198   /nodefaultlib /machine:amd64 /ltcg /Brepro @e:\os\obj\amd64fre\temp\7726ce71621e698e0ad7e6c5e6e8a399\lib_1.rsp
3205>Microsoft (R) Library Manager Version 14.42.34444.100
3205>Copyright (C) Microsoft Corporation.  All rights reserved.
3205>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\objfre\amd64\mcpjsonrpc.obj 
3205>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\objfre\amd64\mcpserverutils.obj 
3205>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\objfre\amd64\Macros-PASS1.txt
3205>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\objfre\amd64\Macros-PASS1.txt
3205> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\mcpprotocollib" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3205>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\mcpprotocollib in pass PASS1
3209>Calculated LAYERINFO_MODULE='OneCoreDS'.
3209>makefile.def: TEMP=e:\os\obj\amd64fre\temp\f72003bd1b2ca2c3f400e4d0de6365e4
3209>makefile.def: BUILDINGINDATT=
3209>[Core OS Undocking] NOT using package ''
3209>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr' (target 'AdMcpSvr', type 'LIBRARY', nt_target_version '0xA000011')
3209>ObjectsMac.ts: validation succeeded
3209>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr" (STL_VER_TELEMETRY)
3209>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3209>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64 already exists.
3209> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\_PASS1_Marker.log
3209> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\f72003bd1b2ca2c3f400e4d0de6365e4\cl_1.rsp
3209>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3209>Copyright (C) Microsoft Corporation.  All rights reserved.
3209>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64/"
3209>   /FC
3209>   /Iamd64
3209>   /I.
3209>   /Ie:\os\src\data\MSRC
3209>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3209>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\BuildMetadata\internal\cppwinrt
3209>   /Ie:\os\public\amd64fre\onecoreuap\restricted\windows\inc
3209>   /I..\common
3209>   /I..\llmclientlib
3209>   /I..\aimxsrv\inc
3209>   /I..\aimxsrv\server
3209>   /I..\aimxsrv\client
3209>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\..\aimxsrv\idl\objfre\amd64
3209>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3209>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64
3209>   /Ie:\os\src\onecore\ds\inc
3209>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3209>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3209>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3209>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3209>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3209>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3209>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3209>   /Ie:\os\public\amd64fre\shared\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3209>   /Ie:\os\public\amd64fre\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3209>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3209>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3209>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3209>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3209>   /Ie:\os\public\amd64fre\shared\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3209>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3209>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3209>   /Ie:\os\public\amd64fre\internal\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3209>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3209>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3209>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3209>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3209>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3209>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3209>   /D_WIN64
3209>   /D_AMD64_
3209>   /DAMD64
3209>   /DCONDITION_HANDLING=1
3209>   /DNT_INST=0
3209>   /DWIN32=100
3209>   /D_NT1X_=100
3209>   /DWINNT=1
3209>   /D_WIN32_WINNT=0x0A00
3209>   /DWINVER=0x0A00
3209>   /D_WIN32_IE=0x0A00
3209>   /DWIN32_LEAN_AND_MEAN=1
3209>   /DDEVL=1
3209>   /DNDEBUG
3209>   /D_STL120_
3209>   /D_STL140_
3209>   /D_DLL=1
3209>   /D_MT=1
3209>   -DNT_IUM
3209>   -DWIN32
3209>   -D_WIN32
3209>   -DUNICODE
3209>   -D_UNICODE
3209>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3209>   /D_USE_DEV11_CRT
3209>   -D_APISET_MINWIN_VERSION=0x0115
3209>   -D_APISET_MINCORE_VERSION=0x0114
3209>   /DFE_SB
3209>   /DFE_IME
3209>   /DNTDDI_VERSION=0x0A000011
3209>   /DWINBLUE_KBSPRING14
3209>   /DBUILD_WINDOWS
3209>   /DUNDOCKED_WINDOWS_UCRT
3209>   /D__WRL_CONFIGURATION_LEGACY__
3209>   /DBUILD_UMS_ENABLED=1
3209>   /DBUILD_WOW64_ENABLED=1
3209>   /DBUILD_ARM64X_ENABLED=0
3209>   /DEXECUTABLE_WRITES_SUPPORT=0
3209>   -D_USE_DECLSPECS_FOR_SAL=1
3209>   /DRUN_WPP
3209>   -D__PLACEHOLDER_SAL=1
3209>   /c
3209>   /Zc:wchar_t-
3209>   /Zl
3209>   /Zp8
3209>   /Gy
3209>   /W4
3209>   /wd4244
3209>   /EHsc
3209>   /d1import_no_registry
3209>   /EHsc
3209>   /GR-
3209>   /GF
3209>   /GS
3209>   /Z7
3209>   /Oxs
3209>   /GL
3209>   /Z7
3209>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3209>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3209>   /w15043
3209>   /Zc:rvalueCast
3209>   -D_UCRT
3209>   -D_CONST_RETURN=
3209>   -D_CRT_SECURE_NO_WARNINGS
3209>   -D_CRT_NON_CONFORMING_SWPRINTFS
3209>   -D_CRT_NONSTDC_NO_WARNINGS
3209>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3209>   /D_CRT_STDIO_INLINE=extern
3209>   /D_NO_CRT_STDIO_INLINE
3209>   /D_ACRTIMP_ALT=
3209>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3209>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3209>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3209>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3209>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3209>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3209>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3209>   /D_HAS_STD_BYTE=0
3209>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3209>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3209>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3209>   /D_FULL_IOBUF
3209>   /d1initAll:Mask11
3209>   /d1initAll:FillPattern0
3209>   /d1nodatetime
3209>   /d1trimfile:e:\os\src\=BASEDIR
3209>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3209>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3209>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3209>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3209>   /d2AllowCompatibleILVersions
3209>   /d2Zi+
3209>   /ZH:SHA_256
3209>   /wd4986
3209>   /wd4987
3209>   /wd4471
3209>   /wd4369
3209>   /wd4309
3209>   /wd4754
3209>   /wd4427
3209>   /d2DeepThoughtInliner-
3209>   /d2implyavx512upperregs-
3209>   /Wv:19.23
3209>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\
3209>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3209>   /wl4002
3209>   /wl4003
3209>   /wl4005
3209>   /wl4006
3209>   /wl4007
3209>   /wl4008
3209>   /wl4010
3209>   /wl4013
3209>   /wl4015
3209>   /wl4018
3209>   /wl4020
3209>   /wl4022
3209>   /wl4024
3209>   /wl4025
3209>   /wl4026
3209>   /wl4027
3209>   /wl4028
3209>   /wl4029
3209>   /wl4030
3209>   /wl4031
3209>   /wl4033
3209>   /wl4034
3209>   /wl4036
3209>   /wl4038
3209>   /wl4041
3209>   /wl4042
3209>   /wl4045
3209>   /wl4047
3209>   /wl4048
3209>   /wl4049
3209>   /wl4056
3209>   /wl4066
3209>   /wl4067
3209>   /wl4068
3209>   /wl4073
3209>   /wl4074
3209>   /wl4075
3209>   /wl4076
3209>   /wl4077
3209>   /wl4079
3209>   /wl4080
3209>   /wl4081
3209>   /wl4083
3209>   /wl4085
3209>   /wl4086
3209>   /wl4087
3209>   /wl4088
3209>   /wl4089
3209>   /wl4090
3209>   /wl4091
3209>   /wl4094
3209>   /wl4096
3209>   /wl4097
3209>   /wl4098
3209>   /wl4099
3209>   /wl4101
3209>   /wl4102
3209>   /wl4109
3209>   /wl4112
3209>   /wl4113
3209>   /wl4114
3209>   /wl4115
3209>   /wl4116
3209>   /wl4117
3209>   /wl4119
3209>   /wl4120
3209>   /wl4122
3209>   /wl4124
3209>   /wl4129
3209>   /wl4133
3209>   /wl4138
3209>   /wl4141
3209>   /wl4142
3209>   /wl4143
3209>   /wl4144
3209>   /wl4145
3209>   /wl4150
3209>   /wl4153
3209>   /wl4154
3209>   /wl4155
3209>   /wl4156
3209>   /wl4157
3209>   /wl4158
3209>   /wl4159
3209>   /wl4160
3209>   /wl4161
3209>   /wl4162
3209>   /wl4163
3209>   /wl4164
3209>   /wl4166
3209>   /wl4167
3209>   /wl4168
3209>   /wl4172
3209>   /wl4174
3209>   /wl4175
3209>   /wl4176
3209>   /wl4177
3209>   /wl4178
3209>   /wl4180
3209>   /wl4182
3209>   /wl4183
3209>   /wl4185
3209>   /wl4186
3209>   /wl4187
3209>   /wl4190
3209>   /wl4192
3209>   /wl4197
3209>   /wl4200
3209>   /wl4213
3209>   /wl4215
3209>   /wl4216
3209>   /wl4218
3209>   /wl4223
3209>   /wl4224
3209>   /wl4226
3209>   /wl4227
3209>   /wl4228
3209>   /wl4229
3209>   /wl4230
3209>   /wl4237
3209>   /wl4240
3209>   /wl4243
3209>   /wl4244
3209>   /wl4250
3209>   /wl4251
3209>   /wl4258
3209>   /wl4267
3209>   /wl4269
3209>   /wl4272
3209>   /wl4273
3209>   /wl4274
3209>   /wl4275
3209>   /wl4276
3209>   /wl4278
3209>   /wl4280
3209>   /wl4281
3209>   /wl4282
3209>   /wl4283
3209>   /wl4285
3209>   /wl4286
3209>   /wl4288
3209>   /wl4290
3209>   /wl4291
3209>   /wl4293
3209>   /wl4297
3209>   /wl4302
3209>   /wl4305
3209>   /wl4306
3209>   /wl4307
3209>   /wl4309
3209>   /wl4310
3209>   /wl4311
3209>   /wl4312
3209>   /wl4313
3209>   /wl4316
3209>   /wl4319
3209>   /wl4325
3209>   /wl4326
3209>   /wl4329
3209>   /wl4333
3209>   /wl4334
3209>   /wl4335
3209>   /wl4340
3209>   /wl4344
3209>   /wl4346
3209>   /wl4348
3209>   /wl4353
3209>   /wl4356
3209>   /wl4357
3209>   /wl4358
3209>   /wl4359
3209>   /wl4364
3209>   /wl4368
3209>   /wl4369
3209>   /wl4373
3209>   /wl4374
3209>   /wl4375
3209>   /wl4376
3209>   /wl4377
3209>   /wl4378
3209>   /wl4379
3209>   /wl4381
3209>   /wl4382
3209>   /wl4383
3209>   /wl4384
3209>   /wl4390
3209>   /wl4391
3209>   /wl4392
3209>   /wl4393
3209>   /wl4394
3209>   /wl4395
3209>   /wl4396
3209>   /wl4397
3209>   /wl4398
3209>   /wl4399
3209>   /wl4600
3209>   /wl4401
3209>   /wl4402
3209>   /wl4403
3209>   /wl4404
3209>   /wl4405
3209>   /wl4406
3209>   /wl4407
3209>   /wl4409
3209>   /wl4410
3209>   /wl4411
3209>   /wl4414
3209>   /wl4420
3209>   /wl4430
3209>   /wl4436
3209>   /wl4439
3209>   /wl4440
3209>   /wl4441
3209>   /wl4445
3209>   /wl4461
3209>   /wl4462
3209>   /wl4470
3209>   /wl4473
3209>   /wl4477
3209>   /wl4484
3209>   /wl4485
3209>   /wl4486
3209>   /wl4488
3209>   /wl4489
3209>   /wl4490
3209>   /wl4502
3209>   /wl4503
3209>   /wl4506
3209>   /wl4508
3209>   /wl4511
3209>   /wl4518
3209>   /wl4521
3209>   /wl4522
3209>   /wl4523
3209>   /wl4526
3209>   /wl4530
3209>   /wl4534
3209>   /wl4535
3209>   /wl4537
3209>   /wl4538
3209>   /wl4540
3209>   /wl4541
3209>   /wl4543
3209>   /wl4544
3209>   /wl4550
3209>   /wl4551
3209>   /wl4552
3209>   /wl4553
3209>   /wl4554
3209>   /wl4556
3209>   /wl4558
3209>   /wl4561
3209>   /wl4566
3209>   /wl4570
3209>   /wl4572
3209>   /wl4580
3209>   /wl4581
3209>   /wl4584
3209>   /wl4596
3209>   /wl4597
3209>   /wl4602
3209>   /wl4603
3209>   /wl4606
3209>   /wl4612
3209>   /wl4613
3209>   /wl4615
3209>   /wl4616
3209>   /wl4618
3209>   /wl4620
3209>   /wl4621
3209>   /wl4622
3209>   /wl4624
3209>   /wl4627
3209>   /wl4630
3209>   /wl4632
3209>   /wl4633
3209>   /wl4635
3209>   /wl4636
3209>   /wl4637
3209>   /wl4638
3209>   /wl4641
3209>   /wl4645
3209>   /wl4646
3209>   /wl4650
3209>   /wl4651
3209>   /wl4652
3209>   /wl4653
3209>   /wl4655
3209>   /wl4656
3209>   /wl4657
3209>   /wl4659
3209>   /wl4661
3209>   /wl4662
3209>   /wl4667
3209>   /wl4669
3209>   /wl4674
3209>   /wl4677
3209>   /wl4678
3209>   /wl4679
3209>   /wl4683
3209>   /wl4684
3209>   /wl4685
3209>   /wl4687
3209>   /wl4688
3209>   /wl4691
3209>   /wl4693
3209>   /wl4694
3209>   /wl4698
3209>   /wl4711
3209>   /wl4715
3209>   /wl4716
3209>   /wl4717
3209>   /wl4722
3209>   /wl4723
3209>   /wl4724
3209>   /wl4727
3209>   /wl4730
3209>   /wl4731
3209>   /wl4733
3209>   /wl4739
3209>   /wl4742
3209>   /wl4743
3209>   /wl4744
3209>   /wl4747
3209>   /wl4750
3209>   /wl4756
3209>   /wl4768
3209>   /wl4772
3209>   /wl4788
3209>   /wl4793
3209>   /wl4794
3209>   /wl4799
3209>   /wl4803
3209>   /wl4804
3209>   /wl4805
3209>   /wl4806
3209>   /wl4807
3209>   /wl4810
3209>   /wl4811
3209>   /wl4812
3209>   /wl4813
3209>   /wl4817
3209>   /wl4819
3209>   /wl4821
3209>   /wl4823
3209>   /wl4829
3209>   /wl4834
3209>   /wl4835
3209>   /wl4838
3209>   /wl4839
3209>   /wl4867
3209>   /wl4900
3209>   /wl4910
3209>   /wl4912
3209>   /wl4920
3209>   /wl4925
3209>   /wl4926
3209>   /wl4927
3209>   /wl4929
3209>   /wl4930
3209>   /wl4935
3209>   /wl4936
3209>   /wl4939
3209>   /wl4944
3209>   /wl4945
3209>   /wl4947
3209>   /wl4948
3209>   /wl4949
3209>   /wl4950
3209>   /wl4951
3209>   /wl4952
3209>   /wl4953
3209>   /wl4956
3209>   /wl4957
3209>   /wl4958
3209>   /wl4959
3209>   /wl4961
3209>   /wl4964
3209>   /wl4965
3209>   /wl4972
3209>   /wl4984
3209>   /wl4995
3209>   /wl4996
3209>   /wl4997
3209>   /wl4999
3209>   /wl5033
3209>   /wl5037
3209>   /wl5046
3209>   /wl5050
3209>   /wl5055
3209>   /wl5056
3209>   /wl5105
3209>   /wl5208
3209>   /d2Qvec-mathlib-
3209>   /d2Qvec-sse2only
3209>   /Gw
3209>   /Zc:checkGwOdr
3209>   /d1ignorePragmaWarningError
3209>   /wd4316
3209>   /wd4973
3209>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3209>   /d2FH4
3209>   /Brepro
3209>   -D_HAS_MAGIC_STATICS=1
3209>   /Qspectre
3209>   /wd5045
3209>   /d2guardspecanalysismode:v1_0
3209>   /d2guardspecmode2
3209>   /guard:cf
3209>   /d2guardcfgfuncptr-
3209>   /d2guardcfgdispatch
3209>   /guard:ehcont
3209>   -D__PLACEHOLDER_SAL=1
3209>   -wd4425
3209>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3209>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3209>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3209>   /std:c++17
3209>   .\admcpsvr.cpp .\powershellfilterparser.cpp .\getadusertool.cpp .\getadgrouptool.cpp .\getadcomputertool.cpp .\getaddomaintool.cpp .\getadforesttool.cpp 
3209>admcpsvr.cpp
3209>powershellfilterparser.cpp
3209>getadusertool.cpp
3209>getadgrouptool.cpp
3209>getadcomputertool.cpp
3209>getaddomaintool.cpp
3209>getadforesttool.cpp
3209> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\AdMcpSvr.lib /IGNORE:4078,4221,4281,4006,4198   /nodefaultlib /machine:amd64 /ltcg /Brepro @e:\os\obj\amd64fre\temp\f72003bd1b2ca2c3f400e4d0de6365e4\lib_1.rsp
3209>Microsoft (R) Library Manager Version 14.42.34444.100
3209>Copyright (C) Microsoft Corporation.  All rights reserved.
3209>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\admcpsvr.obj 
3209>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\powershellfilterparser.obj 
3209>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\getadusertool.obj 
3209>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\getadgrouptool.obj 
3209>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\getadcomputertool.obj 
3209>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\getaddomaintool.obj 
3209>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\getadforesttool.obj 
3209>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\Macros-PASS1.txt
3209>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\Macros-PASS1.txt
3209> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\admcpsvr" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3209>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\admcpsvr in pass PASS1
3207>[33;1mWARNING: Deprecated version found: 4.0, changing back to 12.0[0m
3207>c:\windows\system32\cmd.exe /c E:\os\tools\wines_msbuild\_lowercd.cmd e:\os\src\tools\urtrun.cmd 4.Latest E:\os\tools\wines_msbuild\MSBuild\Current\Bin\MSBuild.exe -toolsVersion:12.0 vcpkg.proj -NoLogo /clp:NoSummary /p:BuildingWithBuildExe=true /p:BuildingInSeparatePasses=true /p:Pass=Compile /p:THREAD_ID=7 /p:TARGET_PLATFORM=windows /p:CODE_SETS_BUILDING=cs_windows:cs_xbox:cs_phone /p:ObjectPath=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\ /p:CODE_SETS_TAGGED=cs_windows:cs_xbox /v:normal /t:BuildCompiled  
3207>Build started 7/24/2025 9:41:10 PM.
3207>Project "e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk\vcpkg.proj" on node 1 (BuildCompiled target(s)).
3207>RunVCPkg:
3207>  Removing directory "e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_empty".
3207>  Creating directory "e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_empty".
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.usedownloadcache
3207>  true
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.prepopulatedownloadcache
3207>  true
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.boosthangdetectionseconds
3207>  2400
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.componentconfigurationtimeout
3207>  7200
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.usebinarycache
3207>  true
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.logfilehashes
3207>  false
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.registerwithcg
3207>  false
3207>  e:\os\src\tools\vcpkg\printConfigValue.bat vcpkg.usenugetcache
3207>  true
3207>  Hashed 26276722 bytes across 729 files in 1799ms: a906ac503da87aaf54003edcdcf982c550aa6e18ff34a24a83fed3295db4af76d27e20bc11cd0a9ad741fb4d044e7d2aa73617ae2f5031d3e3124d7c38d03efd
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\cpprestsdk
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\3.30.1\CompilerIdC\tmp
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\3.30.1\CompilerIdC
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\3.30.1\CompilerIdCXX\tmp
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\3.30.1\CompilerIdCXX
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\3.30.1
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\pkgRedirects
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles\ShowIncludes
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\CMakeFiles
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel\vcpkg-parallel-configure
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler\target-windows-rel
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees\detect_compiler
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_buildtrees
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\bin
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmcppdap
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmcurl
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmlibarchive
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmliblzma
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmlibrhash
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmlibuv
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmnghttp2
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmsys
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmzlib
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\cmzstd
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\command
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\cpack_gen
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\envvar
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\generator
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\guide\ide-integration
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\guide\importing-exporting
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\guide\tutorial
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\guide\user-interaction
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\guide\using-dependencies
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\guide
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\manual
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\module
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\policy
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_cache
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_dir
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_gbl
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_inst
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_sf
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_test
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\prop_tgt
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\release
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\variable
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_downloads\3e2d73bff478d88a7de0de736ba5e361
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_downloads
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_images
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\command
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\cpack_gen
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\envvar
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\generator
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\guide\ide-integration
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\guide\importing-exporting
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\guide\tutorial
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\guide\user-interaction
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\guide\using-dependencies
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\guide
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\manual
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\module
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\policy
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_cache
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_dir
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_gbl
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_inst
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_sf
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_test
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\prop_tgt
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\release
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources\variable
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_sources
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html\_static
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake\html
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc\cmake
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\doc
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\man\man1
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\man\man7
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\man
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\aclocal
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\bash-completion\completions
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\bash-completion
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\command
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\cpack_gen
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\envvar
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\generator
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\include
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\manual\presets
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\manual
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\module
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\policy
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_cache
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_dir
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_gbl
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_inst
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_sf
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_test
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\prop_tgt
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\release
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help\variable
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Help
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\include
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Licenses
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\AndroidTestUtilities
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CheckIPOSupported
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CMakeAddFortranSubdirectory
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler\XL-Fortran
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Compiler
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\CompilerId
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\ExternalProject
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FetchContent
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindCUDA
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindMPI
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FindPython
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FortranCInterface\Verify
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\FortranCInterface
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\IntelVSImplicitPath
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CPack\WIX-v3
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\CPack
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal\OSRelease
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Internal
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\AIX
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform\Android
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\Platform
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\UseJava
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules\UseSWIG
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Modules
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Templates\MSBuild\FlagTables
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Templates\MSBuild
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Templates\Windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30\Templates
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\cmake-3.30
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\emacs\site-lisp
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\emacs
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\vim\vimfiles\indent
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\vim\vimfiles\syntax
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\vim\vimfiles
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share\vim
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386\share
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows\cmake-3.30.1-windows-i386
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\cmake-3.30.1-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\ninja\1.12.1-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\ninja
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools\nuget-6.10.0-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads\tools
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_downloads
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include\cpprest\details
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include\cpprest
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include\pplx
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\lib
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\share\cpprestsdk
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\share
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\vcpkg\info
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\vcpkg\updates
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\vcpkg
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\include\cpprest\details
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\include\cpprest
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\include\pplx
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\include
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\lib
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\share\cpprestsdk
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows\share
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\cpprestsdk_target-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages\detect_compiler_target-windows
3207>  Deleting Directory: e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_packages
3207>  BUILDMSG VCPkg is building the following packages:
3207>  BUILDMSG    cpprestsdk 2.10.18 [core] (for target); port directory = tools\vcpkg\overlay-ports\cpprestsdk
3207>  BUILDMSG Starting package 1/1: cpprestsdk:target-windows@2.10.18...
3207>  VCPkg Performance:
3207>  ReadTransferCount: 269762698 bytes
3207>  ReadOperationCount: 81058 bytes
3207>  WriteTransferCount: 278956398 bytes
3207>  WriteOperationCount: 16434 bytes
3207>  PeakJobMemoryUsed: 410107904 bytes
3207>  PeakProcessMemoryUsed: 212631552 bytes
3207>  TotalkernelTime: 81093750
3207>  TotalUserTime: 61718750
3207>  TotalProcesses: 56
3207>  TotalTerminatedProcesses: 0
3207>  Time Taken to to run vcpkg.exe: 17.2449207s
3207>  Disk space used by vcpkg: 338060010 bytes
3207>  Complete VCPkg build log is in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_logs\VCPkgBuild.log
3207>   -:DEST VCPkg\onecore\ds\ds\src\aimx\prod\cpprestsdk e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_logs\*.*
3207>  The system cannot find the drive specified.
3207>   -:DEST 3rdPartyCatalogs\onecore\ds\ds\src\aimx\prod\cpprestsdk e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg.cat.json
3207>  The system cannot find the drive specified.
3207>  Removing directory "e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_spdx".
3207>  Creating directory "e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_spdx".
3207>   -:DEST evidence\VCPkg\onecore\ds\ds\src\aimx\prod\cpprestsdk e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_spdx\*.*
3207>  The system cannot find the drive specified.
3207>  Removing directory "e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_cg".
3207>  Creating directory "e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_cg".
3207>  Allow-list check succeeded
3207>Done Building Project "e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk\vcpkg.proj" (BuildCompiled target(s)).
1>BUILDC (PipeSpawn): e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /no  1>[0:00:41.610] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll {37}
3201>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
3201>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll *************
3201>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  2>[0:00:41.610] [Pass1 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server {38}
3202>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3202>Compiling e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server *************
3202>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\server TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
BUILD: (ActiveWorkLoad),41.39,,56,1,1,11,16,0,0,9,0,0,0,0,PASS1,0,2000000000,<*> onecore\ds\ds\src\aimx\prod\aimxsrv\dll,RUPO-DELL
BUILD: (ActiveWorkLoad),41.39,,56,1,1,11,16,16,0,10,1,1,0,0,PASS1,0,2000000000,<*> onecore\ds\ds\src\aimx\prod\aimxsrv\server,RUPO-DELL
3201>Calculated LAYERINFO_MODULE='OneCoreDS'.
3201>makefile.def: TEMP=e:\os\obj\amd64fre\temp\b22d16938d8e04ecc0b72731656fddbe
3201>makefile.def: BUILDINGINDATT=
3201>[Core OS Undocking] NOT using package ''
3201>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll' (target 'aimxsrv', type 'DYNLINK', nt_target_version '0xA000011')
3201>ObjectsMac.ts: validation succeeded
3201>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll" (STL_VER_TELEMETRY)
3201>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3201>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64 already exists.
3201> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\_PASS1_Marker.log
3201> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\b22d16938d8e04ecc0b72731656fddbe\tmp_9648_1753418495900208700.tmp /Tpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch_hdr.src
3201>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>cl 
3201>   /Iamd64
3201>   /I.
3201>   /Ie:\os\src\data\MSRC
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3201>   /I..\..\common
3201>   /I..\..\common\nlohmann
3201>   /I..\..\McpProtocolLib
3201>   /I..\client
3201>   /I..\server
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\idl\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\security\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3201>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3201>   /Ie:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3201>   /Ie:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\inc
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\com\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3201>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3201>   /Ie:\os\public\amd64fre\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3201>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /D_WIN64
3201>   /D_AMD64_
3201>   /DAMD64
3201>   /DCONDITION_HANDLING=1
3201>   /DNT_INST=0
3201>   /DWIN32=100
3201>   /D_NT1X_=100
3201>   /DWINNT=1
3201>   /D_WIN32_WINNT=0x0A00
3201>   /DWINVER=0x0A00
3201>   /D_WIN32_IE=0x0A00
3201>   /DWIN32_LEAN_AND_MEAN=1
3201>   /DDEVL=1
3201>   /DNDEBUG
3201>   /D_STL120_
3201>   /D_STL140_
3201>   /D_DLL=1
3201>   /D_MT=1
3201>   -DNT_IUM
3201>   -DWIN32
3201>   -D_WIN32
3201>   -DUNICODE
3201>   -D_UNICODE
3201>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3201>   /D_USE_DEV11_CRT
3201>   -D_APISET_MINWIN_VERSION=0x0115
3201>   -D_APISET_MINCORE_VERSION=0x0114
3201>   /DFE_SB
3201>   /DFE_IME
3201>   /DNTDDI_VERSION=0x0A000011
3201>   /DWINBLUE_KBSPRING14
3201>   /DBUILD_WINDOWS
3201>   /DUNDOCKED_WINDOWS_UCRT
3201>   /D__WRL_CONFIGURATION_LEGACY__
3201>   /DBUILD_UMS_ENABLED=1
3201>   /DBUILD_WOW64_ENABLED=1
3201>   /DBUILD_ARM64X_ENABLED=0
3201>   /DEXECUTABLE_WRITES_SUPPORT=0
3201>   -D_USE_DECLSPECS_FOR_SAL=1
3201>   /DRUN_WPP
3201>   -DUNLOADABLE_DELAYLOAD_IMPLEMENTATION
3201>   -D__PLACEHOLDER_SAL=1
3201>   /D_ATL_STATIC_REGISTRY
3201>   /D_WINDLL
3201>   /c
3201>   /Zc:wchar_t-
3201>   /Zl
3201>   /Zp8
3201>   /Gy
3201>   /W4
3201>   /wd4244
3201>   /d1import_no_registry
3201>   /EHsc
3201>   /GR-
3201>   /GF
3201>   /GS
3201>   /Z7
3201>   /Oxs
3201>   /GL
3201>   /Z7
3201>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3201>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3201>   /w15043
3201>   /Zc:rvalueCast
3201>   /Zo
3201>   -D_UCRT
3201>   -D_CONST_RETURN=
3201>   -D_CRT_SECURE_NO_WARNINGS
3201>   -D_CRT_NON_CONFORMING_SWPRINTFS
3201>   -D_CRT_NONSTDC_NO_WARNINGS
3201>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3201>   /D_CRT_STDIO_INLINE=extern
3201>   /D_NO_CRT_STDIO_INLINE
3201>   /D_ACRTIMP_ALT=
3201>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3201>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3201>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3201>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3201>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_HAS_STD_BYTE=0
3201>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_FULL_IOBUF
3201>   /d1initAll:Mask11
3201>   /d1initAll:FillPattern0
3201>   /d1nodatetime
3201>   /d1trimfile:e:\os\src\=BASEDIR
3201>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3201>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3201>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3201>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3201>   /d2AllowCompatibleILVersions
3201>   /d2Zi+
3201>   /ZH:SHA_256
3201>   /wd4986
3201>   /wd4987
3201>   /wd4471
3201>   /wd4369
3201>   /wd4309
3201>   /wd4754
3201>   /wd4427
3201>   /d2DeepThoughtInliner-
3201>   /d2implyavx512upperregs-
3201>   /Wv:19.23
3201>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\
3201>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3201>   /wl4002
3201>   /wl4003
3201>   /wl4005
3201>   /wl4006
3201>   /wl4007
3201>   /wl4008
3201>   /wl4010
3201>   /wl4013
3201>   /wl4015
3201>   /wl4018
3201>   /wl4020
3201>   /wl4022
3201>   /wl4024
3201>   /wl4025
3201>   /wl4026
3201>   /wl4027
3201>   /wl4028
3201>   /wl4029
3201>   /wl4030
3201>   /wl4031
3201>   /wl4033
3201>   /wl4034
3201>   /wl4036
3201>   /wl4038
3201>   /wl4041
3201>   /wl4042
3201>   /wl4045
3201>   /wl4047
3201>   /wl4048
3201>   /wl4049
3201>   /wl4056
3201>   /wl4066
3201>   /wl4067
3201>   /wl4068
3201>   /wl4073
3201>   /wl4074
3201>   /wl4075
3201>   /wl4076
3201>   /wl4077
3201>   /wl4079
3201>   /wl4080
3201>   /wl4081
3201>   /wl4083
3201>   /wl4085
3201>   /wl4086
3201>   /wl4087
3201>   /wl4088
3201>   /wl4089
3201>   /wl4090
3201>   /wl4091
3201>   /wl4094
3201>   /wl4096
3201>   /wl4097
3201>   /wl4098
3201>   /wl4099
3201>   /wl4101
3201>   /wl4102
3201>   /wl4109
3201>   /wl4112
3201>   /wl4113
3201>   /wl4114
3201>   /wl4115
3201>   /wl4116
3201>   /wl4117
3201>   /wl4119
3201>   /wl4120
3201>   /wl4122
3201>   /wl4124
3201>   /wl4129
3201>   /wl4133
3201>   /wl4138
3201>   /wl4141
3201>   /wl4142
3201>   /wl4143
3201>   /wl4144
3201>   /wl4145
3201>   /wl4150
3201>   /wl4153
3201>   /wl4154
3201>   /wl4155
3201>   /wl4156
3201>   /wl4157
3201>   /wl4158
3201>   /wl4159
3201>   /wl4160
3201>   /wl4161
3201>   /wl4162
3201>   /wl4163
3201>   /wl4164
3201>   /wl4166
3201>   /wl4167
3201>   /wl4168
3201>   /wl4172
3201>   /wl4174
3201>   /wl4175
3201>   /wl4176
3201>   /wl4177
3201>   /wl4178
3201>   /wl4180
3201>   /wl4182
3201>   /wl4183
3201>   /wl4185
3201>   /wl4186
3201>   /wl4187
3201>   /wl4190
3201>   /wl4192
3201>   /wl4197
3201>   /wl4200
3201>   /wl4213
3201>   /wl4215
3201>   /wl4216
3201>   /wl4218
3201>   /wl4223
3201>   /wl4224
3201>   /wl4226
3201>   /wl4227
3201>   /wl4228
3201>   /wl4229
3201>   /wl4230
3201>   /wl4237
3201>   /wl4240
3201>   /wl4243
3201>   /wl4244
3201>   /wl4250
3201>   /wl4251
3201>   /wl4258
3201>   /wl4267
3201>   /wl4269
3201>   /wl4272
3201>   /wl4273
3201>   /wl4274
3201>   /wl4275
3201>   /wl4276
3201>   /wl4278
3201>   /wl4280
3201>   /wl4281
3201>   /wl4282
3201>   /wl4283
3201>   /wl4285
3201>   /wl4286
3201>   /wl4288
3201>   /wl4290
3201>   /wl4291
3201>   /wl4293
3201>   /wl4297
3201>   /wl4302
3201>   /wl4305
3201>   /wl4306
3201>   /wl4307
3201>   /wl4309
3201>   /wl4310
3201>   /wl4311
3201>   /wl4312
3201>   /wl4313
3201>   /wl4316
3201>   /wl4319
3201>   /wl4325
3201>   /wl4326
3201>   /wl4329
3201>   /wl4333
3201>   /wl4334
3201>   /wl4335
3201>   /wl4340
3201>   /wl4344
3201>   /wl4346
3201>   /wl4348
3201>   /wl4353
3201>   /wl4356
3201>   /wl4357
3201>   /wl4358
3201>   /wl4359
3201>   /wl4364
3201>   /wl4368
3201>   /wl4369
3201>   /wl4373
3201>   /wl4374
3201>   /wl4375
3201>   /wl4376
3201>   /wl4377
3201>   /wl4378
3201>   /wl4379
3201>   /wl4381
3201>   /wl4382
3201>   /wl4383
3201>   /wl4384
3201>   /wl4390
3201>   /wl4391
3201>   /wl4392
3201>   /wl4393
3201>   /wl4394
3201>   /wl4395
3201>   /wl4396
3201>   /wl4397
3201>   /wl4398
3201>   /wl4399
3201>   /wl4600
3201>   /wl4401
3201>   /wl4402
3201>   /wl4403
3201>   /wl4404
3201>   /wl4405
3201>   /wl4406
3201>   /wl4407
3201>   /wl4409
3201>   /wl4410
3201>   /wl4411
3201>   /wl4414
3201>   /wl4420
3201>   /wl4430
3201>   /wl4436
3201>   /wl4439
3201>   /wl4440
3201>   /wl4441
3201>   /wl4445
3201>   /wl4461
3201>   /wl4462
3201>   /wl4470
3201>   /wl4473
3201>   /wl4477
3201>   /wl4484
3201>   /wl4485
3201>   /wl4486
3201>   /wl4488
3201>   /wl4489
3201>   /wl4490
3201>   /wl4502
3201>   /wl4503
3201>   /wl4506
3201>   /wl4508
3201>   /wl4511
3201>   /wl4518
3201>   /wl4521
3201>   /wl4522
3201>   /wl4523
3201>   /wl4526
3201>   /wl4530
3201>   /wl4534
3201>   /wl4535
3201>   /wl4537
3201>   /wl4538
3201>   /wl4540
3201>   /wl4541
3201>   /wl4543
3201>   /wl4544
3201>   /wl4550
3201>   /wl4551
3201>   /wl4552
3201>   /wl4553
3201>   /wl4554
3201>   /wl4556
3201>   /wl4558
3201>   /wl4561
3201>   /wl4566
3201>   /wl4570
3201>   /wl4572
3201>   /wl4580
3201>   /wl4581
3201>   /wl4584
3201>   /wl4596
3201>   /wl4597
3201>   /wl4602
3201>   /wl4603
3201>   /wl4606
3201>   /wl4612
3201>   /wl4613
3201>   /wl4615
3201>   /wl4616
3201>   /wl4618
3201>   /wl4620
3201>   /wl4621
3201>   /wl4622
3201>   /wl4624
3201>   /wl4627
3201>   /wl4630
3201>   /wl4632
3201>   /wl4633
3201>   /wl4635
3201>   /wl4636
3201>   /wl4637
3201>   /wl4638
3201>   /wl4641
3201>   /wl4645
3201>   /wl4646
3201>   /wl4650
3201>   /wl4651
3201>   /wl4652
3201>   /wl4653
3201>   /wl4655
3201>   /wl4656
3201>   /wl4657
3201>   /wl4659
3201>   /wl4661
3201>   /wl4662
3201>   /wl4667
3201>   /wl4669
3201>   /wl4674
3201>   /wl4677
3201>   /wl4678
3201>   /wl4679
3201>   /wl4683
3201>   /wl4684
3201>   /wl4685
3201>   /wl4687
3201>   /wl4688
3201>   /wl4691
3201>   /wl4693
3201>   /wl4694
3201>   /wl4698
3201>   /wl4711
3201>   /wl4715
3201>   /wl4716
3201>   /wl4717
3201>   /wl4722
3201>   /wl4723
3201>   /wl4724
3201>   /wl4727
3201>   /wl4730
3201>   /wl4731
3201>   /wl4733
3201>   /wl4739
3201>   /wl4742
3201>   /wl4743
3201>   /wl4744
3201>   /wl4747
3201>   /wl4750
3201>   /wl4756
3201>   /wl4768
3201>   /wl4772
3201>   /wl4788
3201>   /wl4793
3201>   /wl4794
3201>   /wl4799
3201>   /wl4803
3201>   /wl4804
3201>   /wl4805
3201>   /wl4806
3201>   /wl4807
3201>   /wl4810
3201>   /wl4811
3201>   /wl4812
3201>   /wl4813
3201>   /wl4817
3201>   /wl4819
3201>   /wl4821
3201>   /wl4823
3201>   /wl4829
3201>   /wl4834
3201>   /wl4835
3201>   /wl4838
3201>   /wl4839
3201>   /wl4867
3201>   /wl4900
3201>   /wl4910
3201>   /wl4912
3201>   /wl4920
3201>   /wl4925
3201>   /wl4926
3201>   /wl4927
3201>   /wl4929
3201>   /wl4930
3201>   /wl4935
3201>   /wl4936
3201>   /wl4939
3201>   /wl4944
3201>   /wl4945
3201>   /wl4947
3201>   /wl4948
3201>   /wl4949
3201>   /wl4950
3201>   /wl4951
3201>   /wl4952
3201>   /wl4953
3201>   /wl4956
3201>   /wl4957
3201>   /wl4958
3201>   /wl4959
3201>   /wl4961
3201>   /wl4964
3201>   /wl4965
3201>   /wl4972
3201>   /wl4984
3201>   /wl4995
3201>   /wl4996
3201>   /wl4997
3201>   /wl4999
3201>   /wl5033
3201>   /wl5037
3201>   /wl5046
3201>   /wl5050
3201>   /wl5055
3201>   /wl5056
3201>   /wl5105
3201>   /wl5208
3201>   /d2Qvec-mathlib-
3201>   /d2Qvec-sse2only
3201>   /Gw
3201>   /Zc:checkGwOdr
3201>   /d1ignorePragmaWarningError
3201>   /wd4316
3201>   /wd4973
3201>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3201>   /d2FH4
3201>   /Brepro
3201>   -D_HAS_MAGIC_STATICS=1
3201>   /Qspectre
3201>   /wd5045
3201>   /d2guardspecanalysismode:v1_0
3201>   /d2guardspecmode2
3201>   /guard:cf
3201>   /d2guardcfgfuncptr-
3201>   /d2guardcfgdispatch
3201>   /guard:ehcont
3201>   -D__PLACEHOLDER_SAL=1
3201>   -wd4425
3201>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3201>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3201>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3201>   /std:c++17
3201>   /Ylaimxsrv
3201>   /Ycpch.hxx
3201>   /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch.pch
3201>   /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch.obj"
3201>pch_hdr.src
3201> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\b22d16938d8e04ecc0b72731656fddbe\cl_1.rsp
3201>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64/"
3201>   /FC
3201>   /Iamd64
3201>   /I.
3201>   /Ie:\os\src\data\MSRC
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3201>   /I..\..\common
3201>   /I..\..\common\nlohmann
3201>   /I..\..\McpProtocolLib
3201>   /I..\client
3201>   /I..\server
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\idl\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\security\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3201>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3201>   /Ie:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3201>   /Ie:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\inc
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\com\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include
3201>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3201>   /Ie:\os\src\onecore\ds\inc
3201>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3201>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3201>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3201>   /Ie:\os\public\amd64fre\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3201>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3201>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3201>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3201>   /D_WIN64
3201>   /D_AMD64_
3201>   /DAMD64
3201>   /DCONDITION_HANDLING=1
3201>   /DNT_INST=0
3201>   /DWIN32=100
3201>   /D_NT1X_=100
3201>   /DWINNT=1
3201>   /D_WIN32_WINNT=0x0A00
3201>   /DWINVER=0x0A00
3201>   /D_WIN32_IE=0x0A00
3201>   /DWIN32_LEAN_AND_MEAN=1
3201>   /DDEVL=1
3201>   /DNDEBUG
3201>   /D_STL120_
3201>   /D_STL140_
3201>   /D_DLL=1
3201>   /D_MT=1
3201>   -DNT_IUM
3201>   -DWIN32
3201>   -D_WIN32
3201>   -DUNICODE
3201>   -D_UNICODE
3201>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3201>   /D_USE_DEV11_CRT
3201>   -D_APISET_MINWIN_VERSION=0x0115
3201>   -D_APISET_MINCORE_VERSION=0x0114
3201>   /DFE_SB
3201>   /DFE_IME
3201>   /DNTDDI_VERSION=0x0A000011
3201>   /DWINBLUE_KBSPRING14
3201>   /DBUILD_WINDOWS
3201>   /DUNDOCKED_WINDOWS_UCRT
3201>   /D__WRL_CONFIGURATION_LEGACY__
3201>   /DBUILD_UMS_ENABLED=1
3201>   /DBUILD_WOW64_ENABLED=1
3201>   /DBUILD_ARM64X_ENABLED=0
3201>   /DEXECUTABLE_WRITES_SUPPORT=0
3201>   -D_USE_DECLSPECS_FOR_SAL=1
3201>   /DRUN_WPP
3201>   -DUNLOADABLE_DELAYLOAD_IMPLEMENTATION
3201>   -D__PLACEHOLDER_SAL=1
3201>   /D_ATL_STATIC_REGISTRY
3201>   /D_WINDLL
3201>   /c
3201>   /Zc:wchar_t-
3201>   /Zl
3201>   /Zp8
3201>   /Gy
3201>   /W4
3201>   /wd4244
3201>   /d1import_no_registry
3201>   /EHsc
3201>   /GR-
3201>   /GF
3201>   /GS
3201>   /Z7
3201>   /Oxs
3201>   /GL
3201>   /Z7
3201>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3201>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3201>   /w15043
3201>   /Zc:rvalueCast
3201>   /Zo
3201>   -D_UCRT
3201>   -D_CONST_RETURN=
3201>   -D_CRT_SECURE_NO_WARNINGS
3201>   -D_CRT_NON_CONFORMING_SWPRINTFS
3201>   -D_CRT_NONSTDC_NO_WARNINGS
3201>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3201>   /D_CRT_STDIO_INLINE=extern
3201>   /D_NO_CRT_STDIO_INLINE
3201>   /D_ACRTIMP_ALT=
3201>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3201>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3201>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3201>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3201>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_HAS_STD_BYTE=0
3201>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3201>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3201>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3201>   /D_FULL_IOBUF
3201>   /d1initAll:Mask11
3201>   /d1initAll:FillPattern0
3201>   /d1nodatetime
3201>   /d1trimfile:e:\os\src\=BASEDIR
3201>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3201>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3201>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3201>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3201>   /d2AllowCompatibleILVersions
3201>   /d2Zi+
3201>   /ZH:SHA_256
3201>   /wd4986
3201>   /wd4987
3201>   /wd4471
3201>   /wd4369
3201>   /wd4309
3201>   /wd4754
3201>   /wd4427
3201>   /d2DeepThoughtInliner-
3201>   /d2implyavx512upperregs-
3201>   /Wv:19.23
3201>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\
3201>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3201>   /wl4002
3201>   /wl4003
3201>   /wl4005
3201>   /wl4006
3201>   /wl4007
3201>   /wl4008
3201>   /wl4010
3201>   /wl4013
3201>   /wl4015
3201>   /wl4018
3201>   /wl4020
3201>   /wl4022
3201>   /wl4024
3201>   /wl4025
3201>   /wl4026
3201>   /wl4027
3201>   /wl4028
3201>   /wl4029
3201>   /wl4030
3201>   /wl4031
3201>   /wl4033
3201>   /wl4034
3201>   /wl4036
3201>   /wl4038
3201>   /wl4041
3201>   /wl4042
3201>   /wl4045
3201>   /wl4047
3201>   /wl4048
3201>   /wl4049
3201>   /wl4056
3201>   /wl4066
3201>   /wl4067
3201>   /wl4068
3201>   /wl4073
3201>   /wl4074
3201>   /wl4075
3201>   /wl4076
3201>   /wl4077
3201>   /wl4079
3201>   /wl4080
3201>   /wl4081
3201>   /wl4083
3201>   /wl4085
3201>   /wl4086
3201>   /wl4087
3201>   /wl4088
3201>   /wl4089
3201>   /wl4090
3201>   /wl4091
3201>   /wl4094
3201>   /wl4096
3201>   /wl4097
3201>   /wl4098
3201>   /wl4099
3201>   /wl4101
3201>   /wl4102
3201>   /wl4109
3201>   /wl4112
3201>   /wl4113
3201>   /wl4114
3201>   /wl4115
3201>   /wl4116
3201>   /wl4117
3201>   /wl4119
3201>   /wl4120
3201>   /wl4122
3201>   /wl4124
3201>   /wl4129
3201>   /wl4133
3201>   /wl4138
3201>   /wl4141
3201>   /wl4142
3201>   /wl4143
3201>   /wl4144
3201>   /wl4145
3201>   /wl4150
3201>   /wl4153
3201>   /wl4154
3201>   /wl4155
3201>   /wl4156
3201>   /wl4157
3201>   /wl4158
3201>   /wl4159
3201>   /wl4160
3201>   /wl4161
3201>   /wl4162
3201>   /wl4163
3201>   /wl4164
3201>   /wl4166
3201>   /wl4167
3201>   /wl4168
3201>   /wl4172
3201>   /wl4174
3201>   /wl4175
3201>   /wl4176
3201>   /wl4177
3201>   /wl4178
3201>   /wl4180
3201>   /wl4182
3201>   /wl4183
3201>   /wl4185
3201>   /wl4186
3201>   /wl4187
3201>   /wl4190
3201>   /wl4192
3201>   /wl4197
3201>   /wl4200
3201>   /wl4213
3201>   /wl4215
3201>   /wl4216
3201>   /wl4218
3201>   /wl4223
3201>   /wl4224
3201>   /wl4226
3201>   /wl4227
3201>   /wl4228
3201>   /wl4229
3201>   /wl4230
3201>   /wl4237
3201>   /wl4240
3201>   /wl4243
3201>   /wl4244
3201>   /wl4250
3201>   /wl4251
3201>   /wl4258
3201>   /wl4267
3201>   /wl4269
3201>   /wl4272
3201>   /wl4273
3201>   /wl4274
3201>   /wl4275
3201>   /wl4276
3201>   /wl4278
3201>   /wl4280
3201>   /wl4281
3201>   /wl4282
3201>   /wl4283
3201>   /wl4285
3201>   /wl4286
3201>   /wl4288
3201>   /wl4290
3201>   /wl4291
3201>   /wl4293
3201>   /wl4297
3201>   /wl4302
3201>   /wl4305
3201>   /wl4306
3201>   /wl4307
3201>   /wl4309
3201>   /wl4310
3201>   /wl4311
3201>   /wl4312
3201>   /wl4313
3201>   /wl4316
3201>   /wl4319
3201>   /wl4325
3201>   /wl4326
3201>   /wl4329
3201>   /wl4333
3201>   /wl4334
3201>   /wl4335
3201>   /wl4340
3201>   /wl4344
3201>   /wl4346
3201>   /wl4348
3201>   /wl4353
3201>   /wl4356
3201>   /wl4357
3201>   /wl4358
3201>   /wl4359
3201>   /wl4364
3201>   /wl4368
3201>   /wl4369
3201>   /wl4373
3201>   /wl4374
3201>   /wl4375
3201>   /wl4376
3201>   /wl4377
3201>   /wl4378
3201>   /wl4379
3201>   /wl4381
3201>   /wl4382
3201>   /wl4383
3201>   /wl4384
3201>   /wl4390
3201>   /wl4391
3201>   /wl4392
3201>   /wl4393
3201>   /wl4394
3201>   /wl4395
3201>   /wl4396
3201>   /wl4397
3201>   /wl4398
3201>   /wl4399
3201>   /wl4600
3201>   /wl4401
3201>   /wl4402
3201>   /wl4403
3201>   /wl4404
3201>   /wl4405
3201>   /wl4406
3201>   /wl4407
3201>   /wl4409
3201>   /wl4410
3201>   /wl4411
3201>   /wl4414
3201>   /wl4420
3201>   /wl4430
3201>   /wl4436
3201>   /wl4439
3201>   /wl4440
3201>   /wl4441
3201>   /wl4445
3201>   /wl4461
3201>   /wl4462
3201>   /wl4470
3201>   /wl4473
3201>   /wl4477
3201>   /wl4484
3201>   /wl4485
3201>   /wl4486
3201>   /wl4488
3201>   /wl4489
3201>   /wl4490
3201>   /wl4502
3201>   /wl4503
3201>   /wl4506
3201>   /wl4508
3201>   /wl4511
3201>   /wl4518
3201>   /wl4521
3201>   /wl4522
3201>   /wl4523
3201>   /wl4526
3201>   /wl4530
3201>   /wl4534
3201>   /wl4535
3201>   /wl4537
3201>   /wl4538
3201>   /wl4540
3201>   /wl4541
3201>   /wl4543
3201>   /wl4544
3201>   /wl4550
3201>   /wl4551
3201>   /wl4552
3201>   /wl4553
3201>   /wl4554
3201>   /wl4556
3201>   /wl4558
3201>   /wl4561
3201>   /wl4566
3201>   /wl4570
3201>   /wl4572
3201>   /wl4580
3201>   /wl4581
3201>   /wl4584
3201>   /wl4596
3201>   /wl4597
3201>   /wl4602
3201>   /wl4603
3201>   /wl4606
3201>   /wl4612
3201>   /wl4613
3201>   /wl4615
3201>   /wl4616
3201>   /wl4618
3201>   /wl4620
3201>   /wl4621
3201>   /wl4622
3201>   /wl4624
3201>   /wl4627
3201>   /wl4630
3201>   /wl4632
3201>   /wl4633
3201>   /wl4635
3201>   /wl4636
3201>   /wl4637
3201>   /wl4638
3201>   /wl4641
3201>   /wl4645
3201>   /wl4646
3201>   /wl4650
3201>   /wl4651
3201>   /wl4652
3201>   /wl4653
3201>   /wl4655
3201>   /wl4656
3201>   /wl4657
3201>   /wl4659
3201>   /wl4661
3201>   /wl4662
3201>   /wl4667
3201>   /wl4669
3201>   /wl4674
3201>   /wl4677
3201>   /wl4678
3201>   /wl4679
3201>   /wl4683
3201>   /wl4684
3201>   /wl4685
3201>   /wl4687
3201>   /wl4688
3201>   /wl4691
3201>   /wl4693
3201>   /wl4694
3201>   /wl4698
3201>   /wl4711
3201>   /wl4715
3201>   /wl4716
3201>   /wl4717
3201>   /wl4722
3201>   /wl4723
3201>   /wl4724
3201>   /wl4727
3201>   /wl4730
3201>   /wl4731
3201>   /wl4733
3201>   /wl4739
3201>   /wl4742
3201>   /wl4743
3201>   /wl4744
3201>   /wl4747
3201>   /wl4750
3201>   /wl4756
3201>   /wl4768
3201>   /wl4772
3201>   /wl4788
3201>   /wl4793
3201>   /wl4794
3201>   /wl4799
3201>   /wl4803
3201>   /wl4804
3201>   /wl4805
3201>   /wl4806
3201>   /wl4807
3201>   /wl4810
3201>   /wl4811
3201>   /wl4812
3201>   /wl4813
3201>   /wl4817
3201>   /wl4819
3201>   /wl4821
3201>   /wl4823
3201>   /wl4829
3201>   /wl4834
3201>   /wl4835
3201>   /wl4838
3201>   /wl4839
3201>   /wl4867
3201>   /wl4900
3201>   /wl4910
3201>   /wl4912
3201>   /wl4920
3201>   /wl4925
3201>   /wl4926
3201>   /wl4927
3201>   /wl4929
3201>   /wl4930
3201>   /wl4935
3201>   /wl4936
3201>   /wl4939
3201>   /wl4944
3201>   /wl4945
3201>   /wl4947
3201>   /wl4948
3201>   /wl4949
3201>   /wl4950
3201>   /wl4951
3201>   /wl4952
3201>   /wl4953
3201>   /wl4956
3201>   /wl4957
3201>   /wl4958
3201>   /wl4959
3201>   /wl4961
3201>   /wl4964
3201>   /wl4965
3201>   /wl4972
3201>   /wl4984
3201>   /wl4995
3201>   /wl4996
3201>   /wl4997
3201>   /wl4999
3201>   /wl5033
3201>   /wl5037
3201>   /wl5046
3201>   /wl5050
3201>   /wl5055
3201>   /wl5056
3201>   /wl5105
3201>   /wl5208
3201>   /d2Qvec-mathlib-
3201>   /d2Qvec-sse2only
3201>   /Gw
3201>   /Zc:checkGwOdr
3201>   /d1ignorePragmaWarningError
3201>   /wd4316
3201>   /wd4973
3201>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3201>   /d2FH4
3201>   /Brepro
3201>   -D_HAS_MAGIC_STATICS=1
3201>   /Qspectre
3201>   /wd5045
3201>   /d2guardspecanalysismode:v1_0
3201>   /d2guardspecmode2
3201>   /guard:cf
3201>   /d2guardcfgfuncptr-
3201>   /d2guardcfgdispatch
3201>   /guard:ehcont
3201>   -D__PLACEHOLDER_SAL=1
3201>   -wd4425
3201>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3201>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3201>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3201>   /std:c++17
3201>   /Yupch.hxx /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch.pch
3201>   .\dllmain.cpp .\aimxservice.cpp 
3201>dllmain.cpp
3201>aimxservice.cpp
3201> set _createfile=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\delayload.txt
3201> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.lib @e:\os\obj\amd64fre\temp\b22d16938d8e04ecc0b72731656fddbe\lib_1.rsp
3201>Microsoft (R) Library Manager Version 14.42.34444.100
3201>Copyright (C) Microsoft Corporation.  All rights reserved.
3201>/IGNORE:4078,4221,4281,4006,4198 
3201>/nodefaultlib 
3201>/machine:amd64 
3201>/ltcg 
3201>/Brepro 
3201>/def:aimxsrv.def 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\dllmain.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxservice.obj 
3201>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch.obj 
3201>   Creating library e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.lib and object e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.exp
3201>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\Macros-PASS1.txt
3201>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\Macros-PASS1.txt
3201> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\dll" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3201>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\dll in pass PASS1
3202>Calculated LAYERINFO_MODULE='OneCoreDS'.
3202>makefile.def: TEMP=e:\os\obj\amd64fre\temp\8827d5d687c690bb09535de14a400675
3202>makefile.def: BUILDINGINDATT=
3202>[Core OS Undocking] NOT using package ''
3202>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server' (target 'aimxserver', type 'LIBRARY', nt_target_version '0xA000011')
3202>ObjectsMac.ts: validation succeeded
3202>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server" (STL_VER_TELEMETRY)
3202>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3202>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64 already exists.
3202> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\_PASS1_Marker.log
3202> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\8827d5d687c690bb09535de14a400675\tmp_33192_1753418495903493300.tmp /Tpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\pch_hdr.src
3202>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3202>Copyright (C) Microsoft Corporation.  All rights reserved.
3202>cl 
3202>   /Iamd64
3202>   /I.
3202>   /Ie:\os\src\data\MSRC
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3202>   /I..\..\common
3202>   /I..\..\common\nlohmann
3202>   /I..\..\McpProtocolLib
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\..\idl\objfre\amd64
3202>   /Ie:\os\src\onecore\ds\security\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3202>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3202>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3202>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3202>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3202>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\idl
3202>   /Ie:\os\public\amd64fre\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64
3202>   /Ie:\os\src\onecore\ds\inc
3202>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3202>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3202>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3202>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3202>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3202>   /Ie:\os\public\amd64fre\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3202>   /Ie:\os\public\amd64fre\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3202>   /Ie:\os\public\amd64fre\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3202>   /Ie:\os\public\amd64fre\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3202>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3202>   /D_WIN64
3202>   /D_AMD64_
3202>   /DAMD64
3202>   /DCONDITION_HANDLING=1
3202>   /DNT_INST=0
3202>   /DWIN32=100
3202>   /D_NT1X_=100
3202>   /DWINNT=1
3202>   /D_WIN32_WINNT=0x0A00
3202>   /DWINVER=0x0A00
3202>   /D_WIN32_IE=0x0A00
3202>   /DWIN32_LEAN_AND_MEAN=1
3202>   /DDEVL=1
3202>   /DNDEBUG
3202>   /D_STL120_
3202>   /D_STL140_
3202>   /D_DLL=1
3202>   /D_MT=1
3202>   -DNT_IUM
3202>   -DWIN32
3202>   -D_WIN32
3202>   -DUNICODE
3202>   -D_UNICODE
3202>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3202>   /D_USE_DEV11_CRT
3202>   -D_APISET_MINWIN_VERSION=0x0115
3202>   -D_APISET_MINCORE_VERSION=0x0114
3202>   /DFE_SB
3202>   /DFE_IME
3202>   /DNTDDI_VERSION=0x0A000011
3202>   /DWINBLUE_KBSPRING14
3202>   /DBUILD_WINDOWS
3202>   /DUNDOCKED_WINDOWS_UCRT
3202>   /D__WRL_CONFIGURATION_LEGACY__
3202>   /DBUILD_UMS_ENABLED=1
3202>   /DBUILD_WOW64_ENABLED=1
3202>   /DBUILD_ARM64X_ENABLED=0
3202>   /DEXECUTABLE_WRITES_SUPPORT=0
3202>   -D_USE_DECLSPECS_FOR_SAL=1
3202>   /DRUN_WPP
3202>   -D__PLACEHOLDER_SAL=1
3202>   /D_ATL_STATIC_REGISTRY
3202>   /c
3202>   /Zc:wchar_t-
3202>   /Zl
3202>   /Zp8
3202>   /Gy
3202>   /W4
3202>   /d1import_no_registry
3202>   /EHsc
3202>   /GR-
3202>   /GF
3202>   /GS
3202>   /Z7
3202>   /Oxs
3202>   /GL
3202>   /Z7
3202>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3202>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3202>   /w15043
3202>   /Zc:rvalueCast
3202>   /Zo
3202>   -D_UCRT
3202>   -D_CONST_RETURN=
3202>   -D_CRT_SECURE_NO_WARNINGS
3202>   -D_CRT_NON_CONFORMING_SWPRINTFS
3202>   -D_CRT_NONSTDC_NO_WARNINGS
3202>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3202>   /D_CRT_STDIO_INLINE=extern
3202>   /D_NO_CRT_STDIO_INLINE
3202>   /D_ACRTIMP_ALT=
3202>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3202>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3202>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3202>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3202>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3202>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3202>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3202>   /D_HAS_STD_BYTE=0
3202>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3202>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3202>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3202>   /D_FULL_IOBUF
3202>   /d1initAll:Mask11
3202>   /d1initAll:FillPattern0
3202>   /d1nodatetime
3202>   /d1trimfile:e:\os\src\=BASEDIR
3202>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3202>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3202>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3202>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3202>   /d2AllowCompatibleILVersions
3202>   /d2Zi+
3202>   /ZH:SHA_256
3202>   /wd4986
3202>   /wd4987
3202>   /wd4471
3202>   /wd4369
3202>   /wd4309
3202>   /wd4754
3202>   /wd4427
3202>   /d2DeepThoughtInliner-
3202>   /d2implyavx512upperregs-
3202>   /Wv:19.23
3202>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\
3202>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3202>   /wl4002
3202>   /wl4003
3202>   /wl4005
3202>   /wl4006
3202>   /wl4007
3202>   /wl4008
3202>   /wl4010
3202>   /wl4013
3202>   /wl4015
3202>   /wl4018
3202>   /wl4020
3202>   /wl4022
3202>   /wl4024
3202>   /wl4025
3202>   /wl4026
3202>   /wl4027
3202>   /wl4028
3202>   /wl4029
3202>   /wl4030
3202>   /wl4031
3202>   /wl4033
3202>   /wl4034
3202>   /wl4036
3202>   /wl4038
3202>   /wl4041
3202>   /wl4042
3202>   /wl4045
3202>   /wl4047
3202>   /wl4048
3202>   /wl4049
3202>   /wl4056
3202>   /wl4066
3202>   /wl4067
3202>   /wl4068
3202>   /wl4073
3202>   /wl4074
3202>   /wl4075
3202>   /wl4076
3202>   /wl4077
3202>   /wl4079
3202>   /wl4080
3202>   /wl4081
3202>   /wl4083
3202>   /wl4085
3202>   /wl4086
3202>   /wl4087
3202>   /wl4088
3202>   /wl4089
3202>   /wl4090
3202>   /wl4091
3202>   /wl4094
3202>   /wl4096
3202>   /wl4097
3202>   /wl4098
3202>   /wl4099
3202>   /wl4101
3202>   /wl4102
3202>   /wl4109
3202>   /wl4112
3202>   /wl4113
3202>   /wl4114
3202>   /wl4115
3202>   /wl4116
3202>   /wl4117
3202>   /wl4119
3202>   /wl4120
3202>   /wl4122
3202>   /wl4124
3202>   /wl4129
3202>   /wl4133
3202>   /wl4138
3202>   /wl4141
3202>   /wl4142
3202>   /wl4143
3202>   /wl4144
3202>   /wl4145
3202>   /wl4150
3202>   /wl4153
3202>   /wl4154
3202>   /wl4155
3202>   /wl4156
3202>   /wl4157
3202>   /wl4158
3202>   /wl4159
3202>   /wl4160
3202>   /wl4161
3202>   /wl4162
3202>   /wl4163
3202>   /wl4164
3202>   /wl4166
3202>   /wl4167
3202>   /wl4168
3202>   /wl4172
3202>   /wl4174
3202>   /wl4175
3202>   /wl4176
3202>   /wl4177
3202>   /wl4178
3202>   /wl4180
3202>   /wl4182
3202>   /wl4183
3202>   /wl4185
3202>   /wl4186
3202>   /wl4187
3202>   /wl4190
3202>   /wl4192
3202>   /wl4197
3202>   /wl4200
3202>   /wl4213
3202>   /wl4215
3202>   /wl4216
3202>   /wl4218
3202>   /wl4223
3202>   /wl4224
3202>   /wl4226
3202>   /wl4227
3202>   /wl4228
3202>   /wl4229
3202>   /wl4230
3202>   /wl4237
3202>   /wl4240
3202>   /wl4243
3202>   /wl4244
3202>   /wl4250
3202>   /wl4251
3202>   /wl4258
3202>   /wl4267
3202>   /wl4269
3202>   /wl4272
3202>   /wl4273
3202>   /wl4274
3202>   /wl4275
3202>   /wl4276
3202>   /wl4278
3202>   /wl4280
3202>   /wl4281
3202>   /wl4282
3202>   /wl4283
3202>   /wl4285
3202>   /wl4286
3202>   /wl4288
3202>   /wl4290
3202>   /wl4291
3202>   /wl4293
3202>   /wl4297
3202>   /wl4302
3202>   /wl4305
3202>   /wl4306
3202>   /wl4307
3202>   /wl4309
3202>   /wl4310
3202>   /wl4311
3202>   /wl4312
3202>   /wl4313
3202>   /wl4316
3202>   /wl4319
3202>   /wl4325
3202>   /wl4326
3202>   /wl4329
3202>   /wl4333
3202>   /wl4334
3202>   /wl4335
3202>   /wl4340
3202>   /wl4344
3202>   /wl4346
3202>   /wl4348
3202>   /wl4353
3202>   /wl4356
3202>   /wl4357
3202>   /wl4358
3202>   /wl4359
3202>   /wl4364
3202>   /wl4368
3202>   /wl4369
3202>   /wl4373
3202>   /wl4374
3202>   /wl4375
3202>   /wl4376
3202>   /wl4377
3202>   /wl4378
3202>   /wl4379
3202>   /wl4381
3202>   /wl4382
3202>   /wl4383
3202>   /wl4384
3202>   /wl4390
3202>   /wl4391
3202>   /wl4392
3202>   /wl4393
3202>   /wl4394
3202>   /wl4395
3202>   /wl4396
3202>   /wl4397
3202>   /wl4398
3202>   /wl4399
3202>   /wl4600
3202>   /wl4401
3202>   /wl4402
3202>   /wl4403
3202>   /wl4404
3202>   /wl4405
3202>   /wl4406
3202>   /wl4407
3202>   /wl4409
3202>   /wl4410
3202>   /wl4411
3202>   /wl4414
3202>   /wl4420
3202>   /wl4430
3202>   /wl4436
3202>   /wl4439
3202>   /wl4440
3202>   /wl4441
3202>   /wl4445
3202>   /wl4461
3202>   /wl4462
3202>   /wl4470
3202>   /wl4473
3202>   /wl4477
3202>   /wl4484
3202>   /wl4485
3202>   /wl4486
3202>   /wl4488
3202>   /wl4489
3202>   /wl4490
3202>   /wl4502
3202>   /wl4503
3202>   /wl4506
3202>   /wl4508
3202>   /wl4511
3202>   /wl4518
3202>   /wl4521
3202>   /wl4522
3202>   /wl4523
3202>   /wl4526
3202>   /wl4530
3202>   /wl4534
3202>   /wl4535
3202>   /wl4537
3202>   /wl4538
3202>   /wl4540
3202>   /wl4541
3202>   /wl4543
3202>   /wl4544
3202>   /wl4550
3202>   /wl4551
3202>   /wl4552
3202>   /wl4553
3202>   /wl4554
3202>   /wl4556
3202>   /wl4558
3202>   /wl4561
3202>   /wl4566
3202>   /wl4570
3202>   /wl4572
3202>   /wl4580
3202>   /wl4581
3202>   /wl4584
3202>   /wl4596
3202>   /wl4597
3202>   /wl4602
3202>   /wl4603
3202>   /wl4606
3202>   /wl4612
3202>   /wl4613
3202>   /wl4615
3202>   /wl4616
3202>   /wl4618
3202>   /wl4620
3202>   /wl4621
3202>   /wl4622
3202>   /wl4624
3202>   /wl4627
3202>   /wl4630
3202>   /wl4632
3202>   /wl4633
3202>   /wl4635
3202>   /wl4636
3202>   /wl4637
3202>   /wl4638
3202>   /wl4641
3202>   /wl4645
3202>   /wl4646
3202>   /wl4650
3202>   /wl4651
3202>   /wl4652
3202>   /wl4653
3202>   /wl4655
3202>   /wl4656
3202>   /wl4657
3202>   /wl4659
3202>   /wl4661
3202>   /wl4662
3202>   /wl4667
3202>   /wl4669
3202>   /wl4674
3202>   /wl4677
3202>   /wl4678
3202>   /wl4679
3202>   /wl4683
3202>   /wl4684
3202>   /wl4685
3202>   /wl4687
3202>   /wl4688
3202>   /wl4691
3202>   /wl4693
3202>   /wl4694
3202>   /wl4698
3202>   /wl4711
3202>   /wl4715
3202>   /wl4716
3202>   /wl4717
3202>   /wl4722
3202>   /wl4723
3202>   /wl4724
3202>   /wl4727
3202>   /wl4730
3202>   /wl4731
3202>   /wl4733
3202>   /wl4739
3202>   /wl4742
3202>   /wl4743
3202>   /wl4744
3202>   /wl4747
3202>   /wl4750
3202>   /wl4756
3202>   /wl4768
3202>   /wl4772
3202>   /wl4788
3202>   /wl4793
3202>   /wl4794
3202>   /wl4799
3202>   /wl4803
3202>   /wl4804
3202>   /wl4805
3202>   /wl4806
3202>   /wl4807
3202>   /wl4810
3202>   /wl4811
3202>   /wl4812
3202>   /wl4813
3202>   /wl4817
3202>   /wl4819
3202>   /wl4821
3202>   /wl4823
3202>   /wl4829
3202>   /wl4834
3202>   /wl4835
3202>   /wl4838
3202>   /wl4839
3202>   /wl4867
3202>   /wl4900
3202>   /wl4910
3202>   /wl4912
3202>   /wl4920
3202>   /wl4925
3202>   /wl4926
3202>   /wl4927
3202>   /wl4929
3202>   /wl4930
3202>   /wl4935
3202>   /wl4936
3202>   /wl4939
3202>   /wl4944
3202>   /wl4945
3202>   /wl4947
3202>   /wl4948
3202>   /wl4949
3202>   /wl4950
3202>   /wl4951
3202>   /wl4952
3202>   /wl4953
3202>   /wl4956
3202>   /wl4957
3202>   /wl4958
3202>   /wl4959
3202>   /wl4961
3202>   /wl4964
3202>   /wl4965
3202>   /wl4972
3202>   /wl4984
3202>   /wl4995
3202>   /wl4996
3202>   /wl4997
3202>   /wl4999
3202>   /wl5033
3202>   /wl5037
3202>   /wl5046
3202>   /wl5050
3202>   /wl5055
3202>   /wl5056
3202>   /wl5105
3202>   /wl5208
3202>   /d2Qvec-mathlib-
3202>   /d2Qvec-sse2only
3202>   /Gw
3202>   /Zc:checkGwOdr
3202>   /d1ignorePragmaWarningError
3202>   /wd4316
3202>   /wd4973
3202>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3202>   /d2FH4
3202>   /Brepro
3202>   -D_HAS_MAGIC_STATICS=1
3202>   /Qspectre
3202>   /wd5045
3202>   /d2guardspecanalysismode:v1_0
3202>   /d2guardspecmode2
3202>   /guard:cf
3202>   /d2guardcfgfuncptr-
3202>   /d2guardcfgdispatch
3202>   /guard:ehcont
3202>   -D__PLACEHOLDER_SAL=1
3202>   -wd4425
3202>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3202>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3202>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3202>   /std:c++17
3202>   /Ylaimxserver
3202>   /Ycpch.hxx
3202>   /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\pch.pch
3202>   /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\pch.obj"
3202>pch_hdr.src
3202> e:\os\tools\vc\HostX86\amd64\cl.exe @e:\os\obj\amd64fre\temp\8827d5d687c690bb09535de14a400675\cl_1.rsp
3202>Microsoft (R) C/C++ Optimizing Compiler Version 19.42.34444.100 for x64
3202>Copyright (C) Microsoft Corporation.  All rights reserved.
3202>cl /Fo"e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64/"
3202>   /FC
3202>   /Iamd64
3202>   /I.
3202>   /Ie:\os\src\data\MSRC
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\atlmfc
3202>   /I..\..\common
3202>   /I..\..\common\nlohmann
3202>   /I..\..\McpProtocolLib
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\..\idl\objfre\amd64
3202>   /Ie:\os\src\onecore\ds\security\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\ds\inc
3202>   /Ie:\os\public\amd64fre\onecore\private\ds\inc\security\base
3202>   /Ie:\os\public\amd64fre\onecore\private\base\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\base\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\lsa
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Inc\apiset
3202>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc
3202>   /Ie:\os\public\amd64fre\OneCore\Private\MinWin\Priv_Sdk\Inc\lsa
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Inc
3202>   /Ie:\os\src\onecore\ds\ds\src\adai\proto\win32\aimxsrv\idl
3202>   /Ie:\os\public\amd64fre\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\OneCore\Priv_Sdk\Inc
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\include
3202>   /Ie:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64
3202>   /Ie:\os\src\onecore\ds\inc
3202>   /Ie:\os\obj\amd64fre\onecore\ds\inc\objfre\amd64
3202>   /Ie:\os\public\amd64fre\internal\onecoreds\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Restricted\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\Restricted\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\External\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\External\DS\inc
3202>   /Ie:\os\public\amd64fre\ClientCore\External\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\Internal\DS\inc
3202>   /Ie:\os\public\amd64fre\ClientCore\Internal\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\Private\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCoreUap\Private\DS\inc
3202>   /Ie:\os\public\amd64fre\ClientCore\Private\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\external\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\restricted\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\internal\DS\inc
3202>   /Ie:\os\public\amd64fre\OneCore\private\DS\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\oak\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\oak\inc
3202>   /Ie:\os\public\amd64fre\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\shared\inc\MinWin
3202>   /Ie:\os\public\amd64fre\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\MinCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ClientCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ModernCore
3202>   /Ie:\os\public\amd64fre\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\shared\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc
3202>   /Ie:\os\public\amd64fre\onecore\external\ddk\inc\wdm
3202>   /Ie:\os\public\amd64fre\onecoreuap\external\ddk\inc\wdm
3202>   /Ie:\os\public\amd64fre\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\private\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\private\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\restricted\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\internal\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecoreuap\restricted\sdk\inc
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinWin\fs
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\MinCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ClientCore
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ModernCore
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\private
3202>   /Ie:\os\public\amd64fre\OneCore\Internal\hv\hvsdk\just_built\inc\internal
3202>   /Ie:\os\public\amd64fre\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\internal\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\onecore\external\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3202>   /Ie:\os\public\amd64fre\onecore\internal\sdk\inc\ucrt\stl120
3202>   /D_WIN64
3202>   /D_AMD64_
3202>   /DAMD64
3202>   /DCONDITION_HANDLING=1
3202>   /DNT_INST=0
3202>   /DWIN32=100
3202>   /D_NT1X_=100
3202>   /DWINNT=1
3202>   /D_WIN32_WINNT=0x0A00
3202>   /DWINVER=0x0A00
3202>   /D_WIN32_IE=0x0A00
3202>   /DWIN32_LEAN_AND_MEAN=1
3202>   /DDEVL=1
3202>   /DNDEBUG
3202>   /D_STL120_
3202>   /D_STL140_
3202>   /D_DLL=1
3202>   /D_MT=1
3202>   -DNT_IUM
3202>   -DWIN32
3202>   -D_WIN32
3202>   -DUNICODE
3202>   -D_UNICODE
3202>   -D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE
3202>   /D_USE_DEV11_CRT
3202>   -D_APISET_MINWIN_VERSION=0x0115
3202>   -D_APISET_MINCORE_VERSION=0x0114
3202>   /DFE_SB
3202>   /DFE_IME
3202>   /DNTDDI_VERSION=0x0A000011
3202>   /DWINBLUE_KBSPRING14
3202>   /DBUILD_WINDOWS
3202>   /DUNDOCKED_WINDOWS_UCRT
3202>   /D__WRL_CONFIGURATION_LEGACY__
3202>   /DBUILD_UMS_ENABLED=1
3202>   /DBUILD_WOW64_ENABLED=1
3202>   /DBUILD_ARM64X_ENABLED=0
3202>   /DEXECUTABLE_WRITES_SUPPORT=0
3202>   -D_USE_DECLSPECS_FOR_SAL=1
3202>   /DRUN_WPP
3202>   -D__PLACEHOLDER_SAL=1
3202>   /D_ATL_STATIC_REGISTRY
3202>   /c
3202>   /Zc:wchar_t-
3202>   /Zl
3202>   /Zp8
3202>   /Gy
3202>   /W4
3202>   /d1import_no_registry
3202>   /EHsc
3202>   /GR-
3202>   /GF
3202>   /GS
3202>   /Z7
3202>   /Oxs
3202>   /GL
3202>   /Z7
3202>   @e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.Cxx.DefaultErrors.rsp
3202>   /we4308 /we4509 /we4510 /we4532 /we4533 /we4610 /we4700 /we4789
3202>   /w15043
3202>   /Zc:rvalueCast
3202>   /Zo
3202>   -D_UCRT
3202>   -D_CONST_RETURN=
3202>   -D_CRT_SECURE_NO_WARNINGS
3202>   -D_CRT_NON_CONFORMING_SWPRINTFS
3202>   -D_CRT_NONSTDC_NO_WARNINGS
3202>   -D_CRT_STDIO_ARBITRARY_WIDE_SPECIFIERS
3202>   /D_CRT_STDIO_INLINE=extern
3202>   /D_NO_CRT_STDIO_INLINE
3202>   /D_ACRTIMP_ALT=
3202>   /D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS
3202>   /D_STL_EXTRA_DISABLED_WARNINGS=4239
3202>   /D_SILENCE_TR1_NAMESPACE_DEPRECATION_WARNING
3202>   /D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
3202>   /D_SILENCE_TR2_SYS_NAMESPACE_DEPRECATION_WARNING
3202>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3202>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3202>   /D_HAS_STD_BYTE=0
3202>   /D_ENFORCE_MATCHING_ALLOCATORS=0
3202>   /D_HAS_FUNCTION_ALLOCATOR_SUPPORT=1
3202>   /D_SILENCE_STDEXT_ALLOCATORS_DEPRECATION_WARNING
3202>   /D_FULL_IOBUF
3202>   /d1initAll:Mask11
3202>   /d1initAll:FillPattern0
3202>   /d1nodatetime
3202>   /d1trimfile:e:\os\src\=BASEDIR
3202>   /d1trimfile:e:\os\public\amd64fre\=PUBLIC_ROOT
3202>   /d1trimfile:e:\os\obj\amd64fre\=OBJECT_ROOT
3202>   /d1trimfile:e:\os\bin\amd64fre\=_NTTREE
3202>   /d1trimfile:e:\os\osdep\=OSDEPENDSROOT
3202>   /d2AllowCompatibleILVersions
3202>   /d2Zi+
3202>   /ZH:SHA_256
3202>   /wd4986
3202>   /wd4987
3202>   /wd4471
3202>   /wd4369
3202>   /wd4309
3202>   /wd4754
3202>   /wd4427
3202>   /d2DeepThoughtInliner-
3202>   /d2implyavx512upperregs-
3202>   /Wv:19.23
3202>   /Fwe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\
3202>   @e:\os\obj\amd64fre\objfre\amd64\DMF\logged-warnings.rsp
3202>   /wl4002
3202>   /wl4003
3202>   /wl4005
3202>   /wl4006
3202>   /wl4007
3202>   /wl4008
3202>   /wl4010
3202>   /wl4013
3202>   /wl4015
3202>   /wl4018
3202>   /wl4020
3202>   /wl4022
3202>   /wl4024
3202>   /wl4025
3202>   /wl4026
3202>   /wl4027
3202>   /wl4028
3202>   /wl4029
3202>   /wl4030
3202>   /wl4031
3202>   /wl4033
3202>   /wl4034
3202>   /wl4036
3202>   /wl4038
3202>   /wl4041
3202>   /wl4042
3202>   /wl4045
3202>   /wl4047
3202>   /wl4048
3202>   /wl4049
3202>   /wl4056
3202>   /wl4066
3202>   /wl4067
3202>   /wl4068
3202>   /wl4073
3202>   /wl4074
3202>   /wl4075
3202>   /wl4076
3202>   /wl4077
3202>   /wl4079
3202>   /wl4080
3202>   /wl4081
3202>   /wl4083
3202>   /wl4085
3202>   /wl4086
3202>   /wl4087
3202>   /wl4088
3202>   /wl4089
3202>   /wl4090
3202>   /wl4091
3202>   /wl4094
3202>   /wl4096
3202>   /wl4097
3202>   /wl4098
3202>   /wl4099
3202>   /wl4101
3202>   /wl4102
3202>   /wl4109
3202>   /wl4112
3202>   /wl4113
3202>   /wl4114
3202>   /wl4115
3202>   /wl4116
3202>   /wl4117
3202>   /wl4119
3202>   /wl4120
3202>   /wl4122
3202>   /wl4124
3202>   /wl4129
3202>   /wl4133
3202>   /wl4138
3202>   /wl4141
3202>   /wl4142
3202>   /wl4143
3202>   /wl4144
3202>   /wl4145
3202>   /wl4150
3202>   /wl4153
3202>   /wl4154
3202>   /wl4155
3202>   /wl4156
3202>   /wl4157
3202>   /wl4158
3202>   /wl4159
3202>   /wl4160
3202>   /wl4161
3202>   /wl4162
3202>   /wl4163
3202>   /wl4164
3202>   /wl4166
3202>   /wl4167
3202>   /wl4168
3202>   /wl4172
3202>   /wl4174
3202>   /wl4175
3202>   /wl4176
3202>   /wl4177
3202>   /wl4178
3202>   /wl4180
3202>   /wl4182
3202>   /wl4183
3202>   /wl4185
3202>   /wl4186
3202>   /wl4187
3202>   /wl4190
3202>   /wl4192
3202>   /wl4197
3202>   /wl4200
3202>   /wl4213
3202>   /wl4215
3202>   /wl4216
3202>   /wl4218
3202>   /wl4223
3202>   /wl4224
3202>   /wl4226
3202>   /wl4227
3202>   /wl4228
3202>   /wl4229
3202>   /wl4230
3202>   /wl4237
3202>   /wl4240
3202>   /wl4243
3202>   /wl4244
3202>   /wl4250
3202>   /wl4251
3202>   /wl4258
3202>   /wl4267
3202>   /wl4269
3202>   /wl4272
3202>   /wl4273
3202>   /wl4274
3202>   /wl4275
3202>   /wl4276
3202>   /wl4278
3202>   /wl4280
3202>   /wl4281
3202>   /wl4282
3202>   /wl4283
3202>   /wl4285
3202>   /wl4286
3202>   /wl4288
3202>   /wl4290
3202>   /wl4291
3202>   /wl4293
3202>   /wl4297
3202>   /wl4302
3202>   /wl4305
3202>   /wl4306
3202>   /wl4307
3202>   /wl4309
3202>   /wl4310
3202>   /wl4311
3202>   /wl4312
3202>   /wl4313
3202>   /wl4316
3202>   /wl4319
3202>   /wl4325
3202>   /wl4326
3202>   /wl4329
3202>   /wl4333
3202>   /wl4334
3202>   /wl4335
3202>   /wl4340
3202>   /wl4344
3202>   /wl4346
3202>   /wl4348
3202>   /wl4353
3202>   /wl4356
3202>   /wl4357
3202>   /wl4358
3202>   /wl4359
3202>   /wl4364
3202>   /wl4368
3202>   /wl4369
3202>   /wl4373
3202>   /wl4374
3202>   /wl4375
3202>   /wl4376
3202>   /wl4377
3202>   /wl4378
3202>   /wl4379
3202>   /wl4381
3202>   /wl4382
3202>   /wl4383
3202>   /wl4384
3202>   /wl4390
3202>   /wl4391
3202>   /wl4392
3202>   /wl4393
3202>   /wl4394
3202>   /wl4395
3202>   /wl4396
3202>   /wl4397
3202>   /wl4398
3202>   /wl4399
3202>   /wl4600
3202>   /wl4401
3202>   /wl4402
3202>   /wl4403
3202>   /wl4404
3202>   /wl4405
3202>   /wl4406
3202>   /wl4407
3202>   /wl4409
3202>   /wl4410
3202>   /wl4411
3202>   /wl4414
3202>   /wl4420
3202>   /wl4430
3202>   /wl4436
3202>   /wl4439
3202>   /wl4440
3202>   /wl4441
3202>   /wl4445
3202>   /wl4461
3202>   /wl4462
3202>   /wl4470
3202>   /wl4473
3202>   /wl4477
3202>   /wl4484
3202>   /wl4485
3202>   /wl4486
3202>   /wl4488
3202>   /wl4489
3202>   /wl4490
3202>   /wl4502
3202>   /wl4503
3202>   /wl4506
3202>   /wl4508
3202>   /wl4511
3202>   /wl4518
3202>   /wl4521
3202>   /wl4522
3202>   /wl4523
3202>   /wl4526
3202>   /wl4530
3202>   /wl4534
3202>   /wl4535
3202>   /wl4537
3202>   /wl4538
3202>   /wl4540
3202>   /wl4541
3202>   /wl4543
3202>   /wl4544
3202>   /wl4550
3202>   /wl4551
3202>   /wl4552
3202>   /wl4553
3202>   /wl4554
3202>   /wl4556
3202>   /wl4558
3202>   /wl4561
3202>   /wl4566
3202>   /wl4570
3202>   /wl4572
3202>   /wl4580
3202>   /wl4581
3202>   /wl4584
3202>   /wl4596
3202>   /wl4597
3202>   /wl4602
3202>   /wl4603
3202>   /wl4606
3202>   /wl4612
3202>   /wl4613
3202>   /wl4615
3202>   /wl4616
3202>   /wl4618
3202>   /wl4620
3202>   /wl4621
3202>   /wl4622
3202>   /wl4624
3202>   /wl4627
3202>   /wl4630
3202>   /wl4632
3202>   /wl4633
3202>   /wl4635
3202>   /wl4636
3202>   /wl4637
3202>   /wl4638
3202>   /wl4641
3202>   /wl4645
3202>   /wl4646
3202>   /wl4650
3202>   /wl4651
3202>   /wl4652
3202>   /wl4653
3202>   /wl4655
3202>   /wl4656
3202>   /wl4657
3202>   /wl4659
3202>   /wl4661
3202>   /wl4662
3202>   /wl4667
3202>   /wl4669
3202>   /wl4674
3202>   /wl4677
3202>   /wl4678
3202>   /wl4679
3202>   /wl4683
3202>   /wl4684
3202>   /wl4685
3202>   /wl4687
3202>   /wl4688
3202>   /wl4691
3202>   /wl4693
3202>   /wl4694
3202>   /wl4698
3202>   /wl4711
3202>   /wl4715
3202>   /wl4716
3202>   /wl4717
3202>   /wl4722
3202>   /wl4723
3202>   /wl4724
3202>   /wl4727
3202>   /wl4730
3202>   /wl4731
3202>   /wl4733
3202>   /wl4739
3202>   /wl4742
3202>   /wl4743
3202>   /wl4744
3202>   /wl4747
3202>   /wl4750
3202>   /wl4756
3202>   /wl4768
3202>   /wl4772
3202>   /wl4788
3202>   /wl4793
3202>   /wl4794
3202>   /wl4799
3202>   /wl4803
3202>   /wl4804
3202>   /wl4805
3202>   /wl4806
3202>   /wl4807
3202>   /wl4810
3202>   /wl4811
3202>   /wl4812
3202>   /wl4813
3202>   /wl4817
3202>   /wl4819
3202>   /wl4821
3202>   /wl4823
3202>   /wl4829
3202>   /wl4834
3202>   /wl4835
3202>   /wl4838
3202>   /wl4839
3202>   /wl4867
3202>   /wl4900
3202>   /wl4910
3202>   /wl4912
3202>   /wl4920
3202>   /wl4925
3202>   /wl4926
3202>   /wl4927
3202>   /wl4929
3202>   /wl4930
3202>   /wl4935
3202>   /wl4936
3202>   /wl4939
3202>   /wl4944
3202>   /wl4945
3202>   /wl4947
3202>   /wl4948
3202>   /wl4949
3202>   /wl4950
3202>   /wl4951
3202>   /wl4952
3202>   /wl4953
3202>   /wl4956
3202>   /wl4957
3202>   /wl4958
3202>   /wl4959
3202>   /wl4961
3202>   /wl4964
3202>   /wl4965
3202>   /wl4972
3202>   /wl4984
3202>   /wl4995
3202>   /wl4996
3202>   /wl4997
3202>   /wl4999
3202>   /wl5033
3202>   /wl5037
3202>   /wl5046
3202>   /wl5050
3202>   /wl5055
3202>   /wl5056
3202>   /wl5105
3202>   /wl5208
3202>   /d2Qvec-mathlib-
3202>   /d2Qvec-sse2only
3202>   /Gw
3202>   /Zc:checkGwOdr
3202>   /d1ignorePragmaWarningError
3202>   /wd4316
3202>   /wd4973
3202>   /DDONT_DISABLE_PCH_WARNINGS_IN_WARNING_H
3202>   /d2FH4
3202>   /Brepro
3202>   -D_HAS_MAGIC_STATICS=1
3202>   /Qspectre
3202>   /wd5045
3202>   /d2guardspecanalysismode:v1_0
3202>   /d2guardspecmode2
3202>   /guard:cf
3202>   /d2guardcfgfuncptr-
3202>   /d2guardcfgdispatch
3202>   /guard:ehcont
3202>   -D__PLACEHOLDER_SAL=1
3202>   -wd4425
3202>   @e:\os\obj\amd64fre\objfre\amd64\WarningsCop\OneCore.rsp
3202>   /wl4146 /wl4308 /wl4509 /wl4510 /wl4532 /wl4533 /wl4610 /wl4700 /wl4701 /wl4703 /wl4789
3202>   /FIe:\os\public\amd64fre\onecore\internal\sdk\inc\warning.h
3202>   /std:c++17
3202>   /Yupch.hxx /Fpe:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\pch.pch
3202>   .\aimxrpcserver.cpp .\requesthandler.cpp .\planner.cpp .\orchestrator.cpp .\mcpstdioclient.cpp .\mcpsvrmgr.cpp .\mcptoolmanager.cpp .\llminfer.cpp .\aimxllmconfig.cpp .\inprocessmcpserverbase.cpp .\inprocessmcputils.cpp .\systempromptmanager.cpp .\conversationmanager.cpp .\ragservicemanager.cpp 
3202>aimxrpcserver.cpp
3202>requesthandler.cpp
3202>planner.cpp
3202>orchestrator.cpp
3202>mcpstdioclient.cpp
3202>mcpsvrmgr.cpp
3202>mcptoolmanager.cpp
3202>llminfer.cpp
3202>aimxllmconfig.cpp
3202>inprocessmcpserverbase.cpp
3202>inprocessmcputils.cpp
3202>systempromptmanager.cpp
3202>conversationmanager.cpp
3202>ragservicemanager.cpp
3202> e:\os\tools\vc\HostX64\amd64\link.exe /lib /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\aimxserver.lib /IGNORE:4078,4221,4281,4006,4198   /nodefaultlib /machine:amd64 /ltcg /Brepro @e:\os\obj\amd64fre\temp\8827d5d687c690bb09535de14a400675\lib_1.rsp
3202>Microsoft (R) Library Manager Version 14.42.34444.100
3202>Copyright (C) Microsoft Corporation.  All rights reserved.
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\pch.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\aimxrpcserver.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\requesthandler.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\planner.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\orchestrator.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\mcpstdioclient.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\mcpsvrmgr.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\mcptoolmanager.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\llminfer.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\aimxllmconfig.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\inprocessmcpserverbase.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\inprocessmcputils.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\systempromptmanager.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\conversationmanager.obj 
3202>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\ragservicemanager.obj 
3202>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\Macros-PASS1.txt
3202>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\Macros-PASS1.txt
3202> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\server" -Pass PASS1 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3202>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\server in pass PASS1
BUILD: Pass complete => PASS1
1>  1>[0:00:56.782] [Pass2 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell {39}
3001>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
3001>Linking for e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell *************
3001>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS2 LINKONLY=1 NOPASS0=1 MAKEDLL=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  2>[0:00:56.782] [Pass2 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll {40}
3002>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
3002>Linking for e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll *************
3002>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS2 LINKONLY=1 NOPASS0=1 MAKEDLL=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  3>[0:00:56.782] [Pass2 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll {41}
3003>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
3003>Linking for e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll *************
3003>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS2 LINKONLY=1 NOPASS0=1 MAKEDLL=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
3004>BUILDMSG: Processing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller
3004>Linking for e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller *************
3004>'e:\os\tools\NMakeRust\x64\bin\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\os\src\tools\makefile.def BUILD_PASS=PASS2 LINKONLY=1 NOPASS0=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox'
1>  4>[0:00:56.782] [Pass2 ] e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller {42}
BUILD: (ActiveWorkLoad),56.56,,56,1,3,10,16,0,0,0,0,0,0,0,PASS2,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\powershell,RUPO-DELL
BUILD: (ActiveWorkLoad),56.56,,56,1,3,10,16,16,0,1,1,1,0,0,PASS2,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\dll,RUPO-DELL
BUILD: (ActiveWorkLoad),56.56,,56,1,3,10,16,16,0,2,2,2,0,0,PASS2,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll,RUPO-DELL
BUILD: (ActiveWorkLoad),56.56,,56,1,3,10,16,16,0,3,3,3,0,0,PASS2,0,2000000000,onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller,RUPO-DELL
3001>Calculated LAYERINFO_MODULE='OneCoreDS'.
3001>makefile.def: TEMP=e:\os\obj\amd64fre\temp\ff7006f934c9d27f24f90d1d318fb6d1
3001>makefile.def: BUILDINGINDATT=
3001>[Core OS Undocking] NOT using package ''
3001>ObjectsMac.ts: validation succeeded
3001>Starting recursive call to NMAKE for _MAKING_ASMID_INC
3001>Starting _MAKING_ASMID_INC
3001>Calculated LAYERINFO_MODULE='OneCoreDS'.
3001>makefile.def: TEMP=e:\os\obj\amd64fre\temp\ff7006f934c9d27f24f90d1d318fb6d1
3001>makefile.def: BUILDINGINDATT=
3001>[Core OS Undocking] NOT using package ''
3001>ObjectsMac.ts: _objects.mac is not needed; macro validation will be skipped.
3001>BUILDMSG: Checking if we need to generate coffbase.mac file
3001>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3001>BUILDMSG: Optional SPD_INPUT location is e:\os\pgo\amd64fre\spds\\\aimxpsh.dll.spd
3001>OSSCop: getOSSBaselines: Parsing baselines looking for macroDefFiles [e:\os\src\tools\makefile.def]
3001>Ending _MAKING_ASMID_INC
3001>'e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_asmid.inc' is up-to-date
3001>Finished recursively calling NMAKE for _MAKING_ASMID_INC
3001>BUILDMSG: Checking if we need to generate coffbase.mac file
3001>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3001>BUILDMSG: Optional SPD_INPUT location is e:\os\pgo\amd64fre\spds\\\aimxpsh.dll.spd
3001>OSSCop: getOSSBaselines: Parsing baselines looking for macroDefFiles [e:\os\src\tools\makefile.def]
3001>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64 already exists.
3001> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_PASS2_Marker.log
3001> e:\os\osdep\feature.toggles\tools\ft_scraper.exe -pdb:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\aimxpsh.pdb -out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\ft_scraped_aimxpsh.ft -binplace_path:bvtbin\feature_toggles\scraped\OneCore\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
3001>binplace e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\AIMX.psd1
3001> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\ff7006f934c9d27f24f90d1d318fb6d1\post_link_concurrent.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\ff7006f934c9d27f24f90d1d318fb6d1\post_link_concurrent.rsp
3001> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\ff7006f934c9d27f24f90d1d318fb6d1\post_link_concurrent2.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\ff7006f934c9d27f24f90d1d318fb6d1\post_link_concurrent2.rsp
3001>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\Macros-PASS2.txt
3001>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\Macros-PASS2.txt
3001> e:\os\tools\powershell\pwsh.exe -NoProfile -Command e:\os\src\tools\NMakeJS\CheckCFlags\CheckCFlags.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\powershell" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\CheckCFlags.json -OutputDir "e:\os\bin\amd64fre\build_logs\DMFDebt\build_logs\CheckCFlags\OneCore\onecoreds"
3001>CheckCFlags.ps1 : Processing onecore\ds\ds\src\aimx\prod\aimxsrv\powershell in pass PASS2
3001>CheckCFlags.ps1 : Running e:\os\src\tools\urtrun64.cmd 4.Latest e:\os\tools\checkcflags\cs\checkcflags2.exe Scan-List /ListFile:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\CheckCFlags_scanlist.txt /PolicyConfigFilePath:E:\os\src\tools\NMakeJS\CheckCFlags\CCF_PolicyConfig.json /LogDirectory:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64
3001> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\powershell" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3001>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\powershell in pass PASS2
3003>Calculated LAYERINFO_MODULE='OneCoreDS'.
3003>makefile.def: TEMP=e:\os\obj\amd64fre\temp\0f11cd6d95cd3a46f0fac60593b4232e
3003>makefile.def: BUILDINGINDATT=
3003>[Core OS Undocking] NOT using package ''
3003>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll' (target 'aimxclient', type 'DYNLINK', nt_target_version '0xA000011')
3003>ObjectsMac.ts: validation succeeded
3003>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" (STL_VER_TELEMETRY)
3003>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3003>BUILDMSG: Optional SPD_INPUT location is e:\os\pgo\amd64fre\spds\\\aimxclient.dll.spd
3003>OSSCop: getOSSBaselines: Parsing baselines looking for macroDefFiles [e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\sources, e:\os\src\tools\makefile.ucrt, e:\os\src\tools\makefile.def]
3003>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64 already exists.
3003> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\_PASS2_Marker.log
3003> e:\os\tools\vc\HostX64\amd64\link.exe /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll  /machine:amd64 @e:\os\obj\amd64fre\temp\0f11cd6d95cd3a46f0fac60593b4232e\lnk.rsp
3003>Microsoft (R) Incremental Linker Version 14.42.34444.100
3003>Copyright (C) Microsoft Corporation.  All rights reserved.
3003>/filealign:0x1000 
3003>/INCLUDE:__PLEASE_LINK_WITH_legacy_stdio_wide_specifiers.lib 
3003>/INCLUDE:__scrt_stdio_legacy_msvcrt_compatibility 
3003>/NOVCFEATURE 
3003>/d2:-DeepThoughtInliner- 
3003>/d2:-DisableWPASpecializeParam 
3003>/d2:-implyavx512upperregs- 
3003>/RunBelow4GB 
3003>/nopdbprefetch 
3003>-d2:-TypeProp- 
3003>-d2:-SpecDevirt- 
3003>/RetryOnFileOpenFailure 
3003>e:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Lib\hotpatchspareglobals.obj 
3003>/map 
3003>/pdbinject:mapfile 
3003>/baserelocclustering 
3003>/SPGO 
3003>/SPD:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll.spd 
3003>-ignore:4199 
3003>/MERGE:_PAGE=PAGE 
3003>/MERGE:_TEXT=.text 
3003>/MERGE:_RDATA=.rdata 
3003>/OPT:REF 
3003>/OPT:ICF 
3003>/IGNORE:4078,4221,4281,4006,4198 
3003>/INCREMENTAL:NO 
3003>/release 
3003>/NODEFAULTLIB 
3003>/debug 
3003>/debugtype:cv,fixup,pdata 
3003>/version:10.0 
3003>/osversion:10.0 
3003>/ltcg 
3003>/funcoverride 
3003>/d2:-FH4 
3003>/Brepro 
3003>/PDBDLL:mspdbcore.dll 
3003>/functionpadmin:6 
3003>/MERGE:.orpc=.text 
3003>/hotpatchcompatible 
3003>/d2:-guardspecload 
3003>/d2:-guardspecanalysismode:v1_0 
3003>/d2:-guardspecmode2 
3003>/ignore:4291 
3003>/DynamicValueFixupSym:mm_shared_user_data_va=0x7FFE0000 
3003>/DynamicValueFixupSym:ki_user_shared_data=0xFFFFF78000000000 
3003>/guard:cf 
3003>/d2:-guardcfgfuncptr- 
3003>/merge:.gfids=.rdata 
3003>/d2:-guardcfgdispatch 
3003>/guard:ehcont 
3003>/CETCOMPAT 
3003>/pdbcompress 
3003>/STACK:0x40000,0x1000 
3003>/dll 
3003>/subsystem:console,10.00 
3003>/entry:_DllMainCRTStartup 
3003>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\pch.obj 
3003>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.exp 
3003>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.obj 
3003>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxrpcclient.obj 
3003>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\memory.obj 
3003>e:\os\public\amd64fre\onecore\external\sdk\lib\atls.lib 
3003>e:\os\public\amd64fre\onecore\external\sdk\lib\atlthunk.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\msvcprt.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_wide_specifiers.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_msvcrt_compatibility.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\msvcrt.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\osmode_function_map.obj 
3003>e:\os\public\amd64fre\onecore\external\sdk\lib\ucrt.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt_private.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_definitions.lib 
3003>e:\os\public\amd64fre\onecore\external\sdk\lib\MinWin\ntdll.lib 
3003>e:\os\public\amd64fre\onecore\external\sdk\lib\MinWin\rpcrt4.lib 
3003>e:\os\public\amd64fre\onecore\external\sdk\lib\onecore.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\MinWin\1.21\api-ms-win-security-sddl-l1.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\MinWin\1.21\api-ms-win-security-base-l1.lib 
3003>e:\os\public\amd64fre\onecore\internal\sdk\lib\guard_support.lib 
3003>/pdbrpc:no 
3003>Generating code
3003>SPD e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll.spd not found, compiling without profile guided optimizations
3003>Finished generating code
3003> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.ilk.persistent e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll.persistent e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.pdb.persistent 2>nul
3003> c:\windows\system32\cmd.exe /c e:\os\src\tools\urtrun.cmd 4.Latest e:\os\tools\FixTSVersionStringAppend\bin\FixTsVersionStringAppend\release\FixTSVersionStringAppend.exe /fts:e:\os\tools\FixTS\FixTS.exe /pe=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll
3003>[e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll]
3003>No version resources present.
3003>Microsoft (R) COFF/PE Editor Version 14.42.34444.100
3003>Copyright (C) Microsoft Corporation.  All rights reserved.
3003>Updating PDB GUID and/or timestamp in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll
3003>Updating PDB GUID and/or timestamp in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.pdb
3003>Original RSDSKEY:{21AFB07D-8240-0DE1-70FB-B5EC72F188EC}:1
3003>New RSDSKEY:     {21AFB07D-8240-0DE1-70FB-B5EC72F188EC}:1
3003> e:\os\tools\perl64\bin\perl.exe e:\os\src\tools\FeatureStaging\LogInliningFailures.pl /makedir:onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll
3003> e:\os\tools\deferredbinplace\DeferredBinplace.exe  e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll  PASS2  e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\binplace_PASS2.rsp  @e:\os\tools\binplace\binplace.exe  /R e:\os\bin\amd64fre\.  /s e:\os\bin\amd64fre\Symbols.pri\. /j /:DBG /:NOCV  -f -:LOGPDB /:CVTCIL /:SYMBAD e:\os\src\tools\symbad.txt /:TMF   /:DEST retail      e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll
3003>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll
3003>BINPLACE : INFORMATION BNP0017: Can't detect version resource in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll. 0x000000B7 - Cannot create a file when that file already exists.
3003> c:\windows\system32\cmd.exe /c if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.dll.mui (  echo Build_Status  LN_MUI_STS: LGNSTS_UNKNOWN aimxclient.dll  )
3003>Build_Status  LN_MUI_STS: LGNSTS_UNKNOWN aimxclient.dll  
3003> e:\os\osdep\feature.toggles\tools\ft_scraper.exe -pdb:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\aimxclient.pdb -out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\ft_scraped_aimxclient.ft -binplace_path:bvtbin\feature_toggles\scraped\OneCore\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
3003> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\0f11cd6d95cd3a46f0fac60593b4232e\post_link_concurrent.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\0f11cd6d95cd3a46f0fac60593b4232e\post_link_concurrent.rsp
3003> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\0f11cd6d95cd3a46f0fac60593b4232e\post_link_concurrent2.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\0f11cd6d95cd3a46f0fac60593b4232e\post_link_concurrent2.rsp
3003>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\Macros-PASS2.txt
3003>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\Macros-PASS2.txt
3003> e:\os\tools\powershell\pwsh.exe -NoProfile -Command e:\os\src\tools\NMakeJS\CheckCFlags\CheckCFlags.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\CheckCFlags.json -OutputDir "e:\os\bin\amd64fre\build_logs\DMFDebt\build_logs\CheckCFlags\OneCore\onecoreds"
3003>CheckCFlags.ps1 : Processing onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll in pass PASS2
3003>CheckCFlags.ps1 : Running e:\os\src\tools\urtrun64.cmd 4.Latest e:\os\tools\checkcflags\cs\checkcflags2.exe Scan-List /ListFile:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\CheckCFlags_scanlist.txt /PolicyConfigFilePath:E:\os\src\tools\NMakeJS\CheckCFlags\CCF_PolicyConfig.json /LogDirectory:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64
3003> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3003>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll in pass PASS2
3004>Calculated LAYERINFO_MODULE='OneCoreDS'.
3004>makefile.def: TEMP=e:\os\obj\amd64fre\temp\6e2ab89d1ecb70e0fdc918c3171c8bc8
3004>makefile.def: BUILDINGINDATT=
3004>[Core OS Undocking] NOT using package ''
3004>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller' (target 'Aimx.ServiceInstaller', type 'PROGRAM', nt_target_version '0xA000011')
3004>ObjectsMac.ts: validation succeeded
3004>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3004>BUILDMSG: Optional SPD_INPUT location is e:\os\pgo\amd64fre\spds\\\Aimx.ServiceInstaller.exe.spd
3004>c:\windows\system32\cmd.exe /c e:\os\src\tools\urtrun.cmd 4.Latest e:\os\tools\apisettools\apiset.expanddelayload.exe /d:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\delayload.txt /o:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\delayload_expansion.inc
3004>OSSCop: getOSSBaselines: Parsing baselines looking for macroDefFiles [e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\sources, e:\os\src\tools\makefile.ucrt, e:\os\src\tools\makefile.def]
3004>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64 already exists.
3004> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\_PASS2_Marker.log
3004> e:\os\tools\vc\HostX64\amd64\link.exe /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe  /machine:amd64 @e:\os\obj\amd64fre\temp\6e2ab89d1ecb70e0fdc918c3171c8bc8\lnk.rsp
3004>Microsoft (R) Incremental Linker Version 14.42.34444.100
3004>Copyright (C) Microsoft Corporation.  All rights reserved.
3004>/filealign:0x1000 
3004>/INCLUDE:__PLEASE_LINK_WITH_legacy_stdio_wide_specifiers.lib 
3004>/INCLUDE:__scrt_stdio_legacy_msvcrt_compatibility 
3004>/NOVCFEATURE 
3004>/d2:-DeepThoughtInliner- 
3004>/d2:-DisableWPASpecializeParam 
3004>/d2:-implyavx512upperregs- 
3004>/RunBelow4GB 
3004>/nopdbprefetch 
3004>-d2:-TypeProp- 
3004>-d2:-SpecDevirt- 
3004>/RetryOnFileOpenFailure 
3004>e:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Lib\hotpatchspareglobals.obj 
3004>/map 
3004>/pdbinject:mapfile 
3004>/baserelocclustering 
3004>/SPGO 
3004>/SPD:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe.spd 
3004>-ignore:4199 
3004>/MERGE:_PAGE=PAGE 
3004>/MERGE:_TEXT=.text 
3004>/MERGE:_RDATA=.rdata 
3004>/OPT:REF 
3004>/OPT:ICF 
3004>/IGNORE:4078,4221,4281,4006,4198 
3004>/INCREMENTAL:NO 
3004>/release 
3004>/NODEFAULTLIB 
3004>/debug 
3004>/debugtype:cv,fixup,pdata 
3004>/version:10.0 
3004>/osversion:10.0 
3004>/ltcg 
3004>/funcoverride 
3004>/d2:-FH4 
3004>/Brepro 
3004>/PDBDLL:mspdbcore.dll 
3004>/functionpadmin:6 
3004>/MERGE:.orpc=.text 
3004>/hotpatchcompatible 
3004>/d2:-guardspecload 
3004>/d2:-guardspecanalysismode:v1_0 
3004>/d2:-guardspecmode2 
3004>/ignore:4291 
3004>/DynamicValueFixupSym:mm_shared_user_data_va=0x7FFE0000 
3004>/DynamicValueFixupSym:ki_user_shared_data=0xFFFFF78000000000 
3004>/guard:cf 
3004>/d2:-guardcfgfuncptr- 
3004>/merge:.gfids=.rdata 
3004>/d2:-guardcfgdispatch 
3004>/guard:ehcont 
3004>/pdbcompress 
3004>/delayload:api-ms-win-core-crt-l2*.dll 
3004>/delayload:api-ms-win-service-management-l1*.dll 
3004>/delayload:userenv.dll 
3004>/STACK:0x80000,0x2000 
3004>/tsaware 
3004>/highentropyva 
3004>/subsystem:console,10.00 
3004>/entry:wmainCRTStartup 
3004>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\main.obj 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\dloadhelper.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_wide_specifiers.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_msvcrt_compatibility.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\msvcrt.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\osmode_function_map.obj 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\ucrt.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt_private.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_definitions.lib 
3004>e:\os\public\amd64fre\onecore\external\sdk\lib\mincore.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\MinWin\1.21\api-ms-win-core-crt-l2.lib 
3004>e:\os\public\amd64fre\onecore\internal\sdk\lib\guard_support.lib 
3004>e:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Lib\1.21\api-ms-win-core-delayload-l1.lib 
3004>/pdbrpc:no 
3004>Generating code
3004>SPD e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe.spd not found, compiling without profile guided optimizations
3004>Finished generating code
3004> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.ilk.persistent e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe.persistent e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.pdb.persistent 2>nul
3004> c:\windows\system32\cmd.exe /c e:\os\src\tools\urtrun.cmd 4.Latest e:\os\tools\FixTSVersionStringAppend\bin\FixTsVersionStringAppend\release\FixTSVersionStringAppend.exe /fts:e:\os\tools\FixTS\FixTS.exe /pe=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3004>[e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe]
3004>No version resources present.
3004>Microsoft (R) COFF/PE Editor Version 14.42.34444.100
3004>Copyright (C) Microsoft Corporation.  All rights reserved.
3004>Updating PDB GUID and/or timestamp in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3004>Updating PDB GUID and/or timestamp in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.pdb
3004>Original RSDSKEY:{268E6625-712F-68DB-9E62-BDB52F648FEB}:1
3004>New RSDSKEY:     {268E6625-712F-68DB-9E62-BDB52F648FEB}:1
3004> e:\os\tools\perl64\bin\perl.exe e:\os\src\tools\FeatureStaging\LogInliningFailures.pl /makedir:onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3004> e:\os\tools\deferredbinplace\DeferredBinplace.exe  e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller  PASS2  e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\binplace_PASS2.rsp  @e:\os\tools\binplace\binplace.exe  /R e:\os\bin\amd64fre\.  /s e:\os\bin\amd64fre\Symbols.pri\. /j /:DBG /:NOCV  -f -:LOGPDB /:CVTCIL /:SYMBAD e:\os\src\tools\symbad.txt   /:DEST retail      e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3004>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3004>BINPLACE : INFORMATION BNP0017: Can't detect version resource in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe. 0x000000B7 - Cannot create a file when that file already exists.
3004> c:\windows\system32\cmd.exe /c if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe.mui (  echo Build_Status  LN_MUI_STS: LGNSTS_UNKNOWN Aimx.ServiceInstaller.exe  )
3004>Build_Status  LN_MUI_STS: LGNSTS_UNKNOWN Aimx.ServiceInstaller.exe  
3004> e:\os\tools\perl64\bin\perl.exe e:\os\src\tools\check_delayload.pl e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3004>(check_delayload.pl) Command: check_delayload e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.exe
3004>(check_delayload.pl) Started at Thu Jul 24 21:41:53 2025
3004>(check_delayload.pl) reading e:\os\public\amd64fre\onecore\internal\minwin\priv_sdk\lib\dload.dload
3004>All delayloadable dlls are already delay loaded.
3004>The following dlls require stubs before they can be delay loaded:
3004> api-ms-win-core-debug-l1-1-0.dll (1 imports):
3004>  IsDebuggerPresent
3004> api-ms-win-core-delayload-l1-1-0.dll (1 imports):
3004>  DelayLoadFailureHook
3004> api-ms-win-core-delayload-l1-1-1.dll (1 imports):
3004>  ResolveDelayLoadedAPI
3004> api-ms-win-core-errorhandling-l1-1-0.dll (3 imports):
3004>  GetLastError
3004>  SetUnhandledExceptionFilter
3004>  UnhandledExceptionFilter
3004> api-ms-win-core-interlocked-l1-1-0.dll (1 imports):
3004>  InitializeSListHead
3004> api-ms-win-core-libraryloader-l1-2-0.dll (1 imports):
3004>  GetModuleHandleW
3004> api-ms-win-core-processenvironment-l1-1-0.dll (1 imports):
3004>  ExpandEnvironmentStringsW
3004> api-ms-win-core-processthreads-l1-1-0.dll (4 imports):
3004>  GetCurrentProcess
3004>  GetCurrentProcessId
3004>  GetCurrentThreadId
3004>  TerminateProcess
3004> api-ms-win-core-processthreads-l1-1-1.dll (1 imports):
3004>  IsProcessorFeaturePresent
3004> api-ms-win-core-profile-l1-1-0.dll (1 imports):
3004>  QueryPerformanceCounter
3004> api-ms-win-core-registry-l1-1-0.dll (4 imports):
3004>  RegCloseKey
3004>  RegCreateKeyExW
3004>  RegOpenKeyExW
3004>  RegSetValueExW
3004> api-ms-win-core-sysinfo-l1-1-0.dll (1 imports):
3004>  GetSystemTimeAsFileTime
3004> api-ms-win-crt-private-l1-1-0.dll (23 imports):
3004>  __C_specific_handler
3004>  __current_exception
3004>  __current_exception_context
3004>  _o___acrt_iob_func
3004>  _o___p___argc
3004>  _o___p___wargv
3004>  _o___p__commode
3004>  _o___stdio_common_vfwprintf
3004>  _o__cexit
3004>  _o__configthreadlocale
3004>  _o__configure_wide_argv
3004>  _o__crt_atexit
3004>  _o__exit
3004>  _o__get_initial_wide_environment
3004>  _o__initialize_onexit_table
3004>  _o__initialize_wide_environment
3004>  _o__register_onexit_function
3004>  _o__seh_filter_exe
3004>  _o__set_app_type
3004>  _o__set_fmode
3004>  _o__set_new_mode
3004>  _o_exit
3004>  _o_terminate
3004> api-ms-win-crt-runtime-l1-1-0.dll (4 imports):
3004>  _c_exit
3004>  _initterm
3004>  _initterm_e
3004>  _register_thread_local_exe_atexit_callback
3004> api-ms-win-crt-string-l1-1-0.dll (1 imports):
3004>  memset
3004>(check_delayload.pl) Ended at Thu Jul 24 21:41:53 2025 with ExitCode:0
3004>(check_delayload.pl) Elapsed time 0 seconds
3004> e:\os\osdep\feature.toggles\tools\ft_scraper.exe -pdb:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Aimx.ServiceInstaller.pdb -out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\ft_scraped_Aimx.ServiceInstaller.ft -binplace_path:bvtbin\feature_toggles\scraped\OneCore\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller
3004> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\6e2ab89d1ecb70e0fdc918c3171c8bc8\post_link_concurrent.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\6e2ab89d1ecb70e0fdc918c3171c8bc8\post_link_concurrent.rsp
3004> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\6e2ab89d1ecb70e0fdc918c3171c8bc8\post_link_concurrent2.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\6e2ab89d1ecb70e0fdc918c3171c8bc8\post_link_concurrent2.rsp
3004>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Macros-PASS2.txt
3004>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\Macros-PASS2.txt
3004> e:\os\tools\powershell\pwsh.exe -NoProfile -Command e:\os\src\tools\NMakeJS\CheckCFlags\CheckCFlags.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\CheckCFlags.json -OutputDir "e:\os\bin\amd64fre\build_logs\DMFDebt\build_logs\CheckCFlags\OneCore\onecoreds"
3004>CheckCFlags.ps1 : Processing onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller in pass PASS2
3004>CheckCFlags.ps1 : Running e:\os\src\tools\urtrun64.cmd 4.Latest e:\os\tools\checkcflags\cs\checkcflags2.exe Scan-List /ListFile:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\CheckCFlags_scanlist.txt /PolicyConfigFilePath:E:\os\src\tools\NMakeJS\CheckCFlags\CCF_PolicyConfig.json /LogDirectory:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64
3004> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3004>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller in pass PASS2
3002>Calculated LAYERINFO_MODULE='OneCoreDS'.
3002>makefile.def: TEMP=e:\os\obj\amd64fre\temp\e92cce39887833cc17b7bfc3fdcdbc35
3002>makefile.def: BUILDINGINDATT=
3002>[Core OS Undocking] NOT using package ''
3002>UCRT enabled: dir 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll' (target 'aimxsrv', type 'DYNLINK', nt_target_version '0xA000011')
3002>ObjectsMac.ts: validation succeeded
3002>STL version 120 used in "e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll" (STL_VER_TELEMETRY)
3002>_NEED_BUILDDATE not defined setting BUILDDATE to an invalid value.
3002>BUILDMSG: Optional SPD_INPUT location is e:\os\pgo\amd64fre\spds\\\aimxsrv.dll.spd
3002>c:\windows\system32\cmd.exe /c e:\os\src\tools\urtrun.cmd 4.Latest e:\os\tools\apisettools\apiset.expanddelayload.exe /d:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\delayload.txt /o:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\delayload_expansion.inc
3002>OSSCop: getOSSBaselines: Parsing baselines looking for macroDefFiles [e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\sources, e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk\consume.inc, e:\os\src\tools\makefile.ucrt, e:\os\src\tools\makefile.def]
3002>A subdirectory or file e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64 already exists.
3002> e:\os\tools\Windows.Desktop.Tools.amd64\tools\touch.exe /c e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\_PASS2_Marker.log
3002> e:\os\tools\vc\HostX64\amd64\link.exe /out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll  /machine:amd64 @e:\os\obj\amd64fre\temp\e92cce39887833cc17b7bfc3fdcdbc35\lnk.rsp
3002>Microsoft (R) Incremental Linker Version 14.42.34444.100
3002>Copyright (C) Microsoft Corporation.  All rights reserved.
3002>/filealign:0x1000 
3002>/INCLUDE:__PLEASE_LINK_WITH_legacy_stdio_wide_specifiers.lib 
3002>/INCLUDE:__scrt_stdio_legacy_msvcrt_compatibility 
3002>/NOVCFEATURE 
3002>/d2:-DeepThoughtInliner- 
3002>/d2:-DisableWPASpecializeParam 
3002>/d2:-implyavx512upperregs- 
3002>/RunBelow4GB 
3002>/nopdbprefetch 
3002>-d2:-TypeProp- 
3002>-d2:-SpecDevirt- 
3002>/RetryOnFileOpenFailure 
3002>e:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Lib\hotpatchspareglobals.obj 
3002>/map 
3002>/pdbinject:mapfile 
3002>/baserelocclustering 
3002>/SPGO 
3002>/SPD:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll.spd 
3002>-ignore:4199 
3002>/MERGE:_PAGE=PAGE 
3002>/MERGE:_TEXT=.text 
3002>/MERGE:_RDATA=.rdata 
3002>/OPT:REF 
3002>/OPT:ICF 
3002>/IGNORE:4078,4221,4281,4006,4198 
3002>/INCREMENTAL:NO 
3002>/release 
3002>/NODEFAULTLIB 
3002>/debug 
3002>/debugtype:cv,fixup,pdata 
3002>/version:10.0 
3002>/osversion:10.0 
3002>/ltcg 
3002>/funcoverride 
3002>/d2:-FH4 
3002>/Brepro 
3002>/PDBDLL:mspdbcore.dll 
3002>/functionpadmin:6 
3002>/MERGE:.orpc=.text 
3002>/hotpatchcompatible 
3002>/d2:-guardspecload 
3002>/d2:-guardspecanalysismode:v1_0 
3002>/d2:-guardspecmode2 
3002>/ignore:4291 
3002>/DynamicValueFixupSym:mm_shared_user_data_va=0x7FFE0000 
3002>/DynamicValueFixupSym:ki_user_shared_data=0xFFFFF78000000000 
3002>/guard:cf 
3002>/d2:-guardcfgfuncptr- 
3002>/merge:.gfids=.rdata 
3002>/d2:-guardcfgdispatch 
3002>/guard:ehcont 
3002>/CETCOMPAT 
3002>/pdbcompress 
3002>/delayload:api-ms-win-security-base-l1*.dll 
3002>/delayload:api-ms-win-security-sddl-l1*.dll 
3002>/delayload:ext-ms-win-shell32-shellfolders-l1*.dll 
3002>/delayload:ntdsapi.dll 
3002>/delayload:rpcrt4.dll 
3002>/delayload:secur32.dll 
3002>/STACK:0x40000,0x1000 
3002>/dll 
3002>/subsystem:windows,10.00 
3002>/entry:_DllMainCRTStartup 
3002>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\pch.obj 
3002>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.exp 
3002>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\dllmain.obj 
3002>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxservice.obj 
3002>e:\os\public\amd64fre\onecore\external\sdk\lib\dloadhelper.lib 
3002>e:\os\public\amd64fre\onecore\external\sdk\lib\atls.lib 
3002>e:\os\public\amd64fre\onecore\external\sdk\lib\atlthunk.lib 
3002>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\msvcprt.lib 
3002>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_wide_specifiers.lib 
3002>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_msvcrt_compatibility.lib 
3002>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\msvcrt.lib 
3002>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\osmode_function_map.obj 
3002>e:\os\public\amd64fre\onecore\external\sdk\lib\ucrt.lib 
3002>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt_private.lib 
3002>e:\os\public\amd64fre\onecore\internal\sdk\lib\ucrt\legacy_stdio_definitions.lib 
3002>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\client\lib\objfre\amd64\aimxclient_s.lib 
3002>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\server\objfre\amd64\aimxserver.lib 
3002>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\..\llmclientlib\objfre\amd64\llmclientlib.lib 
3002>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\..\McpProtocolLib\objfre\amd64\McpProtocolLib.lib 
3002>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\..\MCPServerSample\lib\objfre\amd64\HelloMcpServer.lib 
3002>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\..\..\AdMcpSvr\objfre\amd64\AdMcpSvr.lib 
3002>e:\os\public\amd64fre\onecore\external\sdk\lib\MinWin\ntdll.lib 
3002>e:\os\public\amd64fre\onecore\external\sdk\lib\MinWin\rpcrt4.lib 
3002>e:\os\public\amd64fre\onecore\external\sdk\lib\secur32.lib 
3002>e:\os\public\amd64fre\onecore\external\sdk\lib\ntdsapi.lib 
3002>e:\os\public\amd64fre\onecore\external\sdk\lib\netapi32.lib 
3002>e:\os\public\amd64fre\onecore\external\sdk\lib\onecore.lib 
3002>e:\os\public\amd64fre\OneCore\Internal\MinCore\Priv_Sdk\Lib\1.20\ext-ms-win-shell32-shellfolders-l1.lib 
3002>e:\os\public\amd64fre\onecore\internal\sdk\lib\MinWin\1.21\api-ms-win-security-sddl-l1.lib 
3002>e:\os\public\amd64fre\onecore\internal\sdk\lib\MinWin\1.21\api-ms-win-security-base-l1.lib 
3002>e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\cpprestsdk\objfre\amd64\vcpkg_installed\target-windows\lib\cpprest_2_10.lib 
3002>e:\os\public\amd64fre\onecore\internal\sdk\lib\guard_support.lib 
3002>e:\os\public\amd64fre\OneCore\Internal\MinWin\Priv_Sdk\Lib\1.21\api-ms-win-core-delayload-l1.lib 
3002>/pdbrpc:no 
3002>Generating code
3002>SPD e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll.spd not found, compiling without profile guided optimizations
3002>Finished generating code
3002> c:\windows\system32\cmd.exe /c del e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.ilk.persistent e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll.persistent e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.pdb.persistent 2>nul
3002> c:\windows\system32\cmd.exe /c e:\os\src\tools\urtrun.cmd 4.Latest e:\os\tools\FixTSVersionStringAppend\bin\FixTsVersionStringAppend\release\FixTSVersionStringAppend.exe /fts:e:\os\tools\FixTS\FixTS.exe /pe=e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3002>[e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll]
3002>No version resources present.
3002>Microsoft (R) COFF/PE Editor Version 14.42.34444.100
3002>Copyright (C) Microsoft Corporation.  All rights reserved.
3002>Updating PDB GUID and/or timestamp in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3002>Updating PDB GUID and/or timestamp in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.pdb
3002>Original RSDSKEY:{53CC8AA1-77CA-8423-7AF2-D30D0F8EF5D2}:1
3002>New RSDSKEY:     {53CC8AA1-77CA-8423-7AF2-D30D0F8EF5D2}:1
3002> e:\os\tools\perl64\bin\perl.exe e:\os\src\tools\FeatureStaging\LogInliningFailures.pl /makedir:onecore\ds\ds\src\aimx\prod\aimxsrv\dll e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3002> e:\os\tools\deferredbinplace\DeferredBinplace.exe  e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll  PASS2  e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\binplace_PASS2.rsp  @e:\os\tools\binplace\binplace.exe  /R e:\os\bin\amd64fre\.  /s e:\os\bin\amd64fre\Symbols.pri\. /j /:DBG /:NOCV  -f -:LOGPDB /:CVTCIL /:SYMBAD e:\os\src\tools\symbad.txt /:TMF   /:DEST retail      e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3002>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3002>BINPLACE : INFORMATION BNP0017: Can't detect version resource in e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll. 0x000000B7 - Cannot create a file when that file already exists.
3002> c:\windows\system32\cmd.exe /c if not exist e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll.mui (  echo Build_Status  LN_MUI_STS: LGNSTS_UNKNOWN aimxsrv.dll  )
3002>Build_Status  LN_MUI_STS: LGNSTS_UNKNOWN aimxsrv.dll  
3002> e:\os\tools\perl64\bin\perl.exe e:\os\src\tools\check_delayload.pl e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3002>(check_delayload.pl) Command: check_delayload e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.dll
3002>(check_delayload.pl) Started at Thu Jul 24 21:42:00 2025
3002>(check_delayload.pl) reading e:\os\public\amd64fre\onecore\internal\minwin\priv_sdk\lib\dload.dload
3002>All delayloadable dlls are already delay loaded.
3002>The following dlls require stubs before they can be delay loaded:
3002> api-ms-win-core-debug-l1-1-0.dll (3 imports):
3002>  DebugBreak
3002>  IsDebuggerPresent
3002>  OutputDebugStringW
3002> api-ms-win-core-delayload-l1-1-0.dll (1 imports):
3002>  DelayLoadFailureHook
3002> api-ms-win-core-delayload-l1-1-1.dll (1 imports):
3002>  ResolveDelayLoadedAPI
3002> api-ms-win-core-errorhandling-l1-1-0.dll (4 imports):
3002>  GetLastError
3002>  SetLastError
3002>  SetUnhandledExceptionFilter
3002>  UnhandledExceptionFilter
3002> api-ms-win-core-file-l1-1-0.dll (8 imports):
3002>  CreateDirectoryW
3002>  CreateFileW
3002>  FindClose
3002>  FindFirstFileW
3002>  FlushFileBuffers
3002>  GetFileAttributesExW
3002>  ReadFile
3002>  WriteFile
3002> api-ms-win-core-file-l2-1-0.dll (2 imports):
3002>  GetFileInformationByHandleEx
3002>  MoveFileExW
3002> api-ms-win-core-handle-l1-1-0.dll (2 imports):
3002>  CloseHandle
3002>  SetHandleInformation
3002> api-ms-win-core-heap-l1-1-0.dll (3 imports):
3002>  GetProcessHeap
3002>  HeapAlloc
3002>  HeapFree
3002> api-ms-win-core-heap-l2-1-0.dll (2 imports):
3002>  GlobalFree
3002>  LocalFree
3002> api-ms-win-core-interlocked-l1-1-0.dll (1 imports):
3002>  InitializeSListHead
3002> api-ms-win-core-io-l1-1-0.dll (1 imports):
3002>  GetOverlappedResult
3002> api-ms-win-core-io-l1-1-1.dll (1 imports):
3002>  CancelIo
3002> api-ms-win-core-libraryloader-l1-2-0.dll (6 imports):
3002>  DisableThreadLibraryCalls
3002>  GetModuleFileNameA
3002>  GetModuleHandleA
3002>  GetModuleHandleExW
3002>  GetModuleHandleW
3002>  GetProcAddress
3002> api-ms-win-core-localization-l1-2-0.dll (3 imports):
3002>  FormatMessageA
3002>  FormatMessageW
3002>  GetLocaleInfoEx
3002> api-ms-win-core-namedpipe-l1-1-0.dll (1 imports):
3002>  CreatePipe
3002> api-ms-win-core-processthreads-l1-1-0.dll (8 imports):
3002>  CreateProcessA
3002>  CreateProcessW
3002>  CreateThread
3002>  GetCurrentProcess
3002>  GetCurrentProcessId
3002>  GetCurrentThreadId
3002>  GetExitCodeProcess
3002>  TerminateProcess
3002> api-ms-win-core-processthreads-l1-1-1.dll (1 imports):
3002>  IsProcessorFeaturePresent
3002> api-ms-win-core-profile-l1-1-0.dll (1 imports):
3002>  QueryPerformanceCounter
3002> api-ms-win-core-registry-l1-1-0.dll (6 imports):
3002>  RegCloseKey
3002>  RegGetValueW
3002>  RegOpenKeyExA
3002>  RegOpenKeyExW
3002>  RegQueryValueExA
3002>  RegQueryValueExW
3002> api-ms-win-core-string-l1-1-0.dll (2 imports):
3002>  MultiByteToWideChar
3002>  WideCharToMultiByte
3002> api-ms-win-core-synch-l1-1-0.dll (16 imports):
3002>  AcquireSRWLockExclusive
3002>  AcquireSRWLockShared
3002>  CreateEventExW
3002>  CreateEventW
3002>  DeleteCriticalSection
3002>  EnterCriticalSection
3002>  InitializeCriticalSection
3002>  InitializeCriticalSectionAndSpinCount
3002>  InitializeCriticalSectionEx
3002>  LeaveCriticalSection
3002>  ReleaseSRWLockExclusive
3002>  ReleaseSRWLockShared
3002>  ResetEvent
3002>  SetEvent
3002>  WaitForSingleObject
3002>  WaitForSingleObjectEx
3002> api-ms-win-core-synch-l1-2-0.dll (1 imports):
3002>  Sleep
3002> api-ms-win-core-sysinfo-l1-1-0.dll (2 imports):
3002>  GetSystemTimeAsFileTime
3002>  GetTickCount
3002> api-ms-win-core-threadpool-l1-2-0.dll (3 imports):
3002>  CloseThreadpoolWork
3002>  CreateThreadpoolWork
3002>  SubmitThreadpoolWork
3002> api-ms-win-core-timezone-l1-1-0.dll (1 imports):
3002>  FileTimeToSystemTime
3002> api-ms-win-crt-private-l1-1-0.dll (66 imports):
3002>  _CxxThrowException
3002>  __C_specific_handler
3002>  __CxxFrameHandler3
3002>  __CxxFrameHandler4
3002>  __current_exception
3002>  __current_exception_context
3002>  __std_terminate
3002>  _o____lc_codepage_func
3002>  _o___std_exception_copy
3002>  _o___std_exception_destroy
3002>  _o___std_type_info_destroy_list
3002>  _o___stdio_common_vsprintf
3002>  _o___stdio_common_vsprintf_s
3002>  _o___stdio_common_vswprintf
3002>  _o___stdio_common_vswprintf_s
3002>  _o__callnewh
3002>  _o__cexit
3002>  _o__configure_narrow_argv
3002>  _o__crt_atexit
3002>  _o__dsign
3002>  _o__errno
3002>  _o__execute_onexit_table
3002>  _o__fseeki64
3002>  _o__get_stream_buffer_pointers
3002>  _o__initialize_narrow_environment
3002>  _o__initialize_onexit_table
3002>  _o__invalid_parameter_noinfo
3002>  _o__invalid_parameter_noinfo_noreturn
3002>  _o__localtime64_s
3002>  _o__lock_file
3002>  _o__purecall
3002>  _o__register_onexit_function
3002>  _o__seh_filter_dll
3002>  _o__unlock_file
3002>  _o__wcsicmp
3002>  _o_abort
3002>  _o_ceilf
3002>  _o_fclose
3002>  _o_fflush
3002>  _o_fgetc
3002>  _o_fgetpos
3002>  _o_fputc
3002>  _o_fread
3002>  _o_free
3002>  _o_fsetpos
3002>  _o_fwrite
3002>  _o_isalpha
3002>  _o_isdigit
3002>  _o_localeconv
3002>  _o_malloc
3002>  _o_realloc
3002>  _o_setvbuf
3002>  _o_strftime
3002>  _o_strtod
3002>  _o_strtoll
3002>  _o_strtoull
3002>  _o_terminate
3002>  _o_ungetc
3002>  _o_wcscpy_s
3002>  _o_wcstof
3002>  _o_wcstol
3002>  memchr
3002>  memcmp
3002>  memcpy
3002>  memmove
3002>  strchr
3002> api-ms-win-crt-runtime-l1-1-0.dll (2 imports):
3002>  _initterm
3002>  _initterm_e
3002> api-ms-win-crt-string-l1-1-0.dll (2 imports):
3002>  memset
3002>  wcsnlen
3002> api-ms-win-crt-time-l1-1-0.dll (1 imports):
3002>  _time64
3002> api-ms-win-eventing-classicprovider-l1-1-0.dll (6 imports):
3002>  GetTraceEnableFlags
3002>  GetTraceEnableLevel
3002>  GetTraceLoggerHandle
3002>  RegisterTraceGuidsW
3002>  TraceMessage
3002>  UnregisterTraceGuids
3002> msvcp_win.dll (124 imports):
3002>  ??0?$basic_ios@DU?$char_traits@D@std@@@std@@IEAA@XZ
3002>  ??0?$basic_ios@GU?$char_traits@G@std@@@std@@IEAA@XZ
3002>  ??0?$basic_istream@DU?$char_traits@D@std@@@std@@QEAA@PEAV?$basic_streambuf@DU?$char_traits@D@std@@@1@_N@Z
3002>  ??0?$basic_istream@GU?$char_traits@G@std@@@std@@QEAA@PEAV?$basic_streambuf@GU?$char_traits@G@std@@@1@_N@Z
3002>  ??0?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAA@PEAV?$basic_streambuf@DU?$char_traits@D@std@@@1@_N@Z
3002>  ??0?$basic_ostream@GU?$char_traits@G@std@@@std@@QEAA@PEAV?$basic_streambuf@GU?$char_traits@G@std@@@1@_N@Z
3002>  ??0?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAA@XZ
3002>  ??0?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEAA@XZ
3002>  ??0_Locinfo@std@@QEAA@PEBD@Z
3002>  ??0_Lockit@std@@QEAA@H@Z
3002>  ??0facet@locale@std@@IEAA@_K@Z
3002>  ??1?$basic_ios@DU?$char_traits@D@std@@@std@@UEAA@XZ
3002>  ??1?$basic_ios@GU?$char_traits@G@std@@@std@@UEAA@XZ
3002>  ??1?$basic_istream@DU?$char_traits@D@std@@@std@@UEAA@XZ
3002>  ??1?$basic_istream@GU?$char_traits@G@std@@@std@@UEAA@XZ
3002>  ??1?$basic_ostream@DU?$char_traits@D@std@@@std@@UEAA@XZ
3002>  ??1?$basic_ostream@GU?$char_traits@G@std@@@std@@UEAA@XZ
3002>  ??1?$basic_streambuf@DU?$char_traits@D@std@@@std@@UEAA@XZ
3002>  ??1?$basic_streambuf@GU?$char_traits@G@std@@@std@@UEAA@XZ
3002>  ??1_Locinfo@std@@QEAA@XZ
3002>  ??1_Lockit@std@@QEAA@XZ
3002>  ??1facet@locale@std@@MEAA@XZ
3002>  ??5?$basic_istream@GU?$char_traits@G@std@@@std@@QEAAAEAV01@AEAH@Z
3002>  ??5?$basic_istream@GU?$char_traits@G@std@@@std@@QEAAAEAV01@AEA_K@Z
3002>  ??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@P6AAEAV01@AEAV01@@Z@Z
3002>  ??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@_K@Z
3002>  ??6?$basic_ostream@GU?$char_traits@G@std@@@std@@QEAAAEAV01@_K@Z
3002>  ??Bid@locale@std@@QEAA_KXZ
3002>  ?_Decref@facet@locale@std@@UEAAPEAV_Facet_base@3@XZ
3002>  ?_Fiopen@std@@YAPEAU_iobuf@@PEBDHH@Z
3002>  ?_Fiopen@std@@YAPEAU_iobuf@@PEBGHH@Z
3002>  ?_Getcat@?$codecvt@DDU_Mbstatet@@@std@@SA_KPEAPEBVfacet@locale@2@PEBV42@@Z
3002>  ?_Getcat@?$ctype@G@std@@SA_KPEAPEBVfacet@locale@2@PEBV42@@Z
3002>  ?_Getcoll@_Locinfo@std@@QEBA?AU_Collvec@@XZ
3002>  ?_Getgloballocale@locale@std@@CAPEAV_Locimp@12@XZ
3002>  ?_Incref@facet@locale@std@@UEAAXXZ
3002>  ?_Init@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAAXXZ
3002>  ?_Init@locale@std@@CAPEAV_Locimp@12@_N@Z
3002>  ?_Lock@?$basic_streambuf@DU?$char_traits@D@std@@@std@@UEAAXXZ
3002>  ?_Lock@?$basic_streambuf@GU?$char_traits@G@std@@@std@@UEAAXXZ
3002>  ?_Osfx@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAXXZ
3002>  ?_Osfx@?$basic_ostream@GU?$char_traits@G@std@@@std@@QEAAXXZ
3002>  ?_Random_device@std@@YAIXZ
3002>  ?_Syserror_map@std@@YAPEBDH@Z
3002>  ?_Throw_Cpp_error@std@@YAXH@Z
3002>  ?_Unlock@?$basic_streambuf@DU?$char_traits@D@std@@@std@@UEAAXXZ
3002>  ?_Unlock@?$basic_streambuf@GU?$char_traits@G@std@@@std@@UEAAXXZ
3002>  ?_Winerror_map@std@@YAHH@Z
3002>  ?_Xbad_alloc@std@@YAXXZ
3002>  ?_Xbad_function_call@std@@YAXXZ
3002>  ?_Xinvalid_argument@std@@YAXPEBD@Z
3002>  ?_Xlength_error@std@@YAXPEBD@Z
3002>  ?_Xout_of_range@std@@YAXPEBD@Z
3002>  ?_Xregex_error@std@@YAXW4error_type@regex_constants@1@@Z
3002>  ?__ExceptionPtrAssign@@YAXPEAXPEBX@Z
3002>  ?__ExceptionPtrCompare@@YA_NPEBX0@Z
3002>  ?__ExceptionPtrCopy@@YAXPEAXPEBX@Z
3002>  ?__ExceptionPtrCopyException@@YAXPEAXPEBX1@Z
3002>  ?__ExceptionPtrCreate@@YAXPEAX@Z
3002>  ?__ExceptionPtrCurrentException@@YAXPEAX@Z
3002>  ?__ExceptionPtrDestroy@@YAXPEAX@Z
3002>  ?__ExceptionPtrRethrow@@YAXPEBX@Z
3002>  ?__ExceptionPtrToBool@@YA_NPEBX@Z
3002>  ?always_noconv@codecvt_base@std@@QEBA_NXZ
3002>  ?bad@ios_base@std@@QEBA_NXZ
3002>  ?classic@locale@std@@SAAEBV12@XZ
3002>  ?clear@?$basic_ios@DU?$char_traits@D@std@@@std@@QEAAXH_N@Z
3002>  ?eback@?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEBAPEAGXZ
3002>  ?egptr@?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEBAPEAGXZ
3002>  ?eof@ios_base@std@@QEBA_NXZ
3002>  ?epptr@?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEBAPEAGXZ
3002>  ?fail@ios_base@std@@QEBA_NXZ
3002>  ?fill@?$basic_ios@GU?$char_traits@G@std@@@std@@QEBAGXZ
3002>  ?flags@ios_base@std@@QEBAHXZ
3002>  ?flush@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV12@XZ
3002>  ?flush@?$basic_ostream@GU?$char_traits@G@std@@@std@@QEAAAEAV12@XZ
3002>  ?getloc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QEBA?AVlocale@2@XZ
3002>  ?good@ios_base@std@@QEBA_NXZ
3002>  ?gptr@?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEBAPEAGXZ
3002>  ?id@?$codecvt@DDU_Mbstatet@@@std@@2V0locale@2@A
3002>  ?id@?$collate@G@std@@2V0locale@2@A
3002>  ?id@?$ctype@G@std@@2V0locale@2@A
3002>  ?imbue@?$basic_ios@GU?$char_traits@G@std@@@std@@QEAA?AVlocale@2@AEBV32@@Z
3002>  ?imbue@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAAXAEBVlocale@2@@Z
3002>  ?imbue@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAAXAEBVlocale@2@@Z
3002>  ?in@?$codecvt@DDU_Mbstatet@@@std@@QEBAHAEAU_Mbstatet@@PEBD1AEAPEBDPEAD3AEAPEAD@Z
3002>  ?is@?$ctype@G@std@@QEBA_NFG@Z
3002>  ?out@?$codecvt@DDU_Mbstatet@@@std@@QEBAHAEAU_Mbstatet@@PEBD1AEAPEBDPEAD3AEAPEAD@Z
3002>  ?pbase@?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEBAPEAGXZ
3002>  ?pptr@?$basic_streambuf@GU?$char_traits@G@std@@@std@@IEBAPEAGXZ
3002>  ?put@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV12@D@Z
3002>  ?rdbuf@?$basic_ios@GU?$char_traits@G@std@@@std@@QEBAPEAV?$basic_streambuf@GU?$char_traits@G@std@@@2@XZ
3002>  ?sbumpc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QEAAHXZ
3002>  ?setbuf@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAAPEAV12@PEAD_J@Z
3002>  ?setbuf@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAAPEAV12@PEAG_J@Z
3002>  ?setstate@?$basic_ios@DU?$char_traits@D@std@@@std@@QEAAXH_N@Z
3002>  ?setstate@?$basic_ios@GU?$char_traits@G@std@@@std@@QEAAXH_N@Z
3002>  ?showmanyc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAA_JXZ
3002>  ?showmanyc@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAA_JXZ
3002>  ?sputc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QEAAHD@Z
3002>  ?sputc@?$basic_streambuf@GU?$char_traits@G@std@@@std@@QEAAGG@Z
3002>  ?sputn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QEAA_JPEBD_J@Z
3002>  ?sputn@?$basic_streambuf@GU?$char_traits@G@std@@@std@@QEAA_JPEBG_J@Z
3002>  ?sync@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAAHXZ
3002>  ?sync@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAAHXZ
3002>  ?tie@?$basic_ios@GU?$char_traits@G@std@@@std@@QEBAPEAV?$basic_ostream@GU?$char_traits@G@std@@@2@XZ
3002>  ?tolower@?$ctype@G@std@@QEBAGG@Z
3002>  ?tolower@?$ctype@G@std@@QEBAPEBGPEAGPEBG@Z
3002>  ?uflow@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAAHXZ
3002>  ?uflow@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAAGXZ
3002>  ?uncaught_exception@std@@YA_NXZ
3002>  ?unshift@?$codecvt@DDU_Mbstatet@@@std@@QEBAHAEAU_Mbstatet@@PEAD1AEAPEAD@Z
3002>  ?widen@?$basic_ios@DU?$char_traits@D@std@@@std@@QEBADD@Z
3002>  ?width@ios_base@std@@QEAA_J_J@Z
3002>  ?width@ios_base@std@@QEBA_JXZ
3002>  ?xsgetn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAA_JPEAD_J@Z
3002>  ?xsgetn@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAA_JPEAG_J@Z
3002>  ?xsputn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAA_JPEBD_J@Z
3002>  ?xsputn@?$basic_streambuf@GU?$char_traits@G@std@@@std@@MEAA_JPEBG_J@Z
3002>  _Mtx_lock
3002>  _Mtx_unlock
3002>  _Wcscoll
3002>  _Wcsxfrm
3002>  _Xtime_get_ticks
3002> NETAPI32.dll (2 imports):
3002>  DsGetDcNameW
3002>(check_delayload.pl) Ended at Thu Jul 24 21:42:00 2025 with ExitCode:0
3002>(check_delayload.pl) Elapsed time 0 seconds
3002> e:\os\osdep\feature.toggles\tools\ft_scraper.exe -pdb:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\aimxsrv.pdb -out:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\ft_scraped_aimxsrv.ft -binplace_path:bvtbin\feature_toggles\scraped\OneCore\onecore\ds\ds\src\aimx\prod\aimxsrv\dll
3002> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\e92cce39887833cc17b7bfc3fdcdbc35\post_link_concurrent.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\e92cce39887833cc17b7bfc3fdcdbc35\post_link_concurrent.rsp
3002> c:\windows\system32\cmd.exe /c if exist e:\os\obj\amd64fre\temp\e92cce39887833cc17b7bfc3fdcdbc35\post_link_concurrent2.rsp e:\os\tools\Windows.Desktop.Tools\tools\contool.exe @e:\os\obj\amd64fre\temp\e92cce39887833cc17b7bfc3fdcdbc35\post_link_concurrent2.rsp
3002>Writing out macros...e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\Macros-PASS2.txt
3002>binplace e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\Macros-PASS2.txt
3002> e:\os\tools\powershell\pwsh.exe -NoProfile -Command e:\os\src\tools\NMakeJS\CheckCFlags\CheckCFlags.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\dll" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\CheckCFlags.json -OutputDir "e:\os\bin\amd64fre\build_logs\DMFDebt\build_logs\CheckCFlags\OneCore\onecoreds"
3002>CheckCFlags.ps1 : Processing onecore\ds\ds\src\aimx\prod\aimxsrv\dll in pass PASS2
3002>CheckCFlags.ps1 : Running e:\os\src\tools\urtrun64.cmd 4.Latest e:\os\tools\checkcflags\cs\checkcflags2.exe Scan-List /ListFile:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\CheckCFlags_scanlist.txt /PolicyConfigFilePath:E:\os\src\tools\NMakeJS\CheckCFlags\CCF_PolicyConfig.json /LogDirectory:e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64
3002> e:\os\tools\powershell\pwsh.exe -NoProfile e:\os\src\tools\NMakeJS\WarningsCop\WarningsCop.ps1 -MakeSubDir "onecore\ds\ds\src\aimx\prod\aimxsrv\dll" -Pass PASS2 -BaselineFile e:\os\src\.config\OneCore\WarningsCop.json -OutputDir "e:\os\bin\amd64fre\evidence\WarningsCop\OneCore\onecoreds"
3002>WarningsCop.ps1 : WarningsCop: Processing onecore\ds\ds\src\aimx\prod\aimxsrv\dll in pass PASS2
BUILD: Pass complete => PASS2

PERF: Waiting for performance monitor thread to terminate.
PERF: Terminating perf data collector thread.
1>info: Microsoft.Internal.Trace.Database.Core.TraceManager[0]
1>      Finalizing the trace reading...
1>info: Microsoft.Internal.Trace.Database.Core.AccessManager[0]
1>      Total Accesses = 51543
1>info: Microsoft.Internal.Trace.Database.Core.FileManager[0]
1>      Total Files = 19239
1>info: Microsoft.Internal.Trace.Database.Core.ProcessManager[0]
1>      Total Processes = 421
1>info: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      Total Process Time = 167.1875
1>info: Microsoft.Internal.Trace.Tracer.EtwTraceAdapter[0]
1>      (Logging time = 3.7030175000000014, Detour time = 7.651005099999996
1>info: Microsoft.Internal.Trace.Database.Core.TraceManager[0]
1>      Finalizing the trace reading...
1>warn: Microsoft.Internal.Trace.Database.Core.FileManager[0]
1>      Including all files for analysis because the Exclusion Evaluation rules are not loaded.
1>warn: Microsoft.Internal.Trace.Database.Core.DirectoryManager[0]
1>      Including all directories for analysis because the Exclusion Evaluation rules are not loaded.
1>info: Microsoft.Internal.Trace.Database.Core.DirectoryManager[0]
1>      Analyzing trace to infer dependencies...
1>info: Microsoft.Internal.Trace.Database.IO.BinaryTraceWriter[0]
1>      File e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc already exists. Implicitly overwriting as the native code would have done.
1>info: Microsoft.Internal.Trace.Database.IO.BinaryTraceWriter[0]
1>      Writing e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      File table total write time = 00:00:00.1918955
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      File total bytes written = 823931
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Process table total write time = 00:00:00.0493852
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Process total bytes written = 90338
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Access table total write time = 00:00:00.0178443
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Access total bytes written = 231957
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Task table total write time = 00:00:00.0003522
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      Task total bytes written = 21
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      EnvironmentAccess table total write time = 00:00:00.0005157
1>info: Microsoft.Internal.Trace.Database.IO.Table6[0]
1>      EnvironmentAccess total bytes written = 21
1>info: Microsoft.Internal.Trace.Database.IO.BinaryTraceWriter[0]
1>      Trace file written to e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc
1>TRACER : Microsoft (R) Build Tracer
1>TRACER : Copyright (C) Microsoft Corporation. All rights reserved.
1>TRACER : Build Trace File Logger 74106951-1662-4aa0-a34e-55f9c8ec57e3 is already running...
1>TRACER : Starting Realtime Build Trace File Logger ded2b352-5dc7-49c8-8be8-4fe5a497c85f...
1>TRACER : Enabling trace provider...
1>TRACER (BuildSocket): RegisterClient rupo-dell:0 (Tracer)
1>TRACER : Tracer Satellite: Satellite command disabled. (No value in environment variable TRACER_SATELLITE_COMMAND)
1>TRACER : Launching: "e:\os\tools\corebuild\amd64\buildc.exeTRACER :  /hostname localhost /hostport 29026TRACER : "
1>TRACER : Tracer Satellite: Stop Process: Nothing to do.
1>TRACER : Disabling trace provider...
1>TRACER (event): ETW Trace Session
1>TRACER (event): =================
1>TRACER (event): Buffers Allocated: 32
1>TRACER (event): Buffers Written: 673
1>TRACER (event): Buffer Size: 512KB
1>TRACER (event): Buffers Lost: 0
1>TRACER (event): Real Time Buffers Lost: 0
1>TRACER (event): Events Lost: 0
1>TRACER : Stopping Build Trace File Logger ded2b352-5dc7-49c8-8be8-4fe5a497c85f...
1>Running analyzer on build trace...
  *************
1>'toolredirector.exe analyzernative -merge:e:\os\obj\amd64fre\objfre\amd64\build.ldg -reportconfig:e:\os\src\build\config\core\dbb_report_config.xml -in:bin e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc'
1>?? [CallRedirectedCommand] Environment.CommandLine: 'E:\os\tools\ToolRedirectors\ToolRedirector.exe analyzernative -merge:e:\os\obj\amd64fre\objfre\amd64\build.ldg -reportconfig:e:\os\src\build\config\core\dbb_report_config.xml -in:bin e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc
1>?? [CallRedirectedCommand] calledArguments[0]: 'E:\os\tools\ToolRedirectors\ToolRedirector.exe'
1>?? [CallRedirectedCommand] calledArguments[1]: 'analyzernative'
1>?? [CallRedirectedCommand] calledArguments[2]: '-merge:e:\os\obj\amd64fre\objfre\amd64\build.ldg'
1>?? [CallRedirectedCommand] calledArguments[3]: '-reportconfig:e:\os\src\build\config\core\dbb_report_config.xml'
1>?? [CallRedirectedCommand] calledArguments[4]: '-in:bin'
1>?? [CallRedirectedCommand] calledArguments[5]: 'e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc'
1>?? [CallRedirectedCommand] redirectedCommand: 'e:\os\tools\Analyzer\amd64\analyzer.exe'
1>?? [CallRedirectedCommand] redirectedCommandArguments: ' -merge:e:\os\obj\amd64fre\objfre\amd64\build.ldg -reportconfig:e:\os\src\build\config\core\dbb_report_config.xml -in:bin e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc'
1>?? [CallRedirectedCommand] startInfo.FileName: 'e:\os\tools\Analyzer\amd64\analyzer.exe'
1>?? [CallRedirectedCommand] startInfo.Arguments: ' -merge:e:\os\obj\amd64fre\objfre\amd64\build.ldg -reportconfig:e:\os\src\build\config\core\dbb_report_config.xml -in:bin e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc'
1>(TRACEREPORT) : ERROR (filestream): CreateFile failed: The system cannot find the file specified.
(TRACEREPORT)error (filestream): CreateFile failed: The system cannot find the file specified.
1>(TRACEREPORT) : ERROR (xmlparser): Failed to create file stream (error: 0x8007007a)
(TRACEREPORT)error (xmlparser): Failed to create file stream (error: 0x8007007a)
1>(TRACEREPORT) : ERROR (configparser): TRE1032: The config file 'e:\os\src\build\config\core\dbb_report_config.xml' was not found or could not be parsed
(TRACEREPORT)error (configparser): TRE1032: The config file 'e:\os\src\build\config\core\dbb_report_config.xml' was not found or could not be parsed
1>ANALYZER : Microsoft (R) Build Trace Analyzer [Build 8.0.250519001+6b8e35f5c0ee29c4c18a354e33cc8b695d5695ad]
1>ANALYZER : Copyright (C) Microsoft Corporation. All rights reserved.
1>[21:42:03.465] Parsing error policy from 'e:\os\src\build\config\Core\AnalyzerEnforcedErrors.json'...
1>ANALYZER (_tmain): Analyzer has completed and is exiting with return code '4' indicating infrastructure errors.
1>ANALYZER : PageFaultCount:     10714
1>ANALYZER : PeakWorkingSetSize: 40513536
1>ANALYZER : PeakPagefileUsage:  11190272
1>ANALYZER : ProcessCycleTime:   567988581
1>ANALYZER : KernelTime:         0.094
1>ANALYZER : UserTime:           0.125
toolredirector.exe analyzernative -merge:e:\os\obj\amd64fre\objfre\amd64\build.ldg -reportconfig:e:\os\src\build\config\core\dbb_report_config.xml -in:bin e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\buildfre.trc failed - rc = 0x00000004

Build layers enabled: [ShellCommon,GameCore,DesktopEditions,ClientCore,OnecoreUAP,OSClient]
Number of excluded directories, not in layer set: 0


    12 directories scanned
    61 files compiled - 4 Errors
    8 libraries built
    3 executables built
    19 files binplaced
