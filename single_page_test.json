﻿{
    "outputs":  [

                ],
    "synopsis":  "Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell.",
    "related_links":  [

                      ],
    "scraped_at":  "2025-07-25 21:44:51",
    "url":  "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser",
    "notes":  [

              ],
    "description":  "The Get-ADUser cmdlet gets a specified user object or performs a search to get multiple user objects.\n\nThe Identity parameter specifies the Active Directory user to get. You can identify a user by its distinguished name (DN), GUID, security identifier (SID), or Security Account Manager (SAM) account name. You can also set the parameter to a user object variable such as $ or pass a user object through the pipeline to the Identity parameter.\n\nTo search for and retrieve more than one user, use the Filter or LDAPFilter parameters. The Filter parameter uses the PowerShell Expression Language to write query strings for Active Directory. PowerShell Expression Language syntax provides rich type-conversion support for value types received by the Filter parameter. For more information about the Filter parameter syntax, type Get-Help about_ActiveDirectory_Filter. If you have existing Lightweight Directory Access Protocol (LDAP) query strings, you can use the LDAPFilter parameter.\n\nThis cmdlet retrieves a default set of user object properties. To retrieve additional properties use the Properties parameter. For more information about how to determine the properties for user objects, see the Properties parameter description.",
    "raw_sections":  {
                         "Parameters":  "\n\t\t\u003ch3 id=\"-authtype\" data-chunk-ids=\"authtype\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Auth\u003cwbr\u003eType\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies the authentication method to use.\nThe acceptable values for this parameter are:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eNegotiate or 0\u003c/li\u003e\n\u003cli\u003eBasic or 1\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eThe default authentication method is Negotiate.\u003c/p\u003e\n\u003cp\u003eA Secure Sockets Layer (SSL) connection is required for the Basic authentication method.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"authtype-properties\" data-chunk-ids=\"authtype\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"authtype\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eADAuthType\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eAccepted values:\u003c/td\u003e\u003ctd\u003eNegotiate, Basic\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"authtype-sets\" data-chunk-ids=\"authtype\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"authtype\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-credential\" data-chunk-ids=\"credential\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Credential\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies the user account credentials to use to perform this task.\nThe default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive.\nIf the cmdlet is run from such a provider drive, the account associated with the drive is the default.\u003c/p\u003e\n\u003cp\u003eTo specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a \u003cstrong\u003ePSCredential\u003c/strong\u003e object.\nIf you specify a user name for this parameter, the cmdlet prompts for a password.\u003c/p\u003e\n\u003cp\u003eYou can also create a \u003cstrong\u003ePSCredential\u003c/strong\u003e object by using a script or by using the \u003cstrong\u003eGet-Credential\u003c/strong\u003e cmdlet.\nYou can then set the \u003cem\u003eCredential\u003c/em\u003e parameter to the \u003cstrong\u003ePSCredential\u003c/strong\u003e object.\u003c/p\u003e\n\u003cp\u003eIf the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"credential-properties\" data-chunk-ids=\"credential\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"credential\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003ePSCredential\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"credential-sets\" data-chunk-ids=\"credential\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"credential\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-filter\" data-chunk-ids=\"filter\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Filter\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies a query string that retrieves Active Directory objects.\nThis string uses the PowerShell Expression Language syntax.\nThe PowerShell Expression Language syntax provides rich type-conversion support for value types received by the \u003cem\u003eFilter\u003c/em\u003e parameter.\nThe syntax uses an in-order representation, which means that the operator is placed between the operand and the value.\nFor more information about the \u003cem\u003eFilter\u003c/em\u003e parameter, type \u003ccode\u003eGet-Help about_ActiveDirectory_Filter\u003c/code\u003e.\u003c/p\u003e\n\u003cp\u003eSyntax:\u003c/p\u003e\n\u003cp\u003eThe following syntax uses Backus-Naur form to show how to use the PowerShell Expression Language for this parameter.\u003c/p\u003e\n\u003cp\u003e\u0026lt;filter\u0026gt;  ::= \"{\" \u0026lt;FilterComponentList\u0026gt; \"}\"\u003c/p\u003e\n\u003cp\u003e\u0026lt;FilterComponentList\u0026gt; ::= \u0026lt;FilterComponent\u0026gt; | \u0026lt;FilterComponent\u0026gt; \u0026lt;JoinOperator\u0026gt; \u0026lt;FilterComponent\u0026gt; | \u0026lt;NotOperator\u0026gt;  \u0026lt;FilterComponent\u0026gt;\u003c/p\u003e\n\u003cp\u003e\u0026lt;FilterComponent\u0026gt; ::= \u0026lt;attr\u0026gt; \u0026lt;FilterOperator\u0026gt; \u0026lt;value\u0026gt; | \"(\" \u0026lt;FilterComponent\u0026gt; \")\"\u003c/p\u003e\n\u003cp\u003e\u0026lt;FilterOperator\u0026gt; ::= \"-eq\" | \"-le\" | \"-ge\" | \"-ne\" | \"-lt\" | \"-gt\"| \"-approx\" | \"-bor\" | \"-band\" | \"-recursivematch\" | \"-like\" | \"-notlike\"\u003c/p\u003e\n\u003cp\u003e\u0026lt;JoinOperator\u0026gt; ::= \"-and\" | \"-or\"\u003c/p\u003e\n\u003cp\u003e\u0026lt;NotOperator\u0026gt; ::= \"-not\"\u003c/p\u003e\n\u003cp\u003e\u0026lt;attr\u0026gt; ::= \u0026lt;PropertyName\u0026gt; | \u0026lt;LDAPDisplayName of the attribute\u0026gt;\u003c/p\u003e\n\u003cp\u003e\u0026lt;value\u0026gt;::= \u0026lt;compare this value with an \u0026lt;attr\u0026gt; by using the specified \u0026lt;FilterOperator\u0026gt;\u0026gt;\u003c/p\u003e\n\u003cp\u003eFor a list of supported types for \u0026lt;value\u0026gt;, type \u003ccode\u003eGet-Help about_ActiveDirectory_ObjectModel\u003c/code\u003e.\u003c/p\u003e\n\u003cp\u003eNote: For String parameter type, PowerShell will cast the filter query to a string while processing the command. When using a string variable as a value in the filter component, make sure that it complies with the \u003ca href=\"/en-us/powershell/module/microsoft.powershell.core/about/about_quoting_rules\" data-linktype=\"absolute-path\"\u003ePowerShell Quoting Rules\u003c/a\u003e. For example, if the filter expression is double-quoted, the variable should be enclosed using single quotation marks:\n\u003cstrong\u003eGet-ADUser -Filter \"Name -like \u0027$UserName\u0027\"\u003c/strong\u003e. On the contrary, if curly braces are used to enclose the filter, the variable should not be quoted at all: \u003cstrong\u003eGet-ADUser -Filter {Name -like $UserName}\u003c/strong\u003e.\u003c/p\u003e\n\u003cp\u003eNote: PowerShell wildcards other than *, such as ?, are not supported by the \u003cem\u003eFilter\u003c/em\u003e syntax.\u003c/p\u003e\n\u003cp\u003eNote: To query using LDAP query strings, use the \u003cem\u003eLDAPFilter\u003c/em\u003e parameter.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"filter-properties\" data-chunk-ids=\"filter\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"filter\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"filter-sets\" data-chunk-ids=\"filter\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"filter\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tFilter \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eTrue\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-identity\" data-chunk-ids=\"identity\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Identity\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies an Active Directory user object by providing one of the following property values.\nThe identifier in parentheses is the LDAP display name for the attribute.\nThe acceptable values for this parameter are:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eA distinguished name\u003c/li\u003e\n\u003cli\u003eA GUID (objectGUID)\u003c/li\u003e\n\u003cli\u003eA security identifier (objectSid)\u003c/li\u003e\n\u003cli\u003eA SAM account name (sAMAccountName)\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eThe cmdlet searches the default naming context or partition to find the object.\nIf two or more objects are found, the cmdlet returns a non-terminating error.\u003c/p\u003e\n\u003cp\u003eThis parameter can also get this object through the pipeline or you can set this parameter to an object instance.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"identity-properties\" data-chunk-ids=\"identity\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"identity\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eADUser\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"identity-sets\" data-chunk-ids=\"identity\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"identity\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tIdentity \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003e0\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eTrue\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eTrue\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-ldapfilter\" data-chunk-ids=\"ldapfilter\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-LDAPFilter\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies an LDAP query string that is used to filter Active Directory objects.\nYou can use this parameter to run your existing LDAP queries.\nThe \u003cem\u003eFilter\u003c/em\u003e parameter syntax supports the same functionality as the LDAP syntax.\nFor more information, see the \u003cem\u003eFilter\u003c/em\u003e parameter description or type \u003ccode\u003eGet-Help about_ActiveDirectory_Filter\u003c/code\u003e.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"ldapfilter-properties\" data-chunk-ids=\"ldapfilter\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"ldapfilter\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"ldapfilter-sets\" data-chunk-ids=\"ldapfilter\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"ldapfilter\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tLdap\u003cwbr\u003eFilter \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eTrue\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-partition\" data-chunk-ids=\"partition\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Partition\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies the distinguished name of an Active Directory partition.\nThe distinguished name must be one of the naming contexts on the current directory server.\nThe cmdlet searches this partition to find the object defined by the \u003cem\u003eIdentity\u003c/em\u003e parameter.\u003c/p\u003e\n\u003cp\u003eIn many cases, a default value is used for the \u003cem\u003ePartition\u003c/em\u003e parameter if no value is specified.\nThe rules for determining the default value are given below.\nNote that rules listed first are evaluated first, and when a default value can be determined, no further rules are evaluated.\u003c/p\u003e\n\u003cp\u003eIn AD DS environments, a default value for \u003cem\u003ePartition\u003c/em\u003e is set in the following cases:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eIf the \u003cem\u003eIdentity\u003c/em\u003e parameter is set to a distinguished name, the default value of \u003cem\u003ePartition\u003c/em\u003e is automatically generated from this distinguished name.\u003c/li\u003e\n\u003cli\u003eIf running cmdlets from an Active Directory provider drive, the default value of \u003cem\u003ePartition\u003c/em\u003e is automatically generated from the current path in the drive.\u003c/li\u003e\n\u003cli\u003eIf none of the previous cases apply, the default value of \u003cem\u003ePartition\u003c/em\u003e is set to the default partition or naming context of the target domain.\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eIn AD LDS environments, a default value for \u003cem\u003ePartition\u003c/em\u003e is set in the following cases:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eIf the \u003cem\u003eIdentity\u003c/em\u003e parameter is set to a distinguished name, the default value of \u003cem\u003ePartition\u003c/em\u003e is automatically generated from this distinguished name.\u003c/li\u003e\n\u003cli\u003eIf running cmdlets from an Active Directory provider drive, the default value of \u003cem\u003ePartition\u003c/em\u003e is automatically generated from the current path in the drive.\u003c/li\u003e\n\u003cli\u003eIf the target AD LDS instance has a default naming context, the default value of \u003cem\u003ePartition\u003c/em\u003e is set to the default naming context.\nTo specify a default naming context for an AD LDS environment, set the \u003cstrong\u003emsDS-defaultNamingContext\u003c/strong\u003e property of the Active Directory directory service agent object (\u003cstrong\u003enTDSDSA\u003c/strong\u003e) for the AD LDS instance.\u003c/li\u003e\n\u003cli\u003eIf none of the previous cases apply, the \u003cem\u003ePartition\u003c/em\u003e parameter does not take any default value.\u003c/li\u003e\n\u003c/ul\u003e\n\n\n\t\t\u003ch4 id=\"partition-properties\" data-chunk-ids=\"partition\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"partition\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"partition-sets\" data-chunk-ids=\"partition\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"partition\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tIdentity \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-properties\" data-chunk-ids=\"properties\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Properties\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies the properties of the output object to retrieve from the server.\nUse this parameter to retrieve properties that are not included in the default set.\u003c/p\u003e\n\u003cp\u003eSpecify properties for this parameter as a comma-separated list of names.\nTo display all of the attributes that are set on the object, specify * (asterisk).\u003c/p\u003e\n\u003cp\u003eTo specify an individual extended property, use the name of the property.\nFor properties that are not default or extended properties, you must specify the LDAP display name of the attribute.\u003c/p\u003e\n\u003cp\u003eTo retrieve properties and display them for an object, you can use the Get-* cmdlet associated with the object and pass the output to the \u003cstrong\u003eGet-Member\u003c/strong\u003e cmdlet.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"properties-properties\" data-chunk-ids=\"properties\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"properties\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cp\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\u003cspan\u003e[\u003c/span\u003e\u003cspan\u003e]\u003c/span\u003e\u003c/p\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eAliases:\u003c/td\u003e\u003ctd\u003eProperty\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"properties-sets\" data-chunk-ids=\"properties\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"properties\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-resultpagesize\" data-chunk-ids=\"resultpagesize\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Result\u003cwbr\u003ePage\u003cwbr\u003eSize\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies the number of objects to include in one page for an Active Directory Domain Services query.\u003c/p\u003e\n\u003cp\u003eThe default is 256 objects per page.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"resultpagesize-properties\" data-chunk-ids=\"resultpagesize\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"resultpagesize\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eInt32\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"resultpagesize-sets\" data-chunk-ids=\"resultpagesize\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"resultpagesize\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tFilter \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"resultpagesize\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tLdap\u003cwbr\u003eFilter \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-resultsetsize\" data-chunk-ids=\"resultsetsize\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Result\u003cwbr\u003eSet\u003cwbr\u003eSize\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies the maximum number of objects to return for an Active Directory Domain Services query.\nIf you want to receive all of the objects, set this parameter to $Null (null value).\nYou can use Ctrl+C to stop the query and return of objects.\u003c/p\u003e\n\u003cp\u003eThe default is $Null.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"resultsetsize-properties\" data-chunk-ids=\"resultsetsize\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"resultsetsize\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eInt32\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"resultsetsize-sets\" data-chunk-ids=\"resultsetsize\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"resultsetsize\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tFilter \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"resultsetsize\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tLdap\u003cwbr\u003eFilter \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-searchbase\" data-chunk-ids=\"searchbase\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Search\u003cwbr\u003eBase\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies an Active Directory path to search under.\u003c/p\u003e\n\u003cp\u003eWhen you run a cmdlet from an Active Directory provider drive, the default value of this parameter is the current path of the drive.\u003c/p\u003e\n\u003cp\u003eWhen you run a cmdlet outside of an Active Directory provider drive against an AD DS target, the default value of this parameter is the default naming context of the target domain.\u003c/p\u003e\n\u003cp\u003eWhen you run a cmdlet outside of an Active Directory provider drive against an AD LDS target, the default value is the default naming context of the target LDS instance if one has been specified by setting the \u003cstrong\u003emsDS-defaultNamingContext\u003c/strong\u003e property of the Active Directory directory service agent (DSA) object (\u003cstrong\u003enTDSDSA\u003c/strong\u003e) for the AD LDS instance.\nIf no default naming context has been specified for the target AD LDS instance, then this parameter has no default value.\u003c/p\u003e\n\u003cp\u003eWhen the value of the \u003cem\u003eSearchBase\u003c/em\u003e parameter is set to an empty string and you are connected to a GC port, all partitions are searched.\nIf the value of the \u003cem\u003eSearchBase\u003c/em\u003e parameter is set to an empty string and you are not connected to a GC port, an error is thrown.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"searchbase-properties\" data-chunk-ids=\"searchbase\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"searchbase\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"searchbase-sets\" data-chunk-ids=\"searchbase\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"searchbase\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tFilter \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"searchbase\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tLdap\u003cwbr\u003eFilter \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-searchscope\" data-chunk-ids=\"searchscope\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Search\u003cwbr\u003eScope\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies the scope of an Active Directory search.\nThe acceptable values for this parameter are:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eBase or 0\u003c/li\u003e\n\u003cli\u003eOneLevel or 1\u003c/li\u003e\n\u003cli\u003eSubtree or 2\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eA SearchScope with a Base value searches only for the given user. If an OU is specified in the SearchBase parameter, no user will be returned by, for example, a specified Filter statement.\nA OneLevel query searches the immediate children of that path or object. This option only works when an OU is given as the SearchBase. If a user is given, no results are returned.\nA Subtree query searches the current path or object and all children of that path or object.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"searchscope-properties\" data-chunk-ids=\"searchscope\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"searchscope\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eADSearchScope\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eAccepted values:\u003c/td\u003e\u003ctd\u003eBase, OneLevel, Subtree\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"searchscope-sets\" data-chunk-ids=\"searchscope\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"searchscope\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tFilter \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"searchscope\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tLdap\u003cwbr\u003eFilter \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"-server\" data-chunk-ids=\"server\" class=\"font-family-monospace margin-top-lg margin-bottom-md\"\u003e-Server\u003c/h3\u003e\n\t\t\u003cp\u003eSpecifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server.\nThe service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory Snapshot instance.\u003c/p\u003e\n\u003cp\u003eDomain name values:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eFully qualified domain name (FQDN)\u003c/li\u003e\n\u003cli\u003eNetBIOS name\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eDirectory server values:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eFully qualified directory server name\u003c/li\u003e\n\u003cli\u003eNetBIOS name\u003c/li\u003e\n\u003cli\u003eFully qualified directory server name and port\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eThe default value for the \u003cem\u003eServer\u003c/em\u003e parameter is determined by one of the following methods in the order that they are listed:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eBy using \u003cem\u003eServer\u003c/em\u003e value from objects passed through the pipeline.\u003c/li\u003e\n\u003cli\u003eBy using the server information associated with the Active Directory PowerShell provider drive, when running under that drive.\u003c/li\u003e\n\u003cli\u003eBy using the domain of the computer running PowerShell.\u003c/li\u003e\n\u003c/ul\u003e\n\n\n\t\t\u003ch4 id=\"server-properties\" data-chunk-ids=\"server\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"server\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"server-sets\" data-chunk-ids=\"server\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"server\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t\u003ch3 id=\"common-parameters\" data-no-chunk=\"\"\u003eCommonParameters\u003c/h3\u003e\n\t\t\u003cdiv data-no-chunk=\"\"\u003e\n\t\t\t\u003cp\u003eThis cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable,\n-InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable,\n-ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see\n\u003ca href=\"https://go.microsoft.com/fwlink/?LinkID=113216\" data-linktype=\"external\"\u003eabout_CommonParameters\u003c/a\u003e.\u003c/p\u003e\n\n\t\t\u003c/div\u003e\n\n\t",
                         "Description":  "\n\t\u003cdiv data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server\"\u003e\n\t\t\u003cp\u003eThe \u003cstrong\u003eGet-ADUser\u003c/strong\u003e cmdlet gets a specified user object or performs a search to get multiple user objects.\u003c/p\u003e\n\u003cp\u003eThe \u003cem\u003eIdentity\u003c/em\u003e parameter specifies the Active Directory user to get.\nYou can identify a user by its distinguished name (DN), GUID, security identifier (SID), or Security Account Manager (SAM) account name.\nYou can also set the parameter to a user object variable such as \u003ccode\u003e$\u0026lt;localUserObject\u0026gt;\u003c/code\u003e or pass a user object through the pipeline to the \u003cem\u003eIdentity\u003c/em\u003e parameter.\u003c/p\u003e\n\u003cp\u003eTo search for and retrieve more than one user, use the \u003cem\u003eFilter\u003c/em\u003e or \u003cem\u003eLDAPFilter\u003c/em\u003e parameters.\nThe \u003cem\u003eFilter\u003c/em\u003e parameter uses the PowerShell Expression Language to write query strings for Active Directory.\nPowerShell Expression Language syntax provides rich type-conversion support for value types received by the \u003cem\u003eFilter\u003c/em\u003e parameter.\nFor more information about the \u003cem\u003eFilter\u003c/em\u003e parameter syntax, type \u003ccode\u003eGet-Help about_ActiveDirectory_Filter\u003c/code\u003e.\nIf you have existing Lightweight Directory Access Protocol (LDAP) query strings, you can use the \u003cem\u003eLDAPFilter\u003c/em\u003e parameter.\u003c/p\u003e\n\u003cp\u003eThis cmdlet retrieves a default set of user object properties.\nTo retrieve additional properties use the \u003cem\u003eProperties\u003c/em\u003e parameter.\nFor more information about how to determine the properties for user objects, see the \u003cem\u003eProperties\u003c/em\u003e parameter description.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\n\t",
                         "Inputs":  "\n\t\t\t\u003ch3 id=\"input-1\" data-chunk-ids=\"inputs\" class=\"break-text font-size-xl\"\u003e\u003cspan class=\"no-loc xref\"\u003eNone or Microsoft.ActiveDirectory.Management.ADUser\u003c/span\u003e\n\u003c/h3\u003e\n\t\t\t\u003cdiv data-chunk-ids=\"inputs\"\u003e\n\t\t\t\t\u003cp\u003eA user object is received by the \u003cem\u003eIdentity\u003c/em\u003e parameter.\u003c/p\u003e\n\n\t\t\t\u003c/div\u003e\n\n\t",
                         "Syntax":  "\n\t\u003ch3 id=\"filter\" data-chunk-ids=\"filter\"\u003e\n\t\tFilter (Default)\n\t\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"filter\"\u003e\n\t\t\u003cpre\u003e\u003ccode class=\"lang-Syntax\"\u003eGet-ADUser\n    -Filter \u0026lt;String\u0026gt;\n    [-AuthType \u0026lt;ADAuthType\u0026gt;]\n    [-Credential \u0026lt;PSCredential\u0026gt;]\n    [-Properties \u0026lt;String[]\u0026gt;]\n    [-ResultPageSize \u0026lt;Int32\u0026gt;]\n    [-ResultSetSize \u0026lt;Int32\u0026gt;]\n    [-SearchBase \u0026lt;String\u0026gt;]\n    [-SearchScope \u0026lt;ADSearchScope\u0026gt;]\n    [-Server \u0026lt;String\u0026gt;]\n    [\u0026lt;CommonParameters\u0026gt;]\n\u003c/code\u003e\u003c/pre\u003e\n\n\t\u003c/div\u003e\n\t\u003ch3 id=\"identity\" data-chunk-ids=\"identity\"\u003e\n\t\tIdentity\n\t\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"identity\"\u003e\n\t\t\u003cpre\u003e\u003ccode class=\"lang-Syntax\"\u003eGet-ADUser\n    [-Identity] \u0026lt;ADUser\u0026gt;\n    [-AuthType \u0026lt;ADAuthType\u0026gt;]\n    [-Credential \u0026lt;PSCredential\u0026gt;]\n    [-Partition \u0026lt;String\u0026gt;]\n    [-Properties \u0026lt;String[]\u0026gt;]\n    [-Server \u0026lt;String\u0026gt;]\n    [\u0026lt;CommonParameters\u0026gt;]\n\u003c/code\u003e\u003c/pre\u003e\n\n\t\u003c/div\u003e\n\t\u003ch3 id=\"ldapfilter\" data-chunk-ids=\"ldapfilter\"\u003e\n\t\tLdap\u003cwbr\u003eFilter\n\t\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"ldapfilter\"\u003e\n\t\t\u003cpre\u003e\u003ccode class=\"lang-Syntax\"\u003eGet-ADUser\n    -LDAPFilter \u0026lt;String\u0026gt;\n    [-AuthType \u0026lt;ADAuthType\u0026gt;]\n    [-Credential \u0026lt;PSCredential\u0026gt;]\n    [-Properties \u0026lt;String[]\u0026gt;]\n    [-ResultPageSize \u0026lt;Int32\u0026gt;]\n    [-ResultSetSize \u0026lt;Int32\u0026gt;]\n    [-SearchBase \u0026lt;String\u0026gt;]\n    [-SearchScope \u0026lt;ADSearchScope\u0026gt;]\n    [-Server \u0026lt;String\u0026gt;]\n    [\u0026lt;CommonParameters\u0026gt;]\n\u003c/code\u003e\u003c/pre\u003e\n\n\t\u003c/div\u003e\n\n\n\t",
                         "Outputs":  "\n\t\t\t\u003ch3 id=\"output-1\" data-chunk-ids=\"outputs\" class=\"break-text font-size-xl\"\u003e\u003cspan class=\"no-loc xref\"\u003eMicrosoft.ActiveDirectory.Management.ADUser\u003c/span\u003e\n\u003c/h3\u003e\n\t\t\t\u003cdiv data-chunk-ids=\"outputs\"\u003e\n\t\t\t\t\u003cp\u003eReturns one or more user objects.\u003c/p\u003e\n\u003cp\u003eThis cmdlet returns a default set of \u003cstrong\u003eADUser\u003c/strong\u003e property values.\nTo retrieve additional \u003cstrong\u003eADUser\u003c/strong\u003e properties, use the \u003cem\u003eProperties\u003c/em\u003e parameter.\u003c/p\u003e\n\u003cp\u003eTo get a list of the default set of properties of an \u003cstrong\u003eADUser\u003c/strong\u003e object, use the following command:\u003c/p\u003e\n\u003cp\u003e\u003ccode\u003eGet-ADUser\u003c/code\u003e\u0026lt;user\u0026gt;\u003ccode\u003e| Get-Member\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eTo get a list of the most commonly used properties of an ADUser object, use the following command:\u003c/p\u003e\n\u003cp\u003e\u003ccode\u003eGet-ADUser\u003c/code\u003e\u0026lt;user\u0026gt;\u003ccode\u003e-Properties Extended | Get-Member\u003c/code\u003e\u003c/p\u003e\n\u003cp\u003eTo get a list of all the properties of an \u003cstrong\u003eADUser\u003c/strong\u003e object, use the following command:\u003c/p\u003e\n\u003cp\u003e\u003ccode\u003eGet-ADUser\u003c/code\u003e\u0026lt;user\u0026gt;\u003ccode\u003e-Properties * | Get-Member\u003c/code\u003e\u003c/p\u003e\n\n\t\t\t\u003c/div\u003e\n\n\t",
                         "Related Links":  "\n\t\u003cul data-no-chunk=\"\"\u003e\n\t\t\t\u003cli\u003e\u003ca href=\"new-aduser?view=windowsserver2025-ps\" data-linktype=\"relative-path\"\u003eNew-ADUser\u003c/a\u003e\u003c/li\u003e\n\t\t\t\u003cli\u003e\u003ca href=\"remove-aduser?view=windowsserver2025-ps\" data-linktype=\"relative-path\"\u003eRemove-ADUser\u003c/a\u003e\u003c/li\u003e\n\t\t\t\u003cli\u003e\u003ca href=\"set-aduser?view=windowsserver2025-ps\" data-linktype=\"relative-path\"\u003eSet-ADUser\u003c/a\u003e\u003c/li\u003e\n\t\u003c/ul\u003e\n\u003c/div\u003e\n\t\t\t\t\t\n\t\t\u003cdiv\n\t\t\tid=\"ms--inline-notifications\"\n\t\t\tclass=\"margin-block-xs\"\n\t\t\tdata-bi-name=\"inline-notification\"\n\t\t\u003e\u003c/div\u003e\n\t \n\t\t\u003cdiv\n\t\t\tid=\"assertive-live-region\"\n\t\t\trole=\"alert\"\n\t\t\taria-live=\"assertive\"\n\t\t\tclass=\"visually-hidden\"\n\t\t\taria-relevant=\"additions\"\n\t\t\taria-atomic=\"true\"\n\t\t\u003e\u003c/div\u003e\n\t\t\u003cdiv\n\t\t\tid=\"polite-live-region\"\n\t\t\trole=\"status\"\n\t\t\taria-live=\"polite\"\n\t\t\tclass=\"visually-hidden\"\n\t\t\taria-relevant=\"additions\"\n\t\t\taria-atomic=\"true\"\n\t\t\u003e\u003c/div\u003e\n\t\n\t\t\t\t\t\n\t\t\u003c!-- feedback section --\u003e\n\t\t\u003csection\n\t\t\tid=\"site-user-feedback-footer\"\n\t\t\tclass=\"font-size-sm margin-top-md display-none-print display-none-desktop\"\n\t\t\tdata-test-id=\"site-user-feedback-footer\"\n\t\t\tdata-bi-name=\"site-feedback-section\"\n\t\t\u003e\n\t\t\t\u003chr class=\"hr\" /\u003e\n\t\t\t",
                         "Examples":  "\n\t\u003ch3 id=\"example-1-get-all-of-the-users-in-a-container\" data-chunk-ids=\"example-1-get-all-of-the-users-in-a-container\"\u003eExample 1: Get all of the users in a container\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"example-1-get-all-of-the-users-in-a-container\"\u003e\n\t\t\u003cpre\u003e\u003ccode class=\"lang-powershell\"\u003ePS C:\\\u0026gt; Get-ADUser -Filter * -SearchBase \"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\"\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThis command gets all users in the container OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\t\u003ch3 id=\"example-2-get-a-filtered-list-of-users\" data-chunk-ids=\"example-2-get-a-filtered-list-of-users\"\u003eExample 2: Get a filtered list of users\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"example-2-get-a-filtered-list-of-users\"\u003e\n\t\t\u003cpre\u003e\u003ccode class=\"lang-powershell\"\u003ePS C:\\\u0026gt; Get-ADUser -Filter \u0027Name -like \"*SvcAccount\"\u0027 | Format-Table Name,SamAccountName -A\n\u003c/code\u003e\u003c/pre\u003e\n\u003cpre\u003e\u003ccode class=\"lang-Output\"\u003eName             SamAccountName\n----             --------------\nSQL01 SvcAccount SQL01\nSQL02 SvcAccount SQL02\nIIS01 SvcAccount IIS01\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThis command gets all users that have a name that ends with SvcAccount.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\t\u003ch3 id=\"example-3-get-all-of-the-properties-for-a-specified-user\" data-chunk-ids=\"example-3-get-all-of-the-properties-for-a-specified-user\"\u003eExample 3: Get all of the properties for a specified user\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"example-3-get-all-of-the-properties-for-a-specified-user\"\u003e\n\t\t\u003cpre\u003e\u003ccode class=\"lang-powershell\"\u003ePS C:\\\u0026gt; Get-ADUser -Identity ChewDavid -Properties *\n\u003c/code\u003e\u003c/pre\u003e\n\u003cpre\u003e\u003ccode class=\"lang-Output\"\u003eSurname           : David\nName              : Chew David\nUserPrincipalName :\nGivenName         : David\nEnabled           : False\nSamAccountName    : ChewDavid\nObjectClass       : user\nSID               : S-1-5-21-**********-**********-**********-3544\nObjectGUID        : e1418d64-096c-4cb0-b903-ebb66562d99d\nDistinguishedName : CN=Chew David,OU=NorthAmerica,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThis command gets all of the properties of the user with the SAM account name ChewDavid.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\t\u003ch3 id=\"example-4-get-a-specified-user\" data-chunk-ids=\"example-4-get-a-specified-user\"\u003eExample 4: Get a specified user\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"example-4-get-a-specified-user\"\u003e\n\t\t\u003cpre\u003e\u003ccode class=\"lang-powershell\"\u003ePS C:\\\u0026gt; Get-ADUser -Filter \"Name -eq \u0027ChewDavid\u0027\" -SearchBase \"DC=AppNC\" -Properties \"mail\" -Server lds.Fabrikam.com:50000\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThis command gets the user with the name ChewDavid in the Active Directory Lightweight Directory Services (AD LDS) instance.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\t\u003ch3 id=\"example-5-get-all-enabled-user-accounts\" data-chunk-ids=\"example-5-get-all-enabled-user-accounts\"\u003eExample 5: Get all enabled user accounts\u003c/h3\u003e\n\t\u003cdiv data-chunk-ids=\"example-5-get-all-enabled-user-accounts\"\u003e\n\t\t\u003cpre\u003e\u003ccode class=\"lang-powershell\"\u003eC:\\PS\u0026gt; Get-ADUser -LDAPFilter \u0027(!userAccountControl:1.2.840.113556.1.4.803:=2)\u0027\n\u003c/code\u003e\u003c/pre\u003e\n\u003cp\u003eThis command gets all enabled user accounts in Active Directory using an LDAP filter.\u003c/p\u003e\n\n\t\u003c/div\u003e\n\n\t",
                         "Feedback":  "\n\t\t\t\u003cdiv class=\"display-flex flex-wrap-wrap align-items-center\"\u003e\n\t\t\t\t\u003cp class=\"font-weight-semibold margin-xxs margin-left-none\"\u003e\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t\u003c/p\u003e\n\t\t\t\t\u003cdiv class=\"buttons\"\u003e\n\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\tclass=\"thumb-rating-button like button button-primary button-sm\"\n\t\t\t\t\t\tdata-test-id=\"footer-rating-yes\"\n\t\t\t\t\t\tdata-binary-rating-response=\"rating-yes\"\n\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\ttitle=\"This article is helpful\"\n\t\t\t\t\t\tdata-bi-name=\"button-rating-yes\"\n\t\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-like\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eYes\u003c/span\u003e\n\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\tclass=\"thumb-rating-button dislike button button-primary button-sm\"\n\t\t\t\t\t\tdata-test-id=\"footer-rating-no\"\n\t\t\t\t\t\tdata-binary-rating-response=\"rating-no\"\n\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\ttitle=\"This article is not helpful\"\n\t\t\t\t\t\tdata-bi-name=\"button-rating-no\"\n\t\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\t\u003e\n\t\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"docon docon-dislike\"\u003e\u003c/span\u003e\n\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\u003cspan\u003eNo\u003c/span\u003e\n\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\u003c/div\u003e\n\t\t\t\u003c/div\u003e\n\t\t\u003c/section\u003e\n\t\t\u003c!-- end feedback section --\u003e\n\t\n\t\t\t\t\u003c/div\u003e\n\t\t\t\t\n\t\t\t\u003c/div\u003e\n\t\t\t\n\t\t\u003cdiv\n\t\t\tid=\"action-panel\"\n\t\t\trole=\"region\"\n\t\t\taria-label=\"Action Panel\"\n\t\t\tclass=\"action-panel\"\n\t\t\ttabindex=\"-1\"\n\t\t\u003e\u003c/div\u003e\n\t\n\t\t\n\t\t\t\t\u003c/main\u003e\n\t\t\t\t\u003caside\n\t\t\t\t\tid=\"layout-body-aside\"\n\t\t\t\t\tclass=\"layout-body-aside \"\n\t\t\t\t\tdata-bi-name=\"aside\"\n\t\t\t  \u003e\n\t\t\t\t\t\n\t\t\u003cdiv\n\t\t\tid=\"ms--additional-resources\"\n\t\t\tclass=\"right-container padding-sm display-none display-block-desktop height-full\"\n\t\t\tdata-bi-name=\"pageactions\"\n\t\t\trole=\"complementary\"\n\t\t\taria-label=\"Additional resources\"\n\t\t\u003e\n\t\t\t\u003cdiv id=\"affixed-right-container\" data-bi-name=\"right-column\"\u003e\n\t\t\t\t\n\t\t\u003cnav\n\t\t\tid=\"side-doc-outline\"\n\t\t\tclass=\"doc-outline border-bottom padding-bottom-xs margin-bottom-xs\"\n\t\t\tdata-bi-name=\"intopic toc\"\n\t\t\taria-label=\"In this article\"\n\t\t\u003e\n\t\t\t\u003ch3\u003eIn this article\u003c/h3\u003e\n\t\t\u003c/nav\u003e\n\t\n\t\t\t\t\u003c!-- Feedback --\u003e\n\t\t\t\t\n\t\t\u003csection\n\t\t\tid=\"ms--site-user-feedback-right-rail\"\n\t\t\tclass=\"font-size-sm display-none-print\"\n\t\t\tdata-test-id=\"site-user-feedback-right-rail\"\n\t\t\tdata-bi-name=\"site-feedback-right-rail\"\n\t\t\u003e\n\t\t\t\u003cp class=\"font-weight-semibold margin-bottom-xs\"\u003eWas this page helpful?\u003c/p\u003e\n\t\t\t\u003cdiv class=\"buttons\"\u003e\n\t\t\t\t\u003cbutton\n\t\t\t\t\tclass=\"thumb-rating-button like button button-primary button-sm\"\n\t\t\t\t\tdata-test-id=\"right-rail-rating-yes\"\n\t\t\t\t\tdata-binary-rating-response=\"rating-yes\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\ttitle=\"This article is helpful\"\n\t\t\t\t\tdata-bi-name=\"button-rating-yes\"\n\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\u003e\n\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-like\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\u003cspan\u003eYes\u003c/span\u003e\n\t\t\t\t\u003c/button\u003e\n\t\t\t\t\u003cbutton\n\t\t\t\t\tclass=\"thumb-rating-button dislike button button-primary button-sm\"\n\t\t\t\t\tdata-test-id=\"right-rail-rating-no\"\n\t\t\t\t\tdata-binary-rating-response=\"rating-no\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\ttitle=\"This article is not helpful\"\n\t\t\t\t\tdata-bi-name=\"button-rating-no\"\n\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\u003e\n\t\t\t\t\t\u003cspan class=\"icon\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-dislike\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\u003cspan\u003eNo\u003c/span\u003e\n\t\t\t\t\u003c/button\u003e\n\t\t\t\u003c/div\u003e\n\t\t\u003c/section\u003e\n\t\n\t\t\t\u003c/div\u003e\n\t\t\u003c/div\u003e\n\t\n\t\t\t  \u003c/aside\u003e \u003csection\n\t\t\t\t\tid=\"layout-body-flyout\"\n\t\t\t\t\tclass=\"layout-body-flyout \"\n\t\t\t\t\tdata-bi-name=\"flyout\"\n\t\t\t  \u003e\n\t\t\t\t\t \u003cdiv\n\tclass=\"height-full border-left background-color-body-medium\"\n\tid=\"ask-learn-flyout\"\n\u003e\u003c/div\u003e\n\t\t\t  \u003c/section\u003e \u003cdiv class=\"layout-body-footer \" data-bi-name=\"layout-footer\"\u003e\n\t\t\u003cfooter\n\t\t\tid=\"footer\"\n\t\t\tdata-test-id=\"footer\"\n\t\t\tdata-bi-name=\"footer\"\n\t\t\tclass=\"footer-layout has-padding has-default-focus border-top  uhf-container\"\n\t\t\trole=\"contentinfo\"\n\t\t\u003e\n\t\t\t\u003cdiv class=\"display-flex gap-xs flex-wrap-wrap is-full-height padding-right-lg-desktop\"\u003e\n\t\t\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"#\"\n\t\t\tdata-bi-name=\"select-locale\"\n\t\t\tclass=\"locale-selector-link flex-shrink-0 button button-sm button-clear external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003e\u003cspan class=\"icon\" aria-hidden=\"true\"\n\t\t\t\t\u003e\u003cspan class=\"docon docon-world\"\u003e\u003c/span\u003e\u003c/span\n\t\t\t\u003e\u003cspan class=\"local-selector-link-text\"\u003een-us\u003c/span\u003e\u003c/a\n\t\t\u003e\n\t\n\t\t\t\t\u003cdiv class=\"ccpa-privacy-link\" data-ccpa-privacy-link hidden\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://aka.ms/yourcaliforniaprivacychoices\"\n\t\t\tdata-bi-name=\"your-privacy-choices\"\n\t\t\tclass=\"button button-sm button-clear flex-shrink-0 external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003e\n\t\t\u003csvg\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\tviewBox=\"0 0 30 14\"\n\t\t\txml:space=\"preserve\"\n\t\t\theight=\"16\"\n\t\t\twidth=\"43\"\n\t\t\taria-hidden=\"true\"\n\t\t\tfocusable=\"false\"\n\t\t\u003e\n\t\t\t\u003cpath\n\t\t\t\td=\"M7.4 12.8h6.8l3.1-11.6H7.4C4.2 1.2 1.6 3.8 1.6 7s2.6 5.8 5.8 5.8z\"\n\t\t\t\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;fill:#fff\"\n\t\t\t\u003e\u003c/path\u003e\n\t\t\t\u003cpath\n\t\t\t\td=\"M22.6 0H7.4c-3.9 0-7 3.1-7 7s3.1 7 7 7h15.2c3.9 0 7-3.1 7-7s-3.2-7-7-7zm-21 7c0-3.2 2.6-5.8 5.8-5.8h9.9l-3.1 11.6H7.4c-3.2 0-5.8-2.6-5.8-5.8z\"\n\t\t\t\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;fill:#06f\"\n\t\t\t\u003e\u003c/path\u003e\n\t\t\t\u003cpath\n\t\t\t\td=\"M24.6 4c.******* 0 .8L22.5 7l2.2 2.2c.******* 0 .8-.2.2-.6.2-.8 0l-2.2-2.2-2.2 2.2c-.2.2-.6.2-.8 0-.2-.2-.2-.6 0-.8L20.8 7l-2.2-2.2c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0l2.2 2.2L23.8 4c.2-.2.6-.2.8 0z\"\n\t\t\t\tstyle=\"fill:#fff\"\n\t\t\t\u003e\u003c/path\u003e\n\t\t\t\u003cpath\n\t\t\t\td=\"M12.7 4.1c.*******.1.8L8.6 9.8c-.1.1-.2.2-.3.2-.2.1-.5.1-.7-.1L5.4 7.7c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0L8 8.6l3.8-4.5c.2-.2.6-.2.9 0z\"\n\t\t\t\tstyle=\"fill:#06f\"\n\t\t\t\u003e\u003c/path\u003e\n\t\t\u003c/svg\u003e\n\t\n\t\t\t\u003cspan\u003eYour Privacy Choices\u003c/span\u003e\u003c/a\n\t\t\u003e\n\t\n\t\u003c/div\u003e\n\t\t\t\t\u003cdiv class=\"flex-shrink-0\"\u003e\n\t\t\u003cdiv class=\"dropdown has-caret-up\"\u003e\n\t\t\t\u003cbutton\n\t\t\t\tdata-test-id=\"theme-selector-button\"\n\t\t\t\tclass=\"dropdown-trigger button button-clear button-sm has-inner-focus theme-dropdown-trigger\"\n\t\t\t\taria-controls=\"{{ themeMenuId }}\"\n\t\t\t\taria-expanded=\"false\"\n\t\t\t\ttitle=\"Theme\"\n\t\t\t\tdata-bi-name=\"theme\"\n\t\t\t\u003e\n\t\t\t\t\u003cspan class=\"icon\"\u003e\n\t\t\t\t\t\u003cspan class=\"docon docon-sun\" aria-hidden=\"true\"\u003e\u003c/span\u003e\n\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003cspan\u003eTheme\u003c/span\u003e\n\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\u003c/span\u003e\n\t\t\t\u003c/button\u003e\n\t\t\t\u003cdiv class=\"dropdown-menu\" id=\"{{ themeMenuId }}\" role=\"menu\"\u003e\n\t\t\t\t\u003cul class=\"theme-selector padding-xxs\" data-test-id=\"theme-dropdown-menu\"\u003e\n\t\t\t\t\t\u003cli class=\"theme display-block\"\u003e\n\t\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"light\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"theme-light margin-right-xxs\"\u003e\n\t\t\t\t\t\t\t\t\u003cspan\n\t\t\t\t\t\t\t\t\tclass=\"theme-selector-icon border display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\t\t\u003csvg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\"\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect width=\"22\" height=\"14\" class=\"has-fill-body-background\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\u003c/svg\u003e\n\t\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003cspan role=\"menuitem\"\u003e Light \u003c/span\u003e\n\t\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\t\u003c/li\u003e\n\t\t\t\t\t\u003cli class=\"theme display-block\"\u003e\n\t\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"dark\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"theme-dark margin-right-xxs\"\u003e\n\t\t\t\t\t\t\t\t\u003cspan\n\t\t\t\t\t\t\t\t\tclass=\"border theme-selector-icon display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\t\t\u003csvg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\"\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect width=\"22\" height=\"14\" class=\"has-fill-body-background\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\u003c/svg\u003e\n\t\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003cspan role=\"menuitem\"\u003e Dark \u003c/span\u003e\n\t\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\t\u003c/li\u003e\n\t\t\t\t\t\u003cli class=\"theme display-block\"\u003e\n\t\t\t\t\t\t\u003cbutton\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"high-contrast\"\n\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\u003cspan class=\"theme-high-contrast margin-right-xxs\"\u003e\n\t\t\t\t\t\t\t\t\u003cspan\n\t\t\t\t\t\t\t\t\tclass=\"border theme-selector-icon display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t\u003e\n\t\t\t\t\t\t\t\t\t\u003csvg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\"\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect width=\"22\" height=\"14\" class=\"has-fill-body-background\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" /\u003e\n\t\t\t\t\t\t\t\t\t\t\u003crect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" /\u003e\n\t\t\t\t\t\t\t\t\t\u003c/svg\u003e\n\t\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\t\t\t\u003cspan role=\"menuitem\"\u003e High contrast \u003c/span\u003e\n\t\t\t\t\t\t\u003c/button\u003e\n\t\t\t\t\t\u003c/li\u003e\n\t\t\t\t\u003c/ul\u003e\n\t\t\t\u003c/div\u003e\n\t\t\u003c/div\u003e\n\t\u003c/div\u003e\n\t\t\t\u003c/div\u003e\n\t\t\t\u003cul class=\"links\" data-bi-name=\"footerlinks\"\u003e\n\t\t\t\t\u003cli class=\"manage-cookies-holder\" hidden=\"\"\u003e\u003c/li\u003e\n\t\t\t\t\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/principles-for-ai-generated-content\"\n\t\t\tdata-bi-name=\"aiDisclaimer\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eAI Disclaimer\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/previous-versions/\"\n\t\t\tdata-bi-name=\"archivelink\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003ePrevious Versions\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e \u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://techcommunity.microsoft.com/t5/microsoft-learn-blog/bg-p/MicrosoftLearnBlog\"\n\t\t\tdata-bi-name=\"bloglink\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eBlog\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e \u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/contribute\"\n\t\t\tdata-bi-name=\"contributorGuide\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eContribute\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://go.microsoft.com/fwlink/?LinkId=521839\"\n\t\t\tdata-bi-name=\"privacy\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003ePrivacy\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/legal/termsofuse\"\n\t\t\tdata-bi-name=\"termsofuse\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eTerms of Use\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\u003cli\u003e\n\t\t\n\t\t\u003ca\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://www.microsoft.com/legal/intellectualproperty/Trademarks/\"\n\t\t\tdata-bi-name=\"trademarks\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t\u003eTrademarks\u003c/a\n\t\t\u003e\n\t\n\t\u003c/li\u003e\n\t\t\t\t\u003cli\u003e\u0026copy; Microsoft 2025\u003c/li\u003e\n\t\t\t\u003c/ul\u003e\n\t\t\u003c/footer\u003e\n\t\u003c/footer\u003e\n\t\t\t\u003c/body\u003e\n\t\t\u003c/html\u003e",
                         "Notes":  "\n\t\u003cdiv data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server\"\u003e\n\t\t\u003cul\u003e\n\u003cli\u003eThis cmdlet does not work with an Active Directory snapshot.\u003c/li\u003e\n\u003c/ul\u003e\n\n\t\u003c/div\u003e\n\n\t"
                     },
    "inputs":  [

               ],
    "syntax_blocks":  [

                      ],
    "page_info":  {
                      "module":  "",
                      "title":  "Get-ADUser (ActiveDirectory) | Microsoft Learn",
                      "command_name":  "get-aduser"
                  },
    "parameters":  {
                       "CommonParameters":  {
                                                "raw_content":  "\n\t\t\u003cdiv data-no-chunk=\"\"\u003e\n\t\t\t\u003cp\u003eThis cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable,\n-InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable,\n-ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see\n\u003ca href=\"https://go.microsoft.com/fwlink/?LinkID=113216\" data-linktype=\"external\"\u003eabout_CommonParameters\u003c/a\u003e.\u003c/p\u003e\n\n\t\t\u003c/div\u003e\n\n\t",
                                                "properties":  {

                                                               },
                                                "description":  "This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable, -InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable, -ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see about_CommonParameters.",
                                                "name":  "CommonParameters"
                                            },
                       "LDAPFilter":  {
                                          "raw_content":  "\n\t\t\u003cp\u003eSpecifies an LDAP query string that is used to filter Active Directory objects.\nYou can use this parameter to run your existing LDAP queries.\nThe \u003cem\u003eFilter\u003c/em\u003e parameter syntax supports the same functionality as the LDAP syntax.\nFor more information, see the \u003cem\u003eFilter\u003c/em\u003e parameter description or type \u003ccode\u003eGet-Help about_ActiveDirectory_Filter\u003c/code\u003e.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"ldapfilter-properties\" data-chunk-ids=\"ldapfilter\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"ldapfilter\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"ldapfilter-sets\" data-chunk-ids=\"ldapfilter\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"ldapfilter\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tLdap\u003cwbr\u003eFilter \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eTrue\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t",
                                          "properties":  {
                                                             "Mandatory":  "True",
                                                             "Position":  "Named",
                                                             "Value from remaining arguments":  "False",
                                                             "Default value":  "None",
                                                             "Value from pipeline":  "False",
                                                             "Supports wildcards":  "False",
                                                             "Value from pipeline by property name":  "False",
                                                             "DontShow":  "False"
                                                         },
                                          "description":  "Specifies an LDAP query string that is used to filter Active Directory objects. You can use this parameter to run your existing LDAP queries. The Filter parameter syntax supports the same functionality as the LDAP syntax. For more information, see the Filter parameter description or type Get-Help about_ActiveDirectory_Filter.",
                                          "name":  "LDAPFilter"
                                      },
                       "Server":  {
                                      "raw_content":  "\n\t\t\u003cp\u003eSpecifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server.\nThe service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory Snapshot instance.\u003c/p\u003e\n\u003cp\u003eDomain name values:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eFully qualified domain name (FQDN)\u003c/li\u003e\n\u003cli\u003eNetBIOS name\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eDirectory server values:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eFully qualified directory server name\u003c/li\u003e\n\u003cli\u003eNetBIOS name\u003c/li\u003e\n\u003cli\u003eFully qualified directory server name and port\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eThe default value for the \u003cem\u003eServer\u003c/em\u003e parameter is determined by one of the following methods in the order that they are listed:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eBy using \u003cem\u003eServer\u003c/em\u003e value from objects passed through the pipeline.\u003c/li\u003e\n\u003cli\u003eBy using the server information associated with the Active Directory PowerShell provider drive, when running under that drive.\u003c/li\u003e\n\u003cli\u003eBy using the domain of the computer running PowerShell.\u003c/li\u003e\n\u003c/ul\u003e\n\n\n\t\t\u003ch4 id=\"server-properties\" data-chunk-ids=\"server\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"server\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"server-sets\" data-chunk-ids=\"server\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"server\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t",
                                      "properties":  {
                                                         "Mandatory":  "False",
                                                         "Position":  "Named",
                                                         "Value from remaining arguments":  "False",
                                                         "Default value":  "None",
                                                         "Value from pipeline":  "False",
                                                         "Supports wildcards":  "False",
                                                         "Value from pipeline by property name":  "False",
                                                         "DontShow":  "False"
                                                     },
                                      "description":  "Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server. The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory Snapshot instance.",
                                      "name":  "Server"
                                  },
                       "Properties":  {
                                          "raw_content":  "\n\t\t\u003cp\u003eSpecifies the properties of the output object to retrieve from the server.\nUse this parameter to retrieve properties that are not included in the default set.\u003c/p\u003e\n\u003cp\u003eSpecify properties for this parameter as a comma-separated list of names.\nTo display all of the attributes that are set on the object, specify * (asterisk).\u003c/p\u003e\n\u003cp\u003eTo specify an individual extended property, use the name of the property.\nFor properties that are not default or extended properties, you must specify the LDAP display name of the attribute.\u003c/p\u003e\n\u003cp\u003eTo retrieve properties and display them for an object, you can use the Get-* cmdlet associated with the object and pass the output to the \u003cstrong\u003eGet-Member\u003c/strong\u003e cmdlet.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"properties-properties\" data-chunk-ids=\"properties\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"properties\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cp\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\u003cspan\u003e[\u003c/span\u003e\u003cspan\u003e]\u003c/span\u003e\u003c/p\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eAliases:\u003c/td\u003e\u003ctd\u003eProperty\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"properties-sets\" data-chunk-ids=\"properties\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"properties\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t",
                                          "properties":  {
                                                             "Mandatory":  "False",
                                                             "Position":  "Named",
                                                             "Aliases":  "Property",
                                                             "Default value":  "None",
                                                             "Value from remaining arguments":  "False",
                                                             "Value from pipeline":  "False",
                                                             "Supports wildcards":  "False",
                                                             "Value from pipeline by property name":  "False",
                                                             "DontShow":  "False"
                                                         },
                                          "description":  "Specifies the properties of the output object to retrieve from the server. Use this parameter to retrieve properties that are not included in the default set.",
                                          "name":  "Properties"
                                      },
                       "Identity":  {
                                        "raw_content":  "\n\t\t\u003cp\u003eSpecifies an Active Directory user object by providing one of the following property values.\nThe identifier in parentheses is the LDAP display name for the attribute.\nThe acceptable values for this parameter are:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eA distinguished name\u003c/li\u003e\n\u003cli\u003eA GUID (objectGUID)\u003c/li\u003e\n\u003cli\u003eA security identifier (objectSid)\u003c/li\u003e\n\u003cli\u003eA SAM account name (sAMAccountName)\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eThe cmdlet searches the default naming context or partition to find the object.\nIf two or more objects are found, the cmdlet returns a non-terminating error.\u003c/p\u003e\n\u003cp\u003eThis parameter can also get this object through the pipeline or you can set this parameter to an object instance.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"identity-properties\" data-chunk-ids=\"identity\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"identity\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eADUser\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"identity-sets\" data-chunk-ids=\"identity\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"identity\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tIdentity \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003e0\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eTrue\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eTrue\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t",
                                        "properties":  {
                                                           "Mandatory":  "True",
                                                           "Position":  "0",
                                                           "Value from remaining arguments":  "False",
                                                           "Default value":  "None",
                                                           "Value from pipeline":  "True",
                                                           "Supports wildcards":  "False",
                                                           "Value from pipeline by property name":  "False",
                                                           "DontShow":  "False"
                                                       },
                                        "description":  "Specifies an Active Directory user object by providing one of the following property values. The identifier in parentheses is the LDAP display name for the attribute. The acceptable values for this parameter are:",
                                        "name":  "Identity"
                                    },
                       "Credential":  {
                                          "raw_content":  "\n\t\t\u003cp\u003eSpecifies the user account credentials to use to perform this task.\nThe default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive.\nIf the cmdlet is run from such a provider drive, the account associated with the drive is the default.\u003c/p\u003e\n\u003cp\u003eTo specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a \u003cstrong\u003ePSCredential\u003c/strong\u003e object.\nIf you specify a user name for this parameter, the cmdlet prompts for a password.\u003c/p\u003e\n\u003cp\u003eYou can also create a \u003cstrong\u003ePSCredential\u003c/strong\u003e object by using a script or by using the \u003cstrong\u003eGet-Credential\u003c/strong\u003e cmdlet.\nYou can then set the \u003cem\u003eCredential\u003c/em\u003e parameter to the \u003cstrong\u003ePSCredential\u003c/strong\u003e object.\u003c/p\u003e\n\u003cp\u003eIf the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"credential-properties\" data-chunk-ids=\"credential\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"credential\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003ePSCredential\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"credential-sets\" data-chunk-ids=\"credential\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"credential\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\t(All) \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t",
                                          "properties":  {
                                                             "Mandatory":  "False",
                                                             "Position":  "Named",
                                                             "Value from remaining arguments":  "False",
                                                             "Default value":  "None",
                                                             "Value from pipeline":  "False",
                                                             "Supports wildcards":  "False",
                                                             "Value from pipeline by property name":  "False",
                                                             "DontShow":  "False"
                                                         },
                                          "description":  "Specifies the user account credentials to use to perform this task. The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated with the drive is the default.",
                                          "name":  "Credential"
                                      },
                       "Filter":  {
                                      "raw_content":  "\n\t\t\u003cp\u003eSpecifies a query string that retrieves Active Directory objects.\nThis string uses the PowerShell Expression Language syntax.\nThe PowerShell Expression Language syntax provides rich type-conversion support for value types received by the \u003cem\u003eFilter\u003c/em\u003e parameter.\nThe syntax uses an in-order representation, which means that the operator is placed between the operand and the value.\nFor more information about the \u003cem\u003eFilter\u003c/em\u003e parameter, type \u003ccode\u003eGet-Help about_ActiveDirectory_Filter\u003c/code\u003e.\u003c/p\u003e\n\u003cp\u003eSyntax:\u003c/p\u003e\n\u003cp\u003eThe following syntax uses Backus-Naur form to show how to use the PowerShell Expression Language for this parameter.\u003c/p\u003e\n\u003cp\u003e\u0026lt;filter\u0026gt;  ::= \"{\" \u0026lt;FilterComponentList\u0026gt; \"}\"\u003c/p\u003e\n\u003cp\u003e\u0026lt;FilterComponentList\u0026gt; ::= \u0026lt;FilterComponent\u0026gt; | \u0026lt;FilterComponent\u0026gt; \u0026lt;JoinOperator\u0026gt; \u0026lt;FilterComponent\u0026gt; | \u0026lt;NotOperator\u0026gt;  \u0026lt;FilterComponent\u0026gt;\u003c/p\u003e\n\u003cp\u003e\u0026lt;FilterComponent\u0026gt; ::= \u0026lt;attr\u0026gt; \u0026lt;FilterOperator\u0026gt; \u0026lt;value\u0026gt; | \"(\" \u0026lt;FilterComponent\u0026gt; \")\"\u003c/p\u003e\n\u003cp\u003e\u0026lt;FilterOperator\u0026gt; ::= \"-eq\" | \"-le\" | \"-ge\" | \"-ne\" | \"-lt\" | \"-gt\"| \"-approx\" | \"-bor\" | \"-band\" | \"-recursivematch\" | \"-like\" | \"-notlike\"\u003c/p\u003e\n\u003cp\u003e\u0026lt;JoinOperator\u0026gt; ::= \"-and\" | \"-or\"\u003c/p\u003e\n\u003cp\u003e\u0026lt;NotOperator\u0026gt; ::= \"-not\"\u003c/p\u003e\n\u003cp\u003e\u0026lt;attr\u0026gt; ::= \u0026lt;PropertyName\u0026gt; | \u0026lt;LDAPDisplayName of the attribute\u0026gt;\u003c/p\u003e\n\u003cp\u003e\u0026lt;value\u0026gt;::= \u0026lt;compare this value with an \u0026lt;attr\u0026gt; by using the specified \u0026lt;FilterOperator\u0026gt;\u0026gt;\u003c/p\u003e\n\u003cp\u003eFor a list of supported types for \u0026lt;value\u0026gt;, type \u003ccode\u003eGet-Help about_ActiveDirectory_ObjectModel\u003c/code\u003e.\u003c/p\u003e\n\u003cp\u003eNote: For String parameter type, PowerShell will cast the filter query to a string while processing the command. When using a string variable as a value in the filter component, make sure that it complies with the \u003ca href=\"/en-us/powershell/module/microsoft.powershell.core/about/about_quoting_rules\" data-linktype=\"absolute-path\"\u003ePowerShell Quoting Rules\u003c/a\u003e. For example, if the filter expression is double-quoted, the variable should be enclosed using single quotation marks:\n\u003cstrong\u003eGet-ADUser -Filter \"Name -like \u0027$UserName\u0027\"\u003c/strong\u003e. On the contrary, if curly braces are used to enclose the filter, the variable should not be quoted at all: \u003cstrong\u003eGet-ADUser -Filter {Name -like $UserName}\u003c/strong\u003e.\u003c/p\u003e\n\u003cp\u003eNote: PowerShell wildcards other than *, such as ?, are not supported by the \u003cem\u003eFilter\u003c/em\u003e syntax.\u003c/p\u003e\n\u003cp\u003eNote: To query using LDAP query strings, use the \u003cem\u003eLDAPFilter\u003c/em\u003e parameter.\u003c/p\u003e\n\n\n\t\t\u003ch4 id=\"filter-properties\" data-chunk-ids=\"filter\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"filter\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"filter-sets\" data-chunk-ids=\"filter\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"filter\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tFilter \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eTrue\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t",
                                      "properties":  {
                                                         "Mandatory":  "True",
                                                         "Position":  "Named",
                                                         "Value from remaining arguments":  "False",
                                                         "Default value":  "None",
                                                         "Value from pipeline":  "False",
                                                         "Supports wildcards":  "False",
                                                         "Value from pipeline by property name":  "False",
                                                         "DontShow":  "False"
                                                     },
                                      "description":  "Specifies a query string that retrieves Active Directory objects. This string uses the PowerShell Expression Language syntax. The PowerShell Expression Language syntax provides rich type-conversion support for value types received by the Filter parameter. The syntax uses an in-order representation, which means that the operator is placed between the operand and the value. For more information about the Filter parameter, type Get-Help about_ActiveDirectory_Filter.",
                                      "name":  "Filter"
                                  },
                       "Partition":  {
                                         "raw_content":  "\n\t\t\u003cp\u003eSpecifies the distinguished name of an Active Directory partition.\nThe distinguished name must be one of the naming contexts on the current directory server.\nThe cmdlet searches this partition to find the object defined by the \u003cem\u003eIdentity\u003c/em\u003e parameter.\u003c/p\u003e\n\u003cp\u003eIn many cases, a default value is used for the \u003cem\u003ePartition\u003c/em\u003e parameter if no value is specified.\nThe rules for determining the default value are given below.\nNote that rules listed first are evaluated first, and when a default value can be determined, no further rules are evaluated.\u003c/p\u003e\n\u003cp\u003eIn AD DS environments, a default value for \u003cem\u003ePartition\u003c/em\u003e is set in the following cases:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eIf the \u003cem\u003eIdentity\u003c/em\u003e parameter is set to a distinguished name, the default value of \u003cem\u003ePartition\u003c/em\u003e is automatically generated from this distinguished name.\u003c/li\u003e\n\u003cli\u003eIf running cmdlets from an Active Directory provider drive, the default value of \u003cem\u003ePartition\u003c/em\u003e is automatically generated from the current path in the drive.\u003c/li\u003e\n\u003cli\u003eIf none of the previous cases apply, the default value of \u003cem\u003ePartition\u003c/em\u003e is set to the default partition or naming context of the target domain.\u003c/li\u003e\n\u003c/ul\u003e\n\u003cp\u003eIn AD LDS environments, a default value for \u003cem\u003ePartition\u003c/em\u003e is set in the following cases:\u003c/p\u003e\n\u003cul\u003e\n\u003cli\u003eIf the \u003cem\u003eIdentity\u003c/em\u003e parameter is set to a distinguished name, the default value of \u003cem\u003ePartition\u003c/em\u003e is automatically generated from this distinguished name.\u003c/li\u003e\n\u003cli\u003eIf running cmdlets from an Active Directory provider drive, the default value of \u003cem\u003ePartition\u003c/em\u003e is automatically generated from the current path in the drive.\u003c/li\u003e\n\u003cli\u003eIf the target AD LDS instance has a default naming context, the default value of \u003cem\u003ePartition\u003c/em\u003e is set to the default naming context.\nTo specify a default naming context for an AD LDS environment, set the \u003cstrong\u003emsDS-defaultNamingContext\u003c/strong\u003e property of the Active Directory directory service agent object (\u003cstrong\u003enTDSDSA\u003c/strong\u003e) for the AD LDS instance.\u003c/li\u003e\n\u003cli\u003eIf none of the previous cases apply, the \u003cem\u003ePartition\u003c/em\u003e parameter does not take any default value.\u003c/li\u003e\n\u003c/ul\u003e\n\n\n\t\t\u003ch4 id=\"partition-properties\" data-chunk-ids=\"partition\"\u003eParameter properties\u003c/h4\u003e\n\t\t\u003ctable data-chunk-ids=\"partition\" class=\"table\"\u003e\n\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003eType:\u003c/td\u003e\u003ctd\u003e\u003cspan class=\"no-loc xref\"\u003eString\u003c/span\u003e\n\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDefault value:\u003c/td\u003e\u003ctd\u003eNone\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eSupports wildcards:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003ctr\u003e\u003ctd\u003eDontShow:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\u003c/tbody\u003e\u003c/table\u003e\n\n\t\t\u003ch4 id=\"partition-sets\" data-chunk-ids=\"partition\"\u003eParameter sets\u003c/h4\u003e\n\t\t\t\u003cdetails class=\"margin-top-sm\" data-chunk-ids=\"partition\" open=\"\"\u003e\n\t\t\t\t\u003csummary class=\"list-style-none link-button\"\u003e\n\t\t\t\t\tIdentity \n\t\t\t\t\t\u003cspan class=\"icon expanded-indicator\" aria-hidden=\"true\"\u003e\n\t\t\t\t\t\t\u003cspan class=\"docon docon-chevron-down-light\"\u003e\u003c/span\u003e\n\t\t\t\t\t\u003c/span\u003e\n\t\t\t\t\u003c/summary\u003e\n\t\t\t\t\u003ctable class=\"table\"\u003e\n\t\t\t\t\t\t\u003ctbody\u003e\u003ctr\u003e\u003ctd\u003ePosition:\u003c/td\u003e\u003ctd\u003eNamed\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eMandatory:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from pipeline by property name:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\t\t\u003ctr\u003e\u003ctd\u003eValue from remaining arguments:\u003c/td\u003e\u003ctd\u003eFalse\u003c/td\u003e\u003c/tr\u003e\n\t\t\t\t\u003c/tbody\u003e\u003c/table\u003e\n\t\t\t\u003c/details\u003e\n\t\t",
                                         "properties":  {
                                                            "Mandatory":  "False",
                                                            "Position":  "Named",
                                                            "Value from remaining arguments":  "False",
                                                            "Default value":  "None",
                                                            "Value from pipeline":  "False",
                                                            "Supports wildcards":  "False",
                                                            "Value from pipeline by property name":  "False",
                                                            "DontShow":  "False"
                                                        },
                                         "description":  "Specifies the distinguished name of an Active Directory partition. The distinguished name must be one of the naming contexts on the current directory server. The cmdlet searches this partition to find the object defined by the Identity parameter.",
                                         "name":  "Partition"
                                     }
                   },
    "examples":  [
                     {
                         "title":  "Example 1: Get all of the users in a container",
                         "description":  "This command gets all users in the container OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM.",
                         "code_blocks":  [
                                             "PS C:\\\u003e Get-ADUser -Filter * -SearchBase \"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\""
                                         ]
                     },
                     {
                         "title":  "Example 2: Get a filtered list of users",
                         "description":  "This command gets all users that have a name that ends with SvcAccount.",
                         "code_blocks":  [
                                             "PS C:\\\u003e Get-ADUser -Filter \u0027Name -like \"*SvcAccount\"\u0027 | Format-Table Name,SamAccountName -A",
                                             "Name             SamAccountName\n----             --------------\nSQL01 SvcAccount SQL01\nSQL02 SvcAccount SQL02\nIIS01 SvcAccount IIS01"
                                         ]
                     },
                     {
                         "title":  "Example 3: Get all of the properties for a specified user",
                         "description":  "This command gets all of the properties of the user with the SAM account name ChewDavid.",
                         "code_blocks":  [
                                             "PS C:\\\u003e Get-ADUser -Identity ChewDavid -Properties *",
                                             "Surname           : David\nName              : Chew David\nUserPrincipalName :\nGivenName         : David\nEnabled           : False\nSamAccountName    : ChewDavid\nObjectClass       : user\nSID               : S-1-5-21-**********-**********-**********-3544\nObjectGUID        : e1418d64-096c-4cb0-b903-ebb66562d99d\nDistinguishedName : CN=Chew David,OU=NorthAmerica,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM"
                                         ]
                     },
                     {
                         "title":  "Example 4: Get a specified user",
                         "description":  "This command gets the user with the name ChewDavid in the Active Directory Lightweight Directory Services (AD LDS) instance.",
                         "code_blocks":  [
                                             "PS C:\\\u003e Get-ADUser -Filter \"Name -eq \u0027ChewDavid\u0027\" -SearchBase \"DC=AppNC\" -Properties \"mail\" -Server lds.Fabrikam.com:50000"
                                         ]
                     },
                     {
                         "title":  "Example 5: Get all enabled user accounts",
                         "description":  "This command gets all enabled user accounts in Active Directory using an LDAP filter.",
                         "code_blocks":  [
                                             "C:\\PS\u003e Get-ADUser -LDAPFilter \u0027(!userAccountControl:1.2.840.113556.1.4.803:=2)\u0027"
                                         ]
                     }
                 ]
}
