/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    McpSvrMgr.h

Abstract:

    Header file for the MCP Server Manager component that manages and enumerates
    registered MCP servers.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/11/2025

--*/

#pragma once

#include <windows.h>
#include <combaseapi.h>
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <shared_mutex>
#include "AimxCommon.h"
#include "StringUtils.h"
#include "nlohmann/json.hpp"
#include "../../McpProtocolLib/McpJsonRpc.h"

// Forward declaration to avoid circular dependency
class RagServiceManager;

// MCP Tool information structure
struct MCP_TOOL_INFO
{
    GUID toolGuid;
    GUID serverGuid;  // Added: GUID of the server that owns this tool
    std::wstring serverName;
    std::wstring toolName;
    std::wstring description;
    nlohmann::json inputSchema;
    nlohmann::json outputSchema;
    bool isAvailable;

    // Constructor to auto-generate GUID
    MCP_TOOL_INFO() : isAvailable(false)
    {
        CoCreateGuid(&toolGuid);
        // serverGuid will be set when tool is added to a server
    }

    // Copy constructor
    MCP_TOOL_INFO(const MCP_TOOL_INFO& other)
        : toolGuid(other.toolGuid)
        , serverGuid(other.serverGuid)
        , serverName(other.serverName)
        , toolName(other.toolName)
        , description(other.description)
        , inputSchema(other.inputSchema)
        , outputSchema(other.outputSchema)
        , isAvailable(other.isAvailable)
    {
    }

    // Assignment operator
    MCP_TOOL_INFO& operator=(const MCP_TOOL_INFO& other)
    {
        if (this != &other)
        {
            toolGuid = other.toolGuid;
            serverGuid = other.serverGuid;
            serverName = other.serverName;
            toolName = other.toolName;
            description = other.description;
            inputSchema = other.inputSchema;
            outputSchema = other.outputSchema;
            isAvailable = other.isAvailable;
        }
        return *this;
    }

};

// MCP Server status enumeration
enum class MCP_SERVER_STATUS
{
    UNKNOWN = 0,
    DISABLED,
    ENABLED,
    RUNNING,
    FAILED
};

// Forward declarations for in-process server interfaces
class IInProcessMcpServer;
class IInProcessMcpServerFactory;

// Forward declaration for stdio client
class McpStdioClient;

// MCP Server information structure (supports both out-of-process and in-process servers)
struct MCP_SERVER_INFO
{
    GUID serverId;
    std::wstring serverName;
    std::wstring description;
    std::wstring packageFullName;
    MCP_SERVER_STATUS status;
    MCP_SERVER_TYPE serverType;  // Server type indicator
    bool isEnabled;
    std::vector<MCP_TOOL_INFO> availableTools;

    // Server-specific data (no union to avoid assignment operator issues)
    // Out-of-process server data
    std::wstring command;
    std::vector<std::wstring> arguments;

    // In-process server data
    std::shared_ptr<IInProcessMcpServer> serverInstance;

    // Constructor
    MCP_SERVER_INFO() : serverType(MCP_SERVER_TYPE::OUT_OF_PROCESS) { }

    // Copy constructor
    MCP_SERVER_INFO(const MCP_SERVER_INFO& other)
        : serverId(other.serverId)
        , serverName(other.serverName)
        , description(other.description)
        , packageFullName(other.packageFullName)
        , status(other.status)
        , serverType(other.serverType)
        , isEnabled(other.isEnabled)
        , availableTools(other.availableTools)
        , command(other.command)
        , arguments(other.arguments)
        , serverInstance(other.serverInstance)
    {
    }

    // Assignment operator
    MCP_SERVER_INFO& operator=(const MCP_SERVER_INFO& other)
    {
        if (this != &other)
        {
            serverId = other.serverId;
            serverName = other.serverName;
            description = other.description;
            packageFullName = other.packageFullName;
            status = other.status;
            serverType = other.serverType;
            isEnabled = other.isEnabled;
            availableTools = other.availableTools;
            command = other.command;
            arguments = other.arguments;
            serverInstance = other.serverInstance;
        }
        return *this;
    }
};

// MCP Server Manager class
class McpSvrMgr
{
public:
    // Initialize the MCP Server Manager
    static HRESULT Initialize();

    // Uninitialize and cleanup resources
    static void Uninitialize();

    // Get only enabled MCP servers
    static HRESULT GetEnabledMcpServers(
        _Out_ std::vector<MCP_SERVER_INFO>& servers
        );

    // Get server information by ID
    static HRESULT GetServerInfo(
        _In_ const GUID& serverId,
        _Out_ MCP_SERVER_INFO& serverInfo
        );

    // Get server information by name
    static HRESULT GetServerInfoByName(
        _In_ const std::wstring& serverName,
        _Out_ MCP_SERVER_INFO& serverInfo
        );

    // Execute a tool on a specific MCP server
    static HRESULT ExecuteServerTool(
        _In_ const GUID& serverId,
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _Out_ nlohmann::json& result
        );

    // In-Process Server Management Methods

    // Register an in-process MCP server
    static HRESULT RegisterInProcessServer(
        _In_ std::shared_ptr<IInProcessMcpServer> server
        );

    // Unregister an in-process MCP server
    static HRESULT UnregisterInProcessServer(
        _In_ const std::wstring& serverName
        );

    // Register an in-process server factory
    static HRESULT RegisterInProcessServerFactory(
        _In_ std::shared_ptr<IInProcessMcpServerFactory> factory
        );

    // Get the singleton instance
    static McpSvrMgr* GetInstance();

    // Register all tools with RAG service
    static HRESULT RegisterAllToolsWithRagService();

    // Configuration-based (JSON-based) Server Management Methods
    
    // Load configured servers from a JSON file
    static HRESULT LoadConfiguredServers(
        _In_ const std::wstring& configPath = L""
        );

    static HRESULT RegisterConfiguredServer(
        _In_ const MCP_SERVER_CONFIG& config
        );

    static HRESULT UnregisterConfiguredServer(
        _In_ const std::wstring& serverName
        );

    static HRESULT GetConfiguredServers(
        _Out_ std::vector<std::wstring>& serverNames
        );

    static HRESULT GetServerConfiguration(
        _In_ const std::wstring& serverName,
        _Out_ MCP_SERVER_CONFIG& config
        );

    // Tool-centric lookup methods
    static HRESULT FindToolByGuid(
        _In_ const GUID& toolGuid,
        _Out_ MCP_TOOL_INFO& toolInfo
        );

    static HRESULT GetAllRegisteredTools(
        _Out_ std::vector<MCP_TOOL_INFO>& tools
        );

private:
    // Private constructor for singleton pattern
    McpSvrMgr();

    // Private destructor
    ~McpSvrMgr();

    // Delete copy constructor and assignment operator
    McpSvrMgr(const McpSvrMgr&) = delete;
    McpSvrMgr& operator=(const McpSvrMgr&) = delete;

    // Internal server execution methods
    HRESULT GetServerInfoInternal(
        _In_ const GUID& serverId,
        _Out_ MCP_SERVER_INFO& serverInfo
        );

    HRESULT ExecuteInProcessServerTool(
        _In_ const MCP_SERVER_INFO& serverInfo,
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _Out_ nlohmann::json& result
        );

    HRESULT ExecuteOutOfProcessServerTool(
        _In_ const GUID& serverId,
        _In_ const std::wstring& toolName,
        _In_ const nlohmann::json& parameters,
        _Out_ nlohmann::json& result
        );

    // Configuration-based server management methods
    static HRESULT CreateServerInfoFromConfig(
        _In_ const MCP_SERVER_CONFIG& config,
        _Out_ MCP_SERVER_INFO& serverInfo
        );

    static HRESULT GetOrCreateConnection(
        _In_ const std::wstring& serverName,
        _In_ const MCP_SERVER_CONFIG& config,
        _Out_ std::shared_ptr<McpStdioClient>& client
        );

    // Internal tool management methods
    void RegisterToolInternal(
        _In_ const MCP_TOOL_INFO& toolInfo
        );

    void UnregisterToolInternal(
        _In_ const GUID& toolGuid
        );

    void UnregisterAllToolsForServerInternal(
        _In_ const GUID& serverGuid
        );

    static void CleanupUnhealthyConnections();

    static std::wstring GetConfigurationPath();

    static HRESULT CreateDefaultConfigurationFile(
        _In_ const std::wstring& configPath
        );

    // Internal data structures
    static McpSvrMgr* s_instance;
    static std::mutex s_instanceMutex;

    // Configuration-based server storage
    static std::unordered_map<std::wstring, MCP_SERVER_CONFIG> s_configuredServers;
    static std::shared_mutex s_configuredServersMutex;

    // Simple connection pool
    static std::unordered_map<std::wstring, std::shared_ptr<McpStdioClient>> s_connectionPool;
    static std::mutex s_connectionPoolMutex; 

    // MCP server collection
    std::unordered_map<GUID, MCP_SERVER_INFO, GuidHash, GuidEqual> m_serverInfos;
    mutable std::shared_mutex m_serverMapMutex;

    // MCP tool collection
    std::unordered_map<GUID, MCP_TOOL_INFO, GuidHash, GuidEqual> m_registeredTools;
    mutable std::shared_mutex m_registeredToolsMutex;

    // In-process server management
    std::unordered_map<std::wstring, std::shared_ptr<IInProcessMcpServer>> m_inProcessServers;
    std::vector<std::shared_ptr<IInProcessMcpServerFactory>> m_inProcessFactories;
    mutable std::shared_mutex m_inProcessServersMutex;
    
    bool m_initialized;
};
