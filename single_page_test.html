﻿ <!DOCTYPE html>
		<html
			class="layout layout-holy-grail   show-table-of-contents reference show-breadcrumb default-focus"
			lang="en-us"
			dir="ltr"
			data-authenticated="false"
			data-auth-status-determined="false"
			data-target="docs"
			x-ms-format-detection="none"
		>
			
		<head>
			<title>Get-ADUser (ActiveDirectory) | Microsoft Learn</title>
			<meta charset="utf-8" />
			<meta name="viewport" content="width=device-width, initial-scale=1.0" />
			<meta name="color-scheme" content="light dark" />

			<meta name="description" content="Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell." />
			<link rel="canonical" href="https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser?view=windowsserver2025-ps" /> 

			<!-- Non-customizable open graph and sharing-related metadata -->
			<meta name="twitter:card" content="summary_large_image" />
			<meta name="twitter:site" content="@MicrosoftLearn" />
			<meta property="og:type" content="website" />
			<meta property="og:image:alt" content="Microsoft Learn" />
			<meta property="og:image" content="https://learn.microsoft.com/en-us/media/open-graph-image.png" />
			<!-- Page specific open graph and sharing-related metadata -->
			<meta property="og:title" content="Get-ADUser (ActiveDirectory)" />
			<meta property="og:url" content="https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser?view=windowsserver2025-ps" />
			<meta property="og:description" content="Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell." />
			<meta name="platform_id" content="12007bea-81cb-fad7-d52a-dbb3ada967bf" /> 
			<meta name="locale" content="en-us" />
			 <meta name="adobe-target" content="true" />
			<meta name="uhfHeaderId" content="MSDocsHeader-M365-IT" />

			<meta name="page_type" content="powershell" />

			<!--page specific meta tags-->
			

			<!-- custom meta tags -->
			
		<meta name="uid" content="ActiveDirectory.Get-ADUser" />
	
		<meta name="module" content="ActiveDirectory" />
	
		<meta name="schema" content="PowerShellCmdlet1" />
	
		<meta name="ROBOTS" content="INDEX, FOLLOW" />
	
		<meta name="apiPlatform" content="powershell" />
	
		<meta name="archive_url" content="https://learn.microsoft.com/previous-versions/powershell/windows/get-started" />
	
		<meta name="author" content="robinharwood" />
	
		<meta name="breadcrumb_path" content="/powershell/windows/bread/toc.json" />
	
		<meta name="feedback_product_url" content="https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332" />
	
		<meta name="feedback_system" content="Standard" />
	
		<meta name="manager" content="tedhudek" />
	
		<meta name="ms.author" content="roharwoo" />
	
		<meta name="ms.devlang" content="powershell" />
	
		<meta name="ms.service" content="windows-11" />
	
		<meta name="ms.topic" content="reference" />
	
		<meta name="products" content="https://authoring-docs-microsoft.poolparty.biz/devrel/56936876-97d9-45cc-ad1b-9d63320447c8" />
	
		<meta name="products" content="https://authoring-docs-microsoft.poolparty.biz/devrel/56754133-c3c3-4a9f-af19-71bdbe19fccf" />
	
		<meta name="document type" content="cmdlet" />
	
		<meta name="external help file" content="Microsoft.ActiveDirectory.Management.dll-Help.xml" />
	
		<meta name="HelpUri" content="https://learn.microsoft.com/powershell/module/activedirectory/get-aduser?view=windowsserver2025-ps&amp;wt.mc_id=ps-gethelp" />
	
		<meta name="Module Name" content="ActiveDirectory" />
	
		<meta name="ms.date" content="2016-12-27T00:00:00Z" />
	
		<meta name="PlatyPS schema version" content="2024-05-01T00:00:00Z" />
	
		<meta name="document_id" content="7b1c4ac3-4780-50a5-03fb-07a9c30bb1e7" />
	
		<meta name="document_version_independent_id" content="77dffbab-e12c-8cc1-9ab7-9fcab4d8b7a4" />
	
		<meta name="updated_at" content="2025-05-14T22:44:00Z" />
	
		<meta name="original_content_git_url" content="https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md" />
	
		<meta name="gitcommit" content="https://github.com/MicrosoftDocs/windows-powershell-docs/blob/0ef3f225d29e26d1cf3119f37dfff70bb6165746/docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md" />
	
		<meta name="git_commit_id" content="0ef3f225d29e26d1cf3119f37dfff70bb6165746" />
	
		<meta name="monikers" content="windowsserver2025-ps" />
	
		<meta name="default_moniker" content="windowsserver2025-ps" />
	
		<meta name="site_name" content="Docs" />
	
		<meta name="depot_name" content="TechNet.windows-powershell" />
	
		<meta name="in_right_rail" content="h2h3" />
	
		<meta name="page_kind" content="command" />
	
		<meta name="toc_rel" content="../windowsserver2025-ps/toc.json" />
	
		<meta name="feedback_help_link_type" content="" />
	
		<meta name="feedback_help_link_url" content="" />
	
		<meta name="config_moniker_range" content="WindowsServer2025-ps" />
	
		<meta name="asset_id" content="module/activedirectory/get-aduser" />
	
		<meta name="moniker_range_name" content="ffb05b7b47577225af7c7b6a20151268" />
	
		<meta name="item_type" content="Content" />
	
		<meta name="source_path" content="docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md" />
	
		<meta name="github_feedback_content_git_url" content="https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md" />
	 
		<meta name="cmProducts" content="https://authoring-docs-microsoft.poolparty.biz/devrel/bcbcbad5-4208-4783-8035-8481272c98b8" data-source="generated" />
	
		<meta name="spProducts" content="https://authoring-docs-microsoft.poolparty.biz/devrel/43b2e5aa-8a6d-4de2-a252-692232e5edc8" data-source="generated" />
	

			<!-- assets and js globals -->
			
			<link rel="stylesheet" href="/static/assets/0.4.03126.7002-3880ccdd/styles/site-ltr.css" />
			<link rel="preconnect" href="//mscom.demdex.net" crossorigin />
						<link rel="dns-prefetch" href="//target.microsoft.com" />
						<link rel="dns-prefetch" href="//microsoftmscompoc.tt.omtrdc.net" />
						<link
							rel="preload"
							as="script"
							href="/static/third-party/adobe-target/at-js/2.9.0/at.js"
							integrity="sha384-1/viVM50hgc33O2gOgkWz3EjiD/Fy/ld1dKYXJRUyjNYVEjSUGcSN+iPiQF7e4cu"
							crossorigin="anonymous"
							id="adobe-target-script"
							type="application/javascript"
						/>
			<script src="https://wcpstatic.microsoft.com/mscc/lib/v2/wcp-consent.js"></script>
			<script src="https://js.monitor.azure.com/scripts/c/ms.jsll-4.min.js"></script>
			<script src="/_themes/docs.theme/master/en-us/_themes/global/deprecation.js"></script>

			<!-- msdocs global object -->
			<script id="msdocs-script">
		var msDocs = {
  "environment": {
    "accessLevel": "online",
    "azurePortalHostname": "portal.azure.com",
    "reviewFeatures": false,
    "supportLevel": "production",
    "systemContent": true,
    "siteName": "learn",
    "legacyHosting": false
  },
  "data": {
    "contentLocale": "en-us",
    "contentDir": "ltr",
    "userLocale": "en-us",
    "userDir": "ltr",
    "pageTemplate": "Reference",
    "brand": "",
    "context": {},
    "standardFeedback": true,
    "showFeedbackReport": false,
    "feedbackHelpLinkType": "",
    "feedbackHelpLinkUrl": "",
    "feedbackSystem": "Standard",
    "feedbackGitHubRepo": "",
    "feedbackProductUrl": "https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332",
    "extendBreadcrumb": true,
    "isEditDisplayable": true,
    "isPrivateUnauthorized": false,
    "hideViewSource": false,
    "isPermissioned": false,
    "hasRecommendations": false,
    "contributors": []
  },
  "functions": {}
};;
	</script>

			<!-- base scripts, msdocs global should be before this -->
			<script src="/static/assets/0.4.03126.7002-3880ccdd/scripts/en-us/index-docs.js"></script>
			

			<!-- json-ld -->
			
		</head>
	
			<body
				id="body"
				data-bi-name="body"
				class="layout-body "
				lang="en-us"
				dir="ltr"
			>
				<header class="layout-body-header">
		<div class="header-holder has-default-focus">
			
		<a
			href="#main"
			
			style="z-index: 1070"
			class="outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body"
			
		>
			Skip to main content
		</a>
	
		<a
			href="#side-doc-outline"
			
			style="z-index: 1070"
			class="outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body"
			
		>
			Skip to in-page navigation
		</a>
	
		<a
			href="#"
			data-skip-to-ask-learn
			style="z-index: 1070"
			class="outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body"
			hidden
		>
			Skip to Ask Learn chat experience
		</a>
	

			<div hidden id="cookie-consent-holder" data-test-id="cookie-consent-container"></div>
			<!-- Unsupported browser warning -->
			<div
				id="unsupported-browser"
				style="background-color: white; color: black; padding: 16px; border-bottom: 1px solid grey;"
				hidden
			>
				<div style="max-width: 800px; margin: 0 auto;">
					<p style="font-size: 24px">This browser is no longer supported.</p>
					<p style="font-size: 16px; margin-top: 16px;">
						Upgrade to Microsoft Edge to take advantage of the latest features, security updates, and technical support.
					</p>
					<div style="margin-top: 12px;">
						<a
							href="https://go.microsoft.com/fwlink/p/?LinkID=2092881 "
							style="background-color: #0078d4; border: 1px solid #0078d4; color: white; padding: 6px 12px; border-radius: 2px; display: inline-block;"
						>
							Download Microsoft Edge
						</a>
						<a
							href="https://learn.microsoft.com/en-us/lifecycle/faq/internet-explorer-microsoft-edge"
							style="background-color: white; padding: 6px 12px; border: 1px solid #505050; color: #171717; border-radius: 2px; display: inline-block;"
						>
							More info about Internet Explorer and Microsoft Edge
						</a>
					</div>
				</div>
			</div>
			<!-- site header -->
			<header
				id="ms--site-header"
				data-test-id="site-header-wrapper"
				role="banner"
				itemscope="itemscope"
				itemtype="http://schema.org/Organization"
			>
				<div
					id="ms--mobile-nav"
					class="site-header display-none-tablet padding-inline-none gap-none"
					data-bi-name="mobile-header"
					data-test-id="mobile-header"
				></div>
				<div
					id="ms--primary-nav"
					class="site-header display-none display-flex-tablet"
					data-bi-name="L1-header"
					data-test-id="primary-header"
				></div>
				<div
					id="ms--secondary-nav"
					class="site-header display-none display-flex-tablet"
					data-bi-name="L2-header"
					data-test-id="secondary-header"
				></div>
			</header>
			
		<!-- banner -->
		<div data-banner>
			<div id="disclaimer-holder"></div>
			
		</div>
		<!-- banner end -->
	
		</div>
	</header>
				 <section
					id="layout-body-menu"
					class="layout-body-menu display-flex"
					data-bi-name="menu"
			  >
					<div
		id="left-container"
		class="left-container display-none display-block-tablet padding-inline-sm padding-bottom-sm width-full"
	>
		<nav
			id="affixed-left-container"
			class="margin-top-sm-tablet position-sticky display-flex flex-direction-column"
			aria-label="Primary"
		></nav>
	</div>
			  </section>

				<main
					id="main"
					role="main"
					class="layout-body-main "
					data-bi-name="content"
					lang="en-us"
					dir="ltr"
				>
					
			<div
		id="ms--content-header"
		class="content-header default-focus border-bottom-none"
		data-bi-name="content-header"
	>
		<div class="content-header-controls margin-xxs margin-inline-sm-tablet">
			<button
				type="button"
				class="contents-button button button-sm margin-right-xxs"
				data-bi-name="contents-expand"
				aria-haspopup="true"
				data-contents-button
			>
				<span class="icon" aria-hidden="true"><span class="docon docon-menu"></span></span>
				<span class="contents-expand-title"> Table of contents </span>
			</button>
			<button
				type="button"
				class="ap-collapse-behavior ap-expanded button button-sm"
				data-bi-name="ap-collapse"
				aria-controls="action-panel"
			>
				<span class="icon" aria-hidden="true"><span class="docon docon-exit-mode"></span></span>
				<span>Exit editor mode</span>
			</button>
		</div>
	</div>
			<div data-main-column class="padding-sm padding-top-none padding-top-sm-tablet">
				<div>
					
		<div id="article-header" class="background-color-body margin-bottom-xs display-none-print">
			<div class="display-flex align-items-center justify-content-space-between">
				
		<details
			id="article-header-breadcrumbs-overflow-popover"
			class="popover"
			data-for="article-header-breadcrumbs"
		>
			<summary
				class="button button-clear button-primary button-sm inner-focus"
				aria-label="All breadcrumbs"
			>
				<span class="icon">
					<span class="docon docon-more"></span>
				</span>
			</summary>
			<div id="article-header-breadcrumbs-overflow" class="popover-content padding-none"></div>
		</details>

		<bread-crumbs
			id="article-header-breadcrumbs"
			data-test-id="article-header-breadcrumbs"
			class="overflow-hidden flex-grow-1 margin-right-sm margin-right-md-tablet margin-right-lg-desktop margin-left-negative-xxs padding-left-xxs"
		></bread-crumbs>
	 
		<div
			id="article-header-page-actions"
			class="opacity-none margin-left-auto display-flex flex-wrap-no-wrap align-items-stretch"
		>
			
		<button
			class="button button-sm border-none inner-focus display-none-tablet flex-shrink-0 "
			data-bi-name="ask-learn-assistant-entry"
			data-test-id="ask-learn-assistant-modal-entry-mobile"
			data-ask-learn-modal-entry
			type="button"
			style="min-width: max-content;"
			aria-expanded="false"
			aria-label="Ask Learn"
			hidden
		>
			<span class="icon font-size-lg" aria-hidden="true">
				<span class="docon docon-chat-sparkle gradient-ask-learn-logo"></span>
			</span>
		</button>
		<button
			class="button button-sm display-none display-inline-flex-tablet display-none-desktop flex-shrink-0 margin-right-xxs "
			data-bi-name="ask-learn-assistant-entry"
			data-test-id="ask-learn-assistant-modal-entry-tablet"
			data-ask-learn-modal-entry
			type="button"
			style="min-width: max-content;"
			aria-expanded="false"
			hidden
		>
			<span class="icon font-size-lg" aria-hidden="true">
				<span class="docon docon-chat-sparkle gradient-ask-learn-logo"></span>
			</span>
			<span>Ask Learn</span>
		</button>
		<button
			class="button button-sm display-none flex-shrink-0 display-inline-flex-desktop margin-right-xxs	 "
			data-bi-name="ask-learn-assistant-entry"
			data-test-id="ask-learn-assistant-flyout-entry"
			data-ask-learn-flyout-entry
			data-flyout-button="toggle"
			type="button"
			style="min-width: max-content;"
			aria-expanded="false"
			aria-controls="ask-learn-flyout"
			hidden
		>
			<span class="icon font-size-lg" aria-hidden="true">
				<span class="docon docon-chat-sparkle gradient-ask-learn-logo"></span>
			</span>
			<span>Ask Learn</span>
		</button>
	 
		<button
			type="button"
			id="ms--focus-mode-button"
			data-focus-mode
			data-bi-name="focus-mode-entry"
			class="button button-sm flex-shrink-0 margin-right-xxs display-none display-inline-flex-desktop"
		>
			<span class="icon font-size-lg" aria-hidden="true">
				<span class="docon docon-glasses"></span>
			</span>
			<span>Focus mode</span>
		</button>
	 

			<details class="popover popover-right" id="article-header-page-actions-overflow">
				<summary
					class="justify-content-flex-start button button-clear button-sm button-primary inner-focus"
					aria-label="More actions"
					title="More actions"
				>
					<span class="icon" aria-hidden="true">
						<span class="docon docon-more-vertical"></span>
					</span>
				</summary>
				<div class="popover-content">
					
		<button
			data-page-action-item="overflow-mobile"
			type="button"
			class="button-block button-sm has-inner-focus button button-clear display-none-tablet justify-content-flex-start text-align-left"
			data-bi-name="contents-expand"
			data-contents-button
			data-popover-close
		>
			<span class="icon">
				<span class="docon docon-editor-list-bullet" aria-hidden="true"></span>
			</span>
			<span class="contents-expand-title">Table of contents</span>
		</button>
	 
		<a
			id="lang-link-overflow"
			class="button-sm has-inner-focus button button-clear button-block justify-content-flex-start text-align-left"
			data-bi-name="language-toggle"
			data-page-action-item="overflow-all"
			data-check-hidden="true"
			data-read-in-link
			href="#"
			hidden
		>
			<span class="icon" aria-hidden="true" data-read-in-link-icon>
				<span class="docon docon-locale-globe"></span>
			</span>
			<span data-read-in-link-text>Read in English</span>
		</a>
	 
		<button
			type="button"
			class="collection button button-clear button-sm button-block justify-content-flex-start text-align-left inner-focus"
			data-list-type="collection"
			data-bi-name="collection"
			data-page-action-item="overflow-all"
			data-check-hidden="true"
			data-popover-close
		>
			<span class="icon" aria-hidden="true">
				<span class="docon docon-circle-addition"></span>
			</span>
			<span class="collection-status">Add</span>
		</button>
	
					
		<button
			type="button"
			class="collection button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus"
			data-list-type="plan"
			data-bi-name="plan"
			data-page-action-item="overflow-all"
			data-check-hidden="true"
			data-popover-close
			hidden
		>
			<span class="icon" aria-hidden="true">
				<span class="docon docon-circle-addition"></span>
			</span>
			<span class="plan-status">Add to plan</span>
		</button>
	  
		<a
			data-contenteditbtn
			class="button button-clear button-block button-sm inner-focus justify-content-flex-start text-align-left text-decoration-none"
			data-bi-name="edit"
			
			href="https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md"
			data-original_content_git_url="https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md"
			data-original_content_git_url_template="{repo}/blob/{branch}/docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md"
			data-pr_repo=""
			data-pr_branch=""
		>
			<span class="icon" aria-hidden="true">
				<span class="docon docon-edit-outline"></span>
			</span>
			<span>Edit</span>
		</a>
	
					
		<hr class="margin-block-xxs" />
		<h4 class="font-size-sm padding-left-xxs">Share via</h4>
		
					<a
						class="button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-facebook"
						data-bi-name="facebook"
						data-page-action-item="overflow-all"
						href="#"
					>
						<span class="icon color-primary" aria-hidden="true">
							<span class="docon docon-facebook-share"></span>
						</span>
						<span>Facebook</span>
					</a>

					<a
						href="#"
						class="button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-twitter"
						data-bi-name="twitter"
						data-page-action-item="overflow-all"
					>
						<span class="icon color-text" aria-hidden="true">
							<span class="docon docon-xlogo-share"></span>
						</span>
						<span>x.com</span>
					</a>

					<a
						href="#"
						class="button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-linkedin"
						data-bi-name="linkedin"
						data-page-action-item="overflow-all"
					>
						<span class="icon color-primary" aria-hidden="true">
							<span class="docon docon-linked-in-logo"></span>
						</span>
						<span>LinkedIn</span>
					</a>
					<a
						href="#"
						class="button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-email"
						data-bi-name="email"
						data-page-action-item="overflow-all"
					>
						<span class="icon color-primary" aria-hidden="true">
							<span class="docon docon-mail-message"></span>
						</span>
						<span>Email</span>
					</a>
			  
	 
		<hr class="margin-block-xxs" />
		<button
			class="button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus"
			type="button"
			data-bi-name="print"
			data-page-action-item="overflow-all"
			data-popover-close
			data-print-page
			data-check-hidden="true"
		>
			<span class="icon color-primary" aria-hidden="true">
				<span class="docon docon-print"></span>
			</span>
			<span>Print</span>
		</button>
	
				</div>
			</details>
		</div>
	
			</div>
		</div>
	
					<!-- azure disclaimer -->
					
					<!-- privateUnauthorizedTemplate is hidden by default -->
					
		<div unauthorized-private-section data-bi-name="permission-content-unauthorized-private" hidden>
			<hr class="hr margin-top-xs margin-bottom-sm" />
			<div class="notification notification-info">
				<div class="notification-content">
					<p class="margin-top-none notification-title">
						<span class="icon">
							<span class="docon docon-exclamation-circle-solid" aria-hidden="true"></span>
						</span>
						<span>Note</span>
					</p>
					<p class="margin-top-none authentication-determined not-authenticated">
						Access to this page requires authorization. You can try <a class="docs-sign-in" href="#" data-bi-name="permission-content-sign-in">signing in</a> or <a  class="docs-change-directory" data-bi-name="permisson-content-change-directory">changing directories</a>.
					</p>
					<p class="margin-top-none authentication-determined authenticated">
						Access to this page requires authorization. You can try <a class="docs-change-directory" data-bi-name="permisson-content-change-directory">changing directories</a>.
					</p>
				</div>
			</div>
		</div>
	
					<div class="content"></div>
					 
					<div class="content"><h1 data-chunk-ids="inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server" class="margin-bottom-xs">Get-ADUser</h1>

	<div class="margin-block-xxs">
		<ul class="metadata page-metadata align-items-center" data-bi-name="page info">
			
			
			
			
		</ul>
	</div>

<div class="metadata" data-chunk-ids="inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server">
		<dl class="attributeList">
			<dt>Module:</dt>
			<dd><a href="./?view=windowsserver2025-ps" data-linktype="relative-path">ActiveDirectory Module</a></dd>
		</dl>
</div>

<nav id="center-doc-outline" class="doc-outline is-hidden-desktop display-none-print margin-bottom-sm" data-bi-name="intopic toc" aria-label="">
  <h2 class="title is-6 margin-block-xs"></h2>
</nav>


	<div class="margin-block-sm" data-chunk-ids="inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server">
		<p>Gets one or more Active Directory users.</p>

	</div>

	<h2 id="syntax" data-chunk-ids="filter,identity,ldapfilter">Syntax</h2>
	<h3 id="filter" data-chunk-ids="filter">
		Filter (Default)
	</h3>
	<div data-chunk-ids="filter">
		<pre><code class="lang-Syntax">Get-ADUser
    -Filter &lt;String&gt;
    [-AuthType &lt;ADAuthType&gt;]
    [-Credential &lt;PSCredential&gt;]
    [-Properties &lt;String[]&gt;]
    [-ResultPageSize &lt;Int32&gt;]
    [-ResultSetSize &lt;Int32&gt;]
    [-SearchBase &lt;String&gt;]
    [-SearchScope &lt;ADSearchScope&gt;]
    [-Server &lt;String&gt;]
    [&lt;CommonParameters&gt;]
</code></pre>

	</div>
	<h3 id="identity" data-chunk-ids="identity">
		Identity
	</h3>
	<div data-chunk-ids="identity">
		<pre><code class="lang-Syntax">Get-ADUser
    [-Identity] &lt;ADUser&gt;
    [-AuthType &lt;ADAuthType&gt;]
    [-Credential &lt;PSCredential&gt;]
    [-Partition &lt;String&gt;]
    [-Properties &lt;String[]&gt;]
    [-Server &lt;String&gt;]
    [&lt;CommonParameters&gt;]
</code></pre>

	</div>
	<h3 id="ldapfilter" data-chunk-ids="ldapfilter">
		Ldap<wbr>Filter
	</h3>
	<div data-chunk-ids="ldapfilter">
		<pre><code class="lang-Syntax">Get-ADUser
    -LDAPFilter &lt;String&gt;
    [-AuthType &lt;ADAuthType&gt;]
    [-Credential &lt;PSCredential&gt;]
    [-Properties &lt;String[]&gt;]
    [-ResultPageSize &lt;Int32&gt;]
    [-ResultSetSize &lt;Int32&gt;]
    [-SearchBase &lt;String&gt;]
    [-SearchScope &lt;ADSearchScope&gt;]
    [-Server &lt;String&gt;]
    [&lt;CommonParameters&gt;]
</code></pre>

	</div>


	<h2 id="description" data-chunk-ids="inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server">Description</h2>
	<div data-chunk-ids="inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server">
		<p>The <strong>Get-ADUser</strong> cmdlet gets a specified user object or performs a search to get multiple user objects.</p>
<p>The <em>Identity</em> parameter specifies the Active Directory user to get.
You can identify a user by its distinguished name (DN), GUID, security identifier (SID), or Security Account Manager (SAM) account name.
You can also set the parameter to a user object variable such as <code>$&lt;localUserObject&gt;</code> or pass a user object through the pipeline to the <em>Identity</em> parameter.</p>
<p>To search for and retrieve more than one user, use the <em>Filter</em> or <em>LDAPFilter</em> parameters.
The <em>Filter</em> parameter uses the PowerShell Expression Language to write query strings for Active Directory.
PowerShell Expression Language syntax provides rich type-conversion support for value types received by the <em>Filter</em> parameter.
For more information about the <em>Filter</em> parameter syntax, type <code>Get-Help about_ActiveDirectory_Filter</code>.
If you have existing Lightweight Directory Access Protocol (LDAP) query strings, you can use the <em>LDAPFilter</em> parameter.</p>
<p>This cmdlet retrieves a default set of user object properties.
To retrieve additional properties use the <em>Properties</em> parameter.
For more information about how to determine the properties for user objects, see the <em>Properties</em> parameter description.</p>

	</div>

	<h2 id="examples" data-chunk-ids="example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts">Examples</h2>
	<h3 id="example-1-get-all-of-the-users-in-a-container" data-chunk-ids="example-1-get-all-of-the-users-in-a-container">Example 1: Get all of the users in a container</h3>
	<div data-chunk-ids="example-1-get-all-of-the-users-in-a-container">
		<pre><code class="lang-powershell">PS C:\&gt; Get-ADUser -Filter * -SearchBase "OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM"
</code></pre>
<p>This command gets all users in the container OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM.</p>

	</div>
	<h3 id="example-2-get-a-filtered-list-of-users" data-chunk-ids="example-2-get-a-filtered-list-of-users">Example 2: Get a filtered list of users</h3>
	<div data-chunk-ids="example-2-get-a-filtered-list-of-users">
		<pre><code class="lang-powershell">PS C:\&gt; Get-ADUser -Filter 'Name -like "*SvcAccount"' | Format-Table Name,SamAccountName -A
</code></pre>
<pre><code class="lang-Output">Name             SamAccountName
----             --------------
SQL01 SvcAccount SQL01
SQL02 SvcAccount SQL02
IIS01 SvcAccount IIS01
</code></pre>
<p>This command gets all users that have a name that ends with SvcAccount.</p>

	</div>
	<h3 id="example-3-get-all-of-the-properties-for-a-specified-user" data-chunk-ids="example-3-get-all-of-the-properties-for-a-specified-user">Example 3: Get all of the properties for a specified user</h3>
	<div data-chunk-ids="example-3-get-all-of-the-properties-for-a-specified-user">
		<pre><code class="lang-powershell">PS C:\&gt; Get-ADUser -Identity ChewDavid -Properties *
</code></pre>
<pre><code class="lang-Output">Surname           : David
Name              : Chew David
UserPrincipalName :
GivenName         : David
Enabled           : False
SamAccountName    : ChewDavid
ObjectClass       : user
SID               : S-1-5-21-**********-**********-**********-3544
ObjectGUID        : e1418d64-096c-4cb0-b903-ebb66562d99d
DistinguishedName : CN=Chew David,OU=NorthAmerica,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM
</code></pre>
<p>This command gets all of the properties of the user with the SAM account name ChewDavid.</p>

	</div>
	<h3 id="example-4-get-a-specified-user" data-chunk-ids="example-4-get-a-specified-user">Example 4: Get a specified user</h3>
	<div data-chunk-ids="example-4-get-a-specified-user">
		<pre><code class="lang-powershell">PS C:\&gt; Get-ADUser -Filter "Name -eq 'ChewDavid'" -SearchBase "DC=AppNC" -Properties "mail" -Server lds.Fabrikam.com:50000
</code></pre>
<p>This command gets the user with the name ChewDavid in the Active Directory Lightweight Directory Services (AD LDS) instance.</p>

	</div>
	<h3 id="example-5-get-all-enabled-user-accounts" data-chunk-ids="example-5-get-all-enabled-user-accounts">Example 5: Get all enabled user accounts</h3>
	<div data-chunk-ids="example-5-get-all-enabled-user-accounts">
		<pre><code class="lang-powershell">C:\PS&gt; Get-ADUser -LDAPFilter '(!userAccountControl:1.2.840.113556.1.4.803:=2)'
</code></pre>
<p>This command gets all enabled user accounts in Active Directory using an LDAP filter.</p>

	</div>

	<h2 id="parameters" data-chunk-ids="authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server">Parameters</h2>
		<h3 id="-authtype" data-chunk-ids="authtype" class="font-family-monospace margin-top-lg margin-bottom-md">-Auth<wbr>Type</h3>
		<p>Specifies the authentication method to use.
The acceptable values for this parameter are:</p>
<ul>
<li>Negotiate or 0</li>
<li>Basic or 1</li>
</ul>
<p>The default authentication method is Negotiate.</p>
<p>A Secure Sockets Layer (SSL) connection is required for the Basic authentication method.</p>


		<h4 id="authtype-properties" data-chunk-ids="authtype">Parameter properties</h4>
		<table data-chunk-ids="authtype" class="table">
				<tbody><tr><td>Type:</td><td><span class="no-loc xref">ADAuthType</span>
</td></tr>
				<tr><td>Default value:</td><td>None</td></tr>
				<tr><td>Accepted values:</td><td>Negotiate, Basic</td></tr>
				<tr><td>Supports wildcards:</td><td>False</td></tr>
				<tr><td>DontShow:</td><td>False</td></tr>
		</tbody></table>

		<h4 id="authtype-sets" data-chunk-ids="authtype">Parameter sets</h4>
			<details class="margin-top-sm" data-chunk-ids="authtype" open="">
				<summary class="list-style-none link-button">
					(All) 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
		<h3 id="-credential" data-chunk-ids="credential" class="font-family-monospace margin-top-lg margin-bottom-md">-Credential</h3>
		<p>Specifies the user account credentials to use to perform this task.
The default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive.
If the cmdlet is run from such a provider drive, the account associated with the drive is the default.</p>
<p>To specify this parameter, you can type a user name, such as User1 or Domain01\User01 or you can specify a <strong>PSCredential</strong> object.
If you specify a user name for this parameter, the cmdlet prompts for a password.</p>
<p>You can also create a <strong>PSCredential</strong> object by using a script or by using the <strong>Get-Credential</strong> cmdlet.
You can then set the <em>Credential</em> parameter to the <strong>PSCredential</strong> object.</p>
<p>If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error.</p>


		<h4 id="credential-properties" data-chunk-ids="credential">Parameter properties</h4>
		<table data-chunk-ids="credential" class="table">
				<tbody><tr><td>Type:</td><td><span class="no-loc xref">PSCredential</span>
</td></tr>
				<tr><td>Default value:</td><td>None</td></tr>
				<tr><td>Supports wildcards:</td><td>False</td></tr>
				<tr><td>DontShow:</td><td>False</td></tr>
		</tbody></table>

		<h4 id="credential-sets" data-chunk-ids="credential">Parameter sets</h4>
			<details class="margin-top-sm" data-chunk-ids="credential" open="">
				<summary class="list-style-none link-button">
					(All) 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
		<h3 id="-filter" data-chunk-ids="filter" class="font-family-monospace margin-top-lg margin-bottom-md">-Filter</h3>
		<p>Specifies a query string that retrieves Active Directory objects.
This string uses the PowerShell Expression Language syntax.
The PowerShell Expression Language syntax provides rich type-conversion support for value types received by the <em>Filter</em> parameter.
The syntax uses an in-order representation, which means that the operator is placed between the operand and the value.
For more information about the <em>Filter</em> parameter, type <code>Get-Help about_ActiveDirectory_Filter</code>.</p>
<p>Syntax:</p>
<p>The following syntax uses Backus-Naur form to show how to use the PowerShell Expression Language for this parameter.</p>
<p>&lt;filter&gt;  ::= "{" &lt;FilterComponentList&gt; "}"</p>
<p>&lt;FilterComponentList&gt; ::= &lt;FilterComponent&gt; | &lt;FilterComponent&gt; &lt;JoinOperator&gt; &lt;FilterComponent&gt; | &lt;NotOperator&gt;  &lt;FilterComponent&gt;</p>
<p>&lt;FilterComponent&gt; ::= &lt;attr&gt; &lt;FilterOperator&gt; &lt;value&gt; | "(" &lt;FilterComponent&gt; ")"</p>
<p>&lt;FilterOperator&gt; ::= "-eq" | "-le" | "-ge" | "-ne" | "-lt" | "-gt"| "-approx" | "-bor" | "-band" | "-recursivematch" | "-like" | "-notlike"</p>
<p>&lt;JoinOperator&gt; ::= "-and" | "-or"</p>
<p>&lt;NotOperator&gt; ::= "-not"</p>
<p>&lt;attr&gt; ::= &lt;PropertyName&gt; | &lt;LDAPDisplayName of the attribute&gt;</p>
<p>&lt;value&gt;::= &lt;compare this value with an &lt;attr&gt; by using the specified &lt;FilterOperator&gt;&gt;</p>
<p>For a list of supported types for &lt;value&gt;, type <code>Get-Help about_ActiveDirectory_ObjectModel</code>.</p>
<p>Note: For String parameter type, PowerShell will cast the filter query to a string while processing the command. When using a string variable as a value in the filter component, make sure that it complies with the <a href="/en-us/powershell/module/microsoft.powershell.core/about/about_quoting_rules" data-linktype="absolute-path">PowerShell Quoting Rules</a>. For example, if the filter expression is double-quoted, the variable should be enclosed using single quotation marks:
<strong>Get-ADUser -Filter "Name -like '$UserName'"</strong>. On the contrary, if curly braces are used to enclose the filter, the variable should not be quoted at all: <strong>Get-ADUser -Filter {Name -like $UserName}</strong>.</p>
<p>Note: PowerShell wildcards other than *, such as ?, are not supported by the <em>Filter</em> syntax.</p>
<p>Note: To query using LDAP query strings, use the <em>LDAPFilter</em> parameter.</p>


		<h4 id="filter-properties" data-chunk-ids="filter">Parameter properties</h4>
		<table data-chunk-ids="filter" class="table">
				<tbody><tr><td>Type:</td><td><span class="no-loc xref">String</span>
</td></tr>
				<tr><td>Default value:</td><td>None</td></tr>
				<tr><td>Supports wildcards:</td><td>False</td></tr>
				<tr><td>DontShow:</td><td>False</td></tr>
		</tbody></table>

		<h4 id="filter-sets" data-chunk-ids="filter">Parameter sets</h4>
			<details class="margin-top-sm" data-chunk-ids="filter" open="">
				<summary class="list-style-none link-button">
					Filter 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>True</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
		<h3 id="-identity" data-chunk-ids="identity" class="font-family-monospace margin-top-lg margin-bottom-md">-Identity</h3>
		<p>Specifies an Active Directory user object by providing one of the following property values.
The identifier in parentheses is the LDAP display name for the attribute.
The acceptable values for this parameter are:</p>
<ul>
<li>A distinguished name</li>
<li>A GUID (objectGUID)</li>
<li>A security identifier (objectSid)</li>
<li>A SAM account name (sAMAccountName)</li>
</ul>
<p>The cmdlet searches the default naming context or partition to find the object.
If two or more objects are found, the cmdlet returns a non-terminating error.</p>
<p>This parameter can also get this object through the pipeline or you can set this parameter to an object instance.</p>


		<h4 id="identity-properties" data-chunk-ids="identity">Parameter properties</h4>
		<table data-chunk-ids="identity" class="table">
				<tbody><tr><td>Type:</td><td><span class="no-loc xref">ADUser</span>
</td></tr>
				<tr><td>Default value:</td><td>None</td></tr>
				<tr><td>Supports wildcards:</td><td>False</td></tr>
				<tr><td>DontShow:</td><td>False</td></tr>
		</tbody></table>

		<h4 id="identity-sets" data-chunk-ids="identity">Parameter sets</h4>
			<details class="margin-top-sm" data-chunk-ids="identity" open="">
				<summary class="list-style-none link-button">
					Identity 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>0</td></tr>
						<tr><td>Mandatory:</td><td>True</td></tr>
						<tr><td>Value from pipeline:</td><td>True</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
		<h3 id="-ldapfilter" data-chunk-ids="ldapfilter" class="font-family-monospace margin-top-lg margin-bottom-md">-LDAPFilter</h3>
		<p>Specifies an LDAP query string that is used to filter Active Directory objects.
You can use this parameter to run your existing LDAP queries.
The <em>Filter</em> parameter syntax supports the same functionality as the LDAP syntax.
For more information, see the <em>Filter</em> parameter description or type <code>Get-Help about_ActiveDirectory_Filter</code>.</p>


		<h4 id="ldapfilter-properties" data-chunk-ids="ldapfilter">Parameter properties</h4>
		<table data-chunk-ids="ldapfilter" class="table">
				<tbody><tr><td>Type:</td><td><span class="no-loc xref">String</span>
</td></tr>
				<tr><td>Default value:</td><td>None</td></tr>
				<tr><td>Supports wildcards:</td><td>False</td></tr>
				<tr><td>DontShow:</td><td>False</td></tr>
		</tbody></table>

		<h4 id="ldapfilter-sets" data-chunk-ids="ldapfilter">Parameter sets</h4>
			<details class="margin-top-sm" data-chunk-ids="ldapfilter" open="">
				<summary class="list-style-none link-button">
					Ldap<wbr>Filter 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>True</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
		<h3 id="-partition" data-chunk-ids="partition" class="font-family-monospace margin-top-lg margin-bottom-md">-Partition</h3>
		<p>Specifies the distinguished name of an Active Directory partition.
The distinguished name must be one of the naming contexts on the current directory server.
The cmdlet searches this partition to find the object defined by the <em>Identity</em> parameter.</p>
<p>In many cases, a default value is used for the <em>Partition</em> parameter if no value is specified.
The rules for determining the default value are given below.
Note that rules listed first are evaluated first, and when a default value can be determined, no further rules are evaluated.</p>
<p>In AD DS environments, a default value for <em>Partition</em> is set in the following cases:</p>
<ul>
<li>If the <em>Identity</em> parameter is set to a distinguished name, the default value of <em>Partition</em> is automatically generated from this distinguished name.</li>
<li>If running cmdlets from an Active Directory provider drive, the default value of <em>Partition</em> is automatically generated from the current path in the drive.</li>
<li>If none of the previous cases apply, the default value of <em>Partition</em> is set to the default partition or naming context of the target domain.</li>
</ul>
<p>In AD LDS environments, a default value for <em>Partition</em> is set in the following cases:</p>
<ul>
<li>If the <em>Identity</em> parameter is set to a distinguished name, the default value of <em>Partition</em> is automatically generated from this distinguished name.</li>
<li>If running cmdlets from an Active Directory provider drive, the default value of <em>Partition</em> is automatically generated from the current path in the drive.</li>
<li>If the target AD LDS instance has a default naming context, the default value of <em>Partition</em> is set to the default naming context.
To specify a default naming context for an AD LDS environment, set the <strong>msDS-defaultNamingContext</strong> property of the Active Directory directory service agent object (<strong>nTDSDSA</strong>) for the AD LDS instance.</li>
<li>If none of the previous cases apply, the <em>Partition</em> parameter does not take any default value.</li>
</ul>


		<h4 id="partition-properties" data-chunk-ids="partition">Parameter properties</h4>
		<table data-chunk-ids="partition" class="table">
				<tbody><tr><td>Type:</td><td><span class="no-loc xref">String</span>
</td></tr>
				<tr><td>Default value:</td><td>None</td></tr>
				<tr><td>Supports wildcards:</td><td>False</td></tr>
				<tr><td>DontShow:</td><td>False</td></tr>
		</tbody></table>

		<h4 id="partition-sets" data-chunk-ids="partition">Parameter sets</h4>
			<details class="margin-top-sm" data-chunk-ids="partition" open="">
				<summary class="list-style-none link-button">
					Identity 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
		<h3 id="-properties" data-chunk-ids="properties" class="font-family-monospace margin-top-lg margin-bottom-md">-Properties</h3>
		<p>Specifies the properties of the output object to retrieve from the server.
Use this parameter to retrieve properties that are not included in the default set.</p>
<p>Specify properties for this parameter as a comma-separated list of names.
To display all of the attributes that are set on the object, specify * (asterisk).</p>
<p>To specify an individual extended property, use the name of the property.
For properties that are not default or extended properties, you must specify the LDAP display name of the attribute.</p>
<p>To retrieve properties and display them for an object, you can use the Get-* cmdlet associated with the object and pass the output to the <strong>Get-Member</strong> cmdlet.</p>


		<h4 id="properties-properties" data-chunk-ids="properties">Parameter properties</h4>
		<table data-chunk-ids="properties" class="table">
				<tbody><tr><td>Type:</td><td><p><span class="no-loc xref">String</span><span>[</span><span>]</span></p>
</td></tr>
				<tr><td>Default value:</td><td>None</td></tr>
				<tr><td>Supports wildcards:</td><td>False</td></tr>
				<tr><td>DontShow:</td><td>False</td></tr>
				<tr><td>Aliases:</td><td>Property</td></tr>
		</tbody></table>

		<h4 id="properties-sets" data-chunk-ids="properties">Parameter sets</h4>
			<details class="margin-top-sm" data-chunk-ids="properties" open="">
				<summary class="list-style-none link-button">
					(All) 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
		<h3 id="-resultpagesize" data-chunk-ids="resultpagesize" class="font-family-monospace margin-top-lg margin-bottom-md">-Result<wbr>Page<wbr>Size</h3>
		<p>Specifies the number of objects to include in one page for an Active Directory Domain Services query.</p>
<p>The default is 256 objects per page.</p>


		<h4 id="resultpagesize-properties" data-chunk-ids="resultpagesize">Parameter properties</h4>
		<table data-chunk-ids="resultpagesize" class="table">
				<tbody><tr><td>Type:</td><td><span class="no-loc xref">Int32</span>
</td></tr>
				<tr><td>Default value:</td><td>None</td></tr>
				<tr><td>Supports wildcards:</td><td>False</td></tr>
				<tr><td>DontShow:</td><td>False</td></tr>
		</tbody></table>

		<h4 id="resultpagesize-sets" data-chunk-ids="resultpagesize">Parameter sets</h4>
			<details class="margin-top-sm" data-chunk-ids="resultpagesize" open="">
				<summary class="list-style-none link-button">
					Filter 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
			<details class="margin-top-sm" data-chunk-ids="resultpagesize">
				<summary class="list-style-none link-button">
					Ldap<wbr>Filter 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
		<h3 id="-resultsetsize" data-chunk-ids="resultsetsize" class="font-family-monospace margin-top-lg margin-bottom-md">-Result<wbr>Set<wbr>Size</h3>
		<p>Specifies the maximum number of objects to return for an Active Directory Domain Services query.
If you want to receive all of the objects, set this parameter to $Null (null value).
You can use Ctrl+C to stop the query and return of objects.</p>
<p>The default is $Null.</p>


		<h4 id="resultsetsize-properties" data-chunk-ids="resultsetsize">Parameter properties</h4>
		<table data-chunk-ids="resultsetsize" class="table">
				<tbody><tr><td>Type:</td><td><span class="no-loc xref">Int32</span>
</td></tr>
				<tr><td>Default value:</td><td>None</td></tr>
				<tr><td>Supports wildcards:</td><td>False</td></tr>
				<tr><td>DontShow:</td><td>False</td></tr>
		</tbody></table>

		<h4 id="resultsetsize-sets" data-chunk-ids="resultsetsize">Parameter sets</h4>
			<details class="margin-top-sm" data-chunk-ids="resultsetsize" open="">
				<summary class="list-style-none link-button">
					Filter 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
			<details class="margin-top-sm" data-chunk-ids="resultsetsize">
				<summary class="list-style-none link-button">
					Ldap<wbr>Filter 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
		<h3 id="-searchbase" data-chunk-ids="searchbase" class="font-family-monospace margin-top-lg margin-bottom-md">-Search<wbr>Base</h3>
		<p>Specifies an Active Directory path to search under.</p>
<p>When you run a cmdlet from an Active Directory provider drive, the default value of this parameter is the current path of the drive.</p>
<p>When you run a cmdlet outside of an Active Directory provider drive against an AD DS target, the default value of this parameter is the default naming context of the target domain.</p>
<p>When you run a cmdlet outside of an Active Directory provider drive against an AD LDS target, the default value is the default naming context of the target LDS instance if one has been specified by setting the <strong>msDS-defaultNamingContext</strong> property of the Active Directory directory service agent (DSA) object (<strong>nTDSDSA</strong>) for the AD LDS instance.
If no default naming context has been specified for the target AD LDS instance, then this parameter has no default value.</p>
<p>When the value of the <em>SearchBase</em> parameter is set to an empty string and you are connected to a GC port, all partitions are searched.
If the value of the <em>SearchBase</em> parameter is set to an empty string and you are not connected to a GC port, an error is thrown.</p>


		<h4 id="searchbase-properties" data-chunk-ids="searchbase">Parameter properties</h4>
		<table data-chunk-ids="searchbase" class="table">
				<tbody><tr><td>Type:</td><td><span class="no-loc xref">String</span>
</td></tr>
				<tr><td>Default value:</td><td>None</td></tr>
				<tr><td>Supports wildcards:</td><td>False</td></tr>
				<tr><td>DontShow:</td><td>False</td></tr>
		</tbody></table>

		<h4 id="searchbase-sets" data-chunk-ids="searchbase">Parameter sets</h4>
			<details class="margin-top-sm" data-chunk-ids="searchbase" open="">
				<summary class="list-style-none link-button">
					Filter 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
			<details class="margin-top-sm" data-chunk-ids="searchbase">
				<summary class="list-style-none link-button">
					Ldap<wbr>Filter 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
		<h3 id="-searchscope" data-chunk-ids="searchscope" class="font-family-monospace margin-top-lg margin-bottom-md">-Search<wbr>Scope</h3>
		<p>Specifies the scope of an Active Directory search.
The acceptable values for this parameter are:</p>
<ul>
<li>Base or 0</li>
<li>OneLevel or 1</li>
<li>Subtree or 2</li>
</ul>
<p>A SearchScope with a Base value searches only for the given user. If an OU is specified in the SearchBase parameter, no user will be returned by, for example, a specified Filter statement.
A OneLevel query searches the immediate children of that path or object. This option only works when an OU is given as the SearchBase. If a user is given, no results are returned.
A Subtree query searches the current path or object and all children of that path or object.</p>


		<h4 id="searchscope-properties" data-chunk-ids="searchscope">Parameter properties</h4>
		<table data-chunk-ids="searchscope" class="table">
				<tbody><tr><td>Type:</td><td><span class="no-loc xref">ADSearchScope</span>
</td></tr>
				<tr><td>Default value:</td><td>None</td></tr>
				<tr><td>Accepted values:</td><td>Base, OneLevel, Subtree</td></tr>
				<tr><td>Supports wildcards:</td><td>False</td></tr>
				<tr><td>DontShow:</td><td>False</td></tr>
		</tbody></table>

		<h4 id="searchscope-sets" data-chunk-ids="searchscope">Parameter sets</h4>
			<details class="margin-top-sm" data-chunk-ids="searchscope" open="">
				<summary class="list-style-none link-button">
					Filter 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
			<details class="margin-top-sm" data-chunk-ids="searchscope">
				<summary class="list-style-none link-button">
					Ldap<wbr>Filter 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
		<h3 id="-server" data-chunk-ids="server" class="font-family-monospace margin-top-lg margin-bottom-md">-Server</h3>
		<p>Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server.
The service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory Snapshot instance.</p>
<p>Domain name values:</p>
<ul>
<li>Fully qualified domain name (FQDN)</li>
<li>NetBIOS name</li>
</ul>
<p>Directory server values:</p>
<ul>
<li>Fully qualified directory server name</li>
<li>NetBIOS name</li>
<li>Fully qualified directory server name and port</li>
</ul>
<p>The default value for the <em>Server</em> parameter is determined by one of the following methods in the order that they are listed:</p>
<ul>
<li>By using <em>Server</em> value from objects passed through the pipeline.</li>
<li>By using the server information associated with the Active Directory PowerShell provider drive, when running under that drive.</li>
<li>By using the domain of the computer running PowerShell.</li>
</ul>


		<h4 id="server-properties" data-chunk-ids="server">Parameter properties</h4>
		<table data-chunk-ids="server" class="table">
				<tbody><tr><td>Type:</td><td><span class="no-loc xref">String</span>
</td></tr>
				<tr><td>Default value:</td><td>None</td></tr>
				<tr><td>Supports wildcards:</td><td>False</td></tr>
				<tr><td>DontShow:</td><td>False</td></tr>
		</tbody></table>

		<h4 id="server-sets" data-chunk-ids="server">Parameter sets</h4>
			<details class="margin-top-sm" data-chunk-ids="server" open="">
				<summary class="list-style-none link-button">
					(All) 
					<span class="icon expanded-indicator" aria-hidden="true">
						<span class="docon docon-chevron-down-light"></span>
					</span>
				</summary>
				<table class="table">
						<tbody><tr><td>Position:</td><td>Named</td></tr>
						<tr><td>Mandatory:</td><td>False</td></tr>
						<tr><td>Value from pipeline:</td><td>False</td></tr>
						<tr><td>Value from pipeline by property name:</td><td>False</td></tr>
						<tr><td>Value from remaining arguments:</td><td>False</td></tr>
				</tbody></table>
			</details>
		<h3 id="common-parameters" data-no-chunk="">CommonParameters</h3>
		<div data-no-chunk="">
			<p>This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable,
-InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable,
-ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see
<a href="https://go.microsoft.com/fwlink/?LinkID=113216" data-linktype="external">about_CommonParameters</a>.</p>

		</div>

	<h2 id="inputs" data-chunk-ids="inputs">Inputs</h2>
			<h3 id="input-1" data-chunk-ids="inputs" class="break-text font-size-xl"><span class="no-loc xref">None or Microsoft.ActiveDirectory.Management.ADUser</span>
</h3>
			<div data-chunk-ids="inputs">
				<p>A user object is received by the <em>Identity</em> parameter.</p>

			</div>

	<h2 id="outputs" data-chunk-ids="outputs">Outputs</h2>
			<h3 id="output-1" data-chunk-ids="outputs" class="break-text font-size-xl"><span class="no-loc xref">Microsoft.ActiveDirectory.Management.ADUser</span>
</h3>
			<div data-chunk-ids="outputs">
				<p>Returns one or more user objects.</p>
<p>This cmdlet returns a default set of <strong>ADUser</strong> property values.
To retrieve additional <strong>ADUser</strong> properties, use the <em>Properties</em> parameter.</p>
<p>To get a list of the default set of properties of an <strong>ADUser</strong> object, use the following command:</p>
<p><code>Get-ADUser</code>&lt;user&gt;<code>| Get-Member</code></p>
<p>To get a list of the most commonly used properties of an ADUser object, use the following command:</p>
<p><code>Get-ADUser</code>&lt;user&gt;<code>-Properties Extended | Get-Member</code></p>
<p>To get a list of all the properties of an <strong>ADUser</strong> object, use the following command:</p>
<p><code>Get-ADUser</code>&lt;user&gt;<code>-Properties * | Get-Member</code></p>

			</div>

	<h2 id="notes" data-chunk-ids="inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server">Notes</h2>
	<div data-chunk-ids="inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server">
		<ul>
<li>This cmdlet does not work with an Active Directory snapshot.</li>
</ul>

	</div>

	<h2 id="related-links" data-no-chunk="">Related Links</h2>
	<ul data-no-chunk="">
			<li><a href="new-aduser?view=windowsserver2025-ps" data-linktype="relative-path">New-ADUser</a></li>
			<li><a href="remove-aduser?view=windowsserver2025-ps" data-linktype="relative-path">Remove-ADUser</a></li>
			<li><a href="set-aduser?view=windowsserver2025-ps" data-linktype="relative-path">Set-ADUser</a></li>
	</ul>
</div>
					
		<div
			id="ms--inline-notifications"
			class="margin-block-xs"
			data-bi-name="inline-notification"
		></div>
	 
		<div
			id="assertive-live-region"
			role="alert"
			aria-live="assertive"
			class="visually-hidden"
			aria-relevant="additions"
			aria-atomic="true"
		></div>
		<div
			id="polite-live-region"
			role="status"
			aria-live="polite"
			class="visually-hidden"
			aria-relevant="additions"
			aria-atomic="true"
		></div>
	
					
		<!-- feedback section -->
		<section
			id="site-user-feedback-footer"
			class="font-size-sm margin-top-md display-none-print display-none-desktop"
			data-test-id="site-user-feedback-footer"
			data-bi-name="site-feedback-section"
		>
			<hr class="hr" />
			<h2 id="ms--feedback" class="title is-3">Feedback</h2>
			<div class="display-flex flex-wrap-wrap align-items-center">
				<p class="font-weight-semibold margin-xxs margin-left-none">
					Was this page helpful?
				</p>
				<div class="buttons">
					<button
						class="thumb-rating-button like button button-primary button-sm"
						data-test-id="footer-rating-yes"
						data-binary-rating-response="rating-yes"
						type="button"
						title="This article is helpful"
						data-bi-name="button-rating-yes"
						aria-pressed="false"
					>
						<span class="icon" aria-hidden="true">
							<span class="docon docon-like"></span>
						</span>
						<span>Yes</span>
					</button>
					<button
						class="thumb-rating-button dislike button button-primary button-sm"
						data-test-id="footer-rating-no"
						data-binary-rating-response="rating-no"
						type="button"
						title="This article is not helpful"
						data-bi-name="button-rating-no"
						aria-pressed="false"
					>
						<span class="icon" aria-hidden="true">
							<span class="docon docon-dislike"></span>
						</span>
						<span>No</span>
					</button>
				</div>
			</div>
		</section>
		<!-- end feedback section -->
	
				</div>
				
			</div>
			
		<div
			id="action-panel"
			role="region"
			aria-label="Action Panel"
			class="action-panel"
			tabindex="-1"
		></div>
	
		
				</main>
				<aside
					id="layout-body-aside"
					class="layout-body-aside "
					data-bi-name="aside"
			  >
					
		<div
			id="ms--additional-resources"
			class="right-container padding-sm display-none display-block-desktop height-full"
			data-bi-name="pageactions"
			role="complementary"
			aria-label="Additional resources"
		>
			<div id="affixed-right-container" data-bi-name="right-column">
				
		<nav
			id="side-doc-outline"
			class="doc-outline border-bottom padding-bottom-xs margin-bottom-xs"
			data-bi-name="intopic toc"
			aria-label="In this article"
		>
			<h3>In this article</h3>
		</nav>
	
				<!-- Feedback -->
				
		<section
			id="ms--site-user-feedback-right-rail"
			class="font-size-sm display-none-print"
			data-test-id="site-user-feedback-right-rail"
			data-bi-name="site-feedback-right-rail"
		>
			<p class="font-weight-semibold margin-bottom-xs">Was this page helpful?</p>
			<div class="buttons">
				<button
					class="thumb-rating-button like button button-primary button-sm"
					data-test-id="right-rail-rating-yes"
					data-binary-rating-response="rating-yes"
					type="button"
					title="This article is helpful"
					data-bi-name="button-rating-yes"
					aria-pressed="false"
				>
					<span class="icon" aria-hidden="true">
						<span class="docon docon-like"></span>
					</span>
					<span>Yes</span>
				</button>
				<button
					class="thumb-rating-button dislike button button-primary button-sm"
					data-test-id="right-rail-rating-no"
					data-binary-rating-response="rating-no"
					type="button"
					title="This article is not helpful"
					data-bi-name="button-rating-no"
					aria-pressed="false"
				>
					<span class="icon" aria-hidden="true">
						<span class="docon docon-dislike"></span>
					</span>
					<span>No</span>
				</button>
			</div>
		</section>
	
			</div>
		</div>
	
			  </aside> <section
					id="layout-body-flyout"
					class="layout-body-flyout "
					data-bi-name="flyout"
			  >
					 <div
	class="height-full border-left background-color-body-medium"
	id="ask-learn-flyout"
></div>
			  </section> <div class="layout-body-footer " data-bi-name="layout-footer">
		<footer
			id="footer"
			data-test-id="footer"
			data-bi-name="footer"
			class="footer-layout has-padding has-default-focus border-top  uhf-container"
			role="contentinfo"
		>
			<div class="display-flex gap-xs flex-wrap-wrap is-full-height padding-right-lg-desktop">
				
		<a
			data-mscc-ic="false"
			href="#"
			data-bi-name="select-locale"
			class="locale-selector-link flex-shrink-0 button button-sm button-clear external-link-indicator"
			id=""
			title=""
			><span class="icon" aria-hidden="true"
				><span class="docon docon-world"></span></span
			><span class="local-selector-link-text">en-us</span></a
		>
	
				<div class="ccpa-privacy-link" data-ccpa-privacy-link hidden>
		
		<a
			data-mscc-ic="false"
			href="https://aka.ms/yourcaliforniaprivacychoices"
			data-bi-name="your-privacy-choices"
			class="button button-sm button-clear flex-shrink-0 external-link-indicator"
			id=""
			title=""
			>
		<svg
			xmlns="http://www.w3.org/2000/svg"
			viewBox="0 0 30 14"
			xml:space="preserve"
			height="16"
			width="43"
			aria-hidden="true"
			focusable="false"
		>
			<path
				d="M7.4 12.8h6.8l3.1-11.6H7.4C4.2 1.2 1.6 3.8 1.6 7s2.6 5.8 5.8 5.8z"
				style="fill-rule:evenodd;clip-rule:evenodd;fill:#fff"
			></path>
			<path
				d="M22.6 0H7.4c-3.9 0-7 3.1-7 7s3.1 7 7 7h15.2c3.9 0 7-3.1 7-7s-3.2-7-7-7zm-21 7c0-3.2 2.6-5.8 5.8-5.8h9.9l-3.1 11.6H7.4c-3.2 0-5.8-2.6-5.8-5.8z"
				style="fill-rule:evenodd;clip-rule:evenodd;fill:#06f"
			></path>
			<path
				d="M24.6 4c.******* 0 .8L22.5 7l2.2 2.2c.******* 0 .8-.2.2-.6.2-.8 0l-2.2-2.2-2.2 2.2c-.2.2-.6.2-.8 0-.2-.2-.2-.6 0-.8L20.8 7l-2.2-2.2c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0l2.2 2.2L23.8 4c.2-.2.6-.2.8 0z"
				style="fill:#fff"
			></path>
			<path
				d="M12.7 4.1c.*******.1.8L8.6 9.8c-.1.1-.2.2-.3.2-.2.1-.5.1-.7-.1L5.4 7.7c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0L8 8.6l3.8-4.5c.2-.2.6-.2.9 0z"
				style="fill:#06f"
			></path>
		</svg>
	
			<span>Your Privacy Choices</span></a
		>
	
	</div>
				<div class="flex-shrink-0">
		<div class="dropdown has-caret-up">
			<button
				data-test-id="theme-selector-button"
				class="dropdown-trigger button button-clear button-sm has-inner-focus theme-dropdown-trigger"
				aria-controls="{{ themeMenuId }}"
				aria-expanded="false"
				title="Theme"
				data-bi-name="theme"
			>
				<span class="icon">
					<span class="docon docon-sun" aria-hidden="true"></span>
				</span>
				<span>Theme</span>
				<span class="icon expanded-indicator" aria-hidden="true">
					<span class="docon docon-chevron-down-light"></span>
				</span>
			</button>
			<div class="dropdown-menu" id="{{ themeMenuId }}" role="menu">
				<ul class="theme-selector padding-xxs" data-test-id="theme-dropdown-menu">
					<li class="theme display-block">
						<button
							class="button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left"
							data-theme-to="light"
						>
							<span class="theme-light margin-right-xxs">
								<span
									class="theme-selector-icon border display-inline-block has-body-background"
									aria-hidden="true"
								>
									<svg class="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 14">
										<rect width="22" height="14" class="has-fill-body-background" />
										<rect x="5" y="5" width="12" height="4" class="has-fill-secondary" />
										<rect x="5" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="8" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="11" y="2" width="3" height="1" class="has-fill-secondary" />
										<rect x="1" y="1" width="2" height="2" class="has-fill-secondary" />
										<rect x="5" y="10" width="7" height="2" rx="0.3" class="has-fill-primary" />
										<rect x="19" y="1" width="2" height="2" rx="1" class="has-fill-secondary" />
									</svg>
								</span>
							</span>
							<span role="menuitem"> Light </span>
						</button>
					</li>
					<li class="theme display-block">
						<button
							class="button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left"
							data-theme-to="dark"
						>
							<span class="theme-dark margin-right-xxs">
								<span
									class="border theme-selector-icon display-inline-block has-body-background"
									aria-hidden="true"
								>
									<svg class="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 14">
										<rect width="22" height="14" class="has-fill-body-background" />
										<rect x="5" y="5" width="12" height="4" class="has-fill-secondary" />
										<rect x="5" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="8" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="11" y="2" width="3" height="1" class="has-fill-secondary" />
										<rect x="1" y="1" width="2" height="2" class="has-fill-secondary" />
										<rect x="5" y="10" width="7" height="2" rx="0.3" class="has-fill-primary" />
										<rect x="19" y="1" width="2" height="2" rx="1" class="has-fill-secondary" />
									</svg>
								</span>
							</span>
							<span role="menuitem"> Dark </span>
						</button>
					</li>
					<li class="theme display-block">
						<button
							class="button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left"
							data-theme-to="high-contrast"
						>
							<span class="theme-high-contrast margin-right-xxs">
								<span
									class="border theme-selector-icon display-inline-block has-body-background"
									aria-hidden="true"
								>
									<svg class="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 14">
										<rect width="22" height="14" class="has-fill-body-background" />
										<rect x="5" y="5" width="12" height="4" class="has-fill-secondary" />
										<rect x="5" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="8" y="2" width="2" height="1" class="has-fill-secondary" />
										<rect x="11" y="2" width="3" height="1" class="has-fill-secondary" />
										<rect x="1" y="1" width="2" height="2" class="has-fill-secondary" />
										<rect x="5" y="10" width="7" height="2" rx="0.3" class="has-fill-primary" />
										<rect x="19" y="1" width="2" height="2" rx="1" class="has-fill-secondary" />
									</svg>
								</span>
							</span>
							<span role="menuitem"> High contrast </span>
						</button>
					</li>
				</ul>
			</div>
		</div>
	</div>
			</div>
			<ul class="links" data-bi-name="footerlinks">
				<li class="manage-cookies-holder" hidden=""></li>
				<li>
		
		<a
			data-mscc-ic="false"
			href="https://learn.microsoft.com/en-us/principles-for-ai-generated-content"
			data-bi-name="aiDisclaimer"
			class=" external-link-indicator"
			id=""
			title=""
			>AI Disclaimer</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://learn.microsoft.com/en-us/previous-versions/"
			data-bi-name="archivelink"
			class=" external-link-indicator"
			id=""
			title=""
			>Previous Versions</a
		>
	
	</li> <li>
		
		<a
			data-mscc-ic="false"
			href="https://techcommunity.microsoft.com/t5/microsoft-learn-blog/bg-p/MicrosoftLearnBlog"
			data-bi-name="bloglink"
			class=" external-link-indicator"
			id=""
			title=""
			>Blog</a
		>
	
	</li> <li>
		
		<a
			data-mscc-ic="false"
			href="https://learn.microsoft.com/en-us/contribute"
			data-bi-name="contributorGuide"
			class=" external-link-indicator"
			id=""
			title=""
			>Contribute</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://go.microsoft.com/fwlink/?LinkId=521839"
			data-bi-name="privacy"
			class=" external-link-indicator"
			id=""
			title=""
			>Privacy</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://learn.microsoft.com/en-us/legal/termsofuse"
			data-bi-name="termsofuse"
			class=" external-link-indicator"
			id=""
			title=""
			>Terms of Use</a
		>
	
	</li><li>
		
		<a
			data-mscc-ic="false"
			href="https://www.microsoft.com/legal/intellectualproperty/Trademarks/"
			data-bi-name="trademarks"
			class=" external-link-indicator"
			id=""
			title=""
			>Trademarks</a
		>
	
	</li>
				<li>&copy; Microsoft 2025</li>
			</ul>
		</footer>
	</footer>
			</body>
		</html>
