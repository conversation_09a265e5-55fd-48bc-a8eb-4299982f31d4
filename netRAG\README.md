# NetRAG - PowerShell Command RAG Service

A Retrieval-Augmented Generation (RAG) service for PowerShell commands that provides semantic search capabilities for enhanced command discovery and execution.

## Project Structure

```
netRAG/
├── NetRagService/          # Main RAG service
│   ├── Program.cs          # Service entry point
│   ├── VectorStoreService.cs
│   ├── PowerShellCommandSearchService.cs
│   ├── Controllers/        # API controllers
│   └── NetRagService.csproj
├── DatabaseConverter/      # Standalone database converter utility
│   ├── Program.cs          # Converter entry point
│   ├── README.md           # Converter documentation
│   └── DatabaseConverter.csproj
├── NetRAG.sln             # Solution file
└── README.md              # This file
```

## Components

### NetRagService
The main service that provides:
- **PowerShell Command Search API** - Semantic search for PowerShell commands
- **Vector Store Management** - Read-only access to pre-computed embeddings
- **MCP Tool Integration** - Integration with Microsoft Copilot Platform tools
- **Health Monitoring** - Service health and statistics endpoints

**Key Features:**
- ✅ **Read-only operation** - No data modification during service runtime
- ✅ **Fast startup** - Loads pre-computed database.bin files
- ✅ **Semantic search** - BERT-based embedding similarity search
- ✅ **Rich metadata** - Command parameters, examples, and usage information

### DatabaseConverter
A standalone utility for converting PowerShell command JSON data to binary database files.

**Purpose:**
- **One-time conversion** - Process JSON data into optimized binary format
- **Offline processing** - Generate embeddings without affecting service performance
- **Data preparation** - Create database.bin files for service consumption

## Building

### Build Everything
```bash
dotnet build
```

### Build Individual Projects
```bash
# Build the main service
dotnet build NetRagService/NetRagService.csproj

# Build the database converter
dotnet build DatabaseConverter/DatabaseConverter.csproj
```

## Usage

### 1. Create Database File (One-time)
```bash
cd DatabaseConverter
dotnet run microsoft_learn_data.json database.bin
```

### 2. Run the Service
```bash
cd NetRagService
dotnet run --console
# or
dotnet run --service
```

### 3. Test the API
```bash
# Health check
curl http://localhost:5000/api/mcptools/statistics

# Search PowerShell commands
curl "http://localhost:5000/api/powershellcommand/search?query=get%20user&limit=5"
```

## API Endpoints

### PowerShell Command Search
- **GET** `/api/powershellcommand/search`
  - `query` (string) - Search query
  - `limit` (int) - Maximum results (default: 5, max: 20)

### Service Management
- **GET** `/api/mcptools/statistics` - Service health and statistics
- **GET** `/api/mcptools/tools` - List registered MCP tools

## Configuration

The service reads configuration from the Windows Registry:
- **Registry Location**: `HKLM\SYSTEM\CurrentControlSet\Services\AIMXSrv\Parameters`
- **Key Settings**:
  - `EmbeddingModelPath` - Path to BERT ONNX model
  - `EmbeddingVocabPath` - Path to BERT vocabulary file
  - `VectorStorePersistenceFilePath` - Database file name (default: database.bin)

## Integration with AIMX Service

The NetRAG service is designed to integrate with the AIMX service for enhanced PowerShell command processing:

1. **AIMX Service** starts NetRAG service automatically
2. **PowerShell Command Search** provides rich context for LLM analysis
3. **Enhanced Command Generation** with real examples and parameter information
4. **Direct Execution** of properly formatted PowerShell commands

## Dependencies

- **.NET 8.0** - Runtime platform
- **Microsoft.SemanticKernel** - AI orchestration framework
- **BERT ONNX Model** - For generating text embeddings
- **System.Numerics.Tensors** - High-performance vector operations

## Development

### Adding New Features
1. **Service Features** - Add to NetRagService project
2. **Data Processing** - Add to DatabaseConverter project
3. **Shared Code** - Consider creating a shared library

### Testing
```bash
# Run service tests
dotnet test NetRagService

# Test database conversion
cd DatabaseConverter
dotnet run sample_data.json test_database.bin
```

## Performance

### Service Performance
- **Startup Time**: < 2 seconds (with pre-computed database)
- **Search Latency**: < 100ms for typical queries
- **Memory Usage**: ~200MB (depending on database size)

### Database Conversion Performance
- **Processing Speed**: ~100-200 commands/second
- **Compression Ratio**: 60-80% size reduction
- **Memory Usage**: Scales with input data size

## Troubleshooting

### Common Issues

1. **Service won't start**
   - Check if database.bin exists in service directory
   - Verify BERT model files are present
   - Check Windows Registry configuration

2. **Search returns no results**
   - Verify database.bin was created successfully
   - Check if vector store loaded (see service logs)
   - Ensure query is not empty

3. **Database conversion fails**
   - Verify input JSON format matches expected schema
   - Check if BERT model files are accessible
   - Ensure sufficient disk space for output file

### Logging
- **Service Logs**: Windows Event Log (Application)
- **Console Logs**: When running with --console flag
- **Debug Logs**: Enable via configuration

## License

This project is part of the Microsoft AIMX system.
