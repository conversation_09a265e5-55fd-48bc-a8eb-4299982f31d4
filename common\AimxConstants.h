/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:
    AimxConstants.h

Abstract:
    Centralized constants for the AIMX system to eliminate hard-coded values
    throughout the codebase.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 6/29/2025

--*/

#pragma once

#include <cstdint>
#include <chrono>

// Borrow a GUID_NULL
#ifndef GUID_NULL
struct __declspec(uuid("00000000-0000-0000-0000-000000000000")) GUID_NULL;
#define GUID_NULL __uuidof(struct GUID_NULL)
#endif

namespace AimxConstants
{
    // Buffer Sizes
    namespace BufferSizes
    {
        constexpr size_t AIMX_TINY_BUFFER = 64;                  // For small strings like port numbers
        constexpr size_t AIMX_MEDIUM_BUFFER = 1024;              // Standard buffer size (replaces magic 1024)
        constexpr size_t AIMX_LARGE_BUFFER = 4096;               // Large buffer size for HTTP operations
        constexpr size_t AIMX_URL_HOSTNAME_BUFFER = 256;         // Buffer size for URL hostname
        constexpr size_t AIMX_URL_PATH_BUFFER = 1024;            // Buffer size for URL path
    }

    // Network and Port Configuration for LLM inference
    namespace Network
    {
        constexpr uint16_t AIMX_MIN_PORT = 1024;          // Minimum allowed port (replaces magic 1024)
        constexpr uint16_t AIMX_MAX_PORT = 65535;         // Maximum allowed port (replaces magic 65535)
        constexpr const wchar_t* AIMX_DEFAULT_LLM_HOST = L"localhost";
        constexpr const wchar_t* AIMX_LLM_ENDPOINT_PATH = L"/v1/chat/completions";
        constexpr const wchar_t* AIMX_DEFAULT_LLM_SCHEME = L"http://";  // For testing ATM, we would need to switch to HTTPS
    }

    // Protocol Constants for JSON-RPC and MCP
    // NOTE: JSON-RPC protocol handling now provided by McpProtocolLib
    // These constants are kept for response parsing and legacy compatibility
    namespace Protocol
    {
        // JSON-RPC Protocol Constants (prefer McpProtocol::JsonRpc for new code)
        constexpr const char* AIMX_JSONRPC_VERSION = "2.0";

        // MCP Method Constants (prefer McpProtocol::Mcp::Methods for new code)
        constexpr const char* AIMX_METHOD_INITIALIZE = "initialize";
        constexpr const char* AIMX_MCP_TOOLS_LIST = "tools/list";
        constexpr const char* AIMX_MCP_TOOLS_CALL = "tools/call";
        constexpr const char* AIMX_METHOD_PING = "ping";
        constexpr const char* AIMX_GET_AD_COMMAND_TOOL = "get_ad_command";

        // JSON-RPC Field Constants (used for response parsing)
        constexpr const char* AIMX_FIELD_JSONRPC = "jsonrpc";
        constexpr const char* AIMX_FIELD_METHOD = "method";
        constexpr const char* AIMX_FIELD_PARAMS = "params";
        constexpr const char* AIMX_FIELD_ID = "id";
        constexpr const char* AIMX_FIELD_RESULT = "result";
        constexpr const char* AIMX_FIELD_ERROR = "error";
        constexpr const char* AIMX_FIELD_CODE = "code";
        constexpr const char* AIMX_FIELD_MESSAGE = "message";
        constexpr const char* AIMX_FIELD_DATA = "data";

        // MCP Specific Field Constants
        constexpr const char* AIMX_FIELD_TOOLS = "tools";
        constexpr const char* AIMX_FIELD_CONTENT = "content";
        constexpr const char* AIMX_FIELD_NAME = "name";
        constexpr const char* AIMX_FIELD_DESCRIPTION = "description";
        constexpr const char* AIMX_FIELD_INPUT_SCHEMA = "inputSchema";

        // MCP Parameter Constants
        constexpr const char* AIMX_PARAM_NAME = "name";
        constexpr const char* AIMX_PARAM_ARGUMENTS = "arguments";
        constexpr const char* AIMX_PARAM_TOOL = "tool";

        // MCP Error Codes (JSON-RPC 2.0 standard error codes)
        constexpr int AIMX_ERROR_PARSE_ERROR = -32700;
        constexpr int AIMX_ERROR_INVALID_REQUEST = -32600;
        constexpr int AIMX_ERROR_METHOD_NOT_FOUND = -32601;
        constexpr int AIMX_ERROR_INVALID_PARAMS = -32602;
        constexpr int AIMX_ERROR_INTERNAL_ERROR = -32603;
    }

    // Registry Configuration
    namespace Registry
    {
        constexpr const wchar_t* AIMX_REGISTRY_PARAMETERS = L"SYSTEM\\CurrentControlSet\\Services\\AIMXSrv\\Parameters";
        constexpr const wchar_t* AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALPORT = L"FoundryLocalPort";              // Registry key type: REG_SZ
        constexpr const wchar_t* AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELID = L"FoundryLocalModelId";
        constexpr const wchar_t* AIMX_REGISTRY_KEYNAME_LLM_TEMPERATURE = L"FoundryLocalTemperature";
        constexpr const wchar_t* AIMX_REGISTRY_KEYNAME_LLM_TOP_K = L"FoundryLocalTopK";
        constexpr const wchar_t* AIMX_REGISTRY_KEYNAME_LLM_TOP_P = L"FoundryLocalTopP";
        constexpr const wchar_t* AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELALIAS = L"FoundryLocalModelAlias";
        constexpr const wchar_t* AIMX_REGISTRY_KEYNAME_ADMODULELOADED = L"ActiveDirectoryModuleLoaded";
        constexpr const wchar_t* AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALINSTALLED = L"FoundryLocalInstalled";
        constexpr const wchar_t* AIMX_REGISTRY_KEYNAME_FOUNDRYLOCALMODELIDLIST = L"FoundryLocalModelIdList";
        constexpr const wchar_t* AIMX_REGISTRY_KEYNAME_SRV_WAIT_FOR_DEUBBGER = L"WaitForDebugger";
    }

    // recommended llm models
    namespace Models
    {
        constexpr const wchar_t* AIMX_AVAILABLE_MODELS[] =
        {
            L"phi-4",
            L"phi-3.5-mini",
            L"deepseek-r1-7b",
            L"qwen2.5-7b"
        };
    }

    // LLM prompt strings
    namespace LlmPrompt
    {
        constexpr const wchar_t* AIMX_LLM_SYSTEM_PROMPT =   L"You are a helpful assistant that selects the best matching command based on user queries.\n" \
                                                            L"Given a list of available commands in JSON format, select the MOST appropriate command and associated parameters that matches the user's query.\n" \
                                                            L"IMPORTANT: Only match commands that are directly related to the user's query. If the user's query is unrelated to any of the available commands, respond with 'No matching command found'.\n\n" \
                                                            L"Examples of when to return 'No matching command found':\n" \
                                                            L"- User asks about weather, sports, general knowledge, or topics unrelated to the available commands\n" \
                                                            L"- User query doesn't match the purpose or functionality of any available command\n" \
                                                            L"- User asks for operations not supported by the available commands\n\n" \
                                                            L"CRITICAL: Your response must ONLY contain the JSON object or 'No matching command found'. Do NOT include any notes, explanations, comments, or additional text before or after the JSON response.\n\n" \
                                                            L"The commands and their parameters are provided in the following JSON structure:\n" \
                                                            L"{\n" \
                                                            L"  \"commands\": {\n" \
                                                            L"    \"command_key\": {\n" \
                                                            L"      \"command\": \"ActualCommand\",\n" \
                                                            L"      \"description\": \"Command description\",\n" \
                                                            L"      \"type\": \"0|1|2\",\n" \
                                                            L"      \"parameters\": [\n" \
                                                            L"        {\n" \
                                                            L"          \"name\": \"ParameterName\",\n" \
                                                            L"          \"description\": \"Parameter description\",\n" \
                                                            L"          \"required\": true|false,\n" \
                                                            L"          \"defaultValue\": \"default_value\"\n" \
                                                            L"        }\n" \
                                                            L"      ]\n" \
                                                            L"    }\n" \
                                                            L"  }\n" \
                                                            L"}\n";

        constexpr const wchar_t* AIMX_LLM_USER_PROMPT = L"Please respond with the matching command name, type, and ALL its parameters (both required and optional) in following exact JSON format:\n" \
                                                        L"{\"command\": \"ActualCommand\", \"type\": \"CommandType\", \"parameters\": [{\"name\": \"ParameterName\", \"value\": \"extracted_or_default_value\", \"required\": true/false}]}\n" \
                                                        L"IMPORTANT: ALWAYS include these required fields:\n" \
                                                        L"1. \"command\" - the exact command name from the definition\n" \
                                                        L"2. \"type\" - the command type from the definition (0, 1, or 2)\n" \
                                                        L"3. \"parameters\" - ALL parameters defined for the command (both required and optional)\n" \
                                                        L"Extract parameter values from the user query when possible, otherwise use the default values from the command definition.\n" \
                                                        L"For example, if the command has Identity and Properties parameters, include both in your response.\n" \
                                                        L"Please do not include any comments, notes or additional information in your response.\n" \
                                                        L"If you don't find a match, just say 'No matching command found'.\n";

        constexpr const wchar_t* AIMX_MCP_LLM_SYSTEM_PROMPT =   L"You are a helpful assistant that selects the best matching Active Directory command based on user queries.\n" \
                                                                L"Given the following list of available Active Directory commands, select the MOST appropriate command that matches the user's query.\n" \
                                                                L"If no command is a good match, respond with 'No matching command found'.\n\n" \
                                                                L"Available commands:\n";

        constexpr const wchar_t* AIMX_QUESTION_LLM_SYSTEM_PROMPT = L"You are a helpful assistant that answers a question accurately using only the given information.\n" \
                                                                L"Given the following information and the users prompt, provide the most precise answer to the prompt question using the provided information.\n\n" \
                                                                L"Only use the provided information. If the answer is not present, say 'I don't know this'.\n\n" \
                                                                L"Available Information:\n";
    }

    // LLM Inference default values
    namespace LlmInferenceDefaults
    {
        constexpr auto LLM_DEFAULT_TEMPERATURE_VALUE = 0.7f;
        constexpr auto LLM_DEFAULT_TOP_K_VALUE = 40;
        constexpr auto LLM_DEFAULT_TOP_P_VALUE = 0.9f;
        constexpr auto LLM_DEFAULT_STREAM_VALUE = false;
        constexpr auto LLM_DEFAULT_N_PREDICT_VALUE = 1024;
        constexpr auto LLM_DEFAULT_CACHE_PROMPT_VALUE = true;

        // Token limits for LLM requests
        constexpr int LLM_MAX_TOKENS_4K = 4096;              // 4k tokens for Foundry local AI
        constexpr int LLM_MAX_TOKENS_LEGACY = 2048;          // Legacy token limit
    }


    // LLM Foundry Role Types
    namespace LlmFoundryRoleType
    {
        constexpr const char* AIMX_LLM_FOUNDRY_ROLE_TYPE_SYSTEM = "system";
        constexpr const char* AIMX_LLM_FOUNDRY_ROLE_TYPE_USER = "user";
    }

    // JSON Field Names for string literals
    namespace JsonFields
    {
        // Standard JSON-RPC fields
        constexpr const char* AIMX_JSONRPC = "jsonrpc";
        constexpr const char* AIMX_METHOD = "method";
        constexpr const char* AIMX_PARAMS = "params";
        constexpr const char* AIMX_ID = "id";
        constexpr const char* AIMX_RESULT = "result";
        constexpr const char* AIMX_JSON_ERROR = "error";
        constexpr const char* AIMX_CODE = "code";
        constexpr const char* AIMX_MESSAGE = "message";

        // Core JSON structure fields
        constexpr const char* AIMX_COMMANDS = "commands";        
        constexpr const char* AIMX_DESCRIPTION = "description";
        constexpr const char* AIMX_TYPE = "type";
        constexpr const char* AIMX_NAME = "name";
        constexpr const char* AIMX_VALUE = "value";
        constexpr const char* AIMX_REQUIRED = "required";
        constexpr const char* AIMX_DEFAULT_VALUE = "defaultValue";
        constexpr const char* AIMX_COMMAND_KEY = "commandKey";

        // Tool and Command fields
        constexpr const char* AIMX_TOOL = "tool";
        constexpr const char* AIMX_PROMPT = "prompt";
        constexpr const char* AIMX_COMMAND = "command";
        constexpr const char* AIMX_COMMAND_TYPE = "commandType";
   
        // Operation and Planning fields
        constexpr const char* AIMX_OPERATION_ID = "operationId";
        constexpr const char* AIMX_STATUS = "status";
        constexpr const char* AIMX_EXECUTION_MODE = "executionMode";
        constexpr const char* AIMX_PLAN = "executionPlan";
        constexpr const char* AIMX_STEPS = "steps";
        constexpr const char* AIMX_STEP_ID = "stepId";
        constexpr const char* AIMX_ACTION = "action";
        constexpr const char* AIMX_TARGET = "target";
        constexpr const char* AIMX_TOOL_NAME = "toolName";
        constexpr const char* AIMX_PARAMETERS = "parameters";
        constexpr const char* AIMX_REQUIRED_SERVICES = "requiredServices";
        constexpr const char* AIMX_REQUIRES_APPROVAL = "requiresApproval";

        // Response status fields
        constexpr const char* AIMX_SUCCESS = "success";
        constexpr const char* AIMX_FAILURE = "failure";

        // Active Directory specific fields
        constexpr const char* AIMX_AD_AREA = "adArea";
        constexpr const char* AIMX_DC_NAME = "dcName";
        constexpr const char* AIMX_REPLICATION_DATA = "replicationData";
        constexpr const char* AIMX_REPL_STATUS = "replStatus";
        constexpr const char* AIMX_REPLICATION_NEIGHBORS = "replicationNeighbors";
        constexpr const char* AIMX_FTIME_LAST_SYNC_SUCCESS = "ftimeLastSyncSuccess";
        constexpr const char* AIMX_FTIME_LAST_SYNC_ATTEMPT = "ftimeLastSyncAttempt";
        constexpr const char* AIMX_CNUM_CONSECUTIVE_SYNC_FAILURES = "cNumConsecutiveSyncFailures";
        constexpr const char* AIMX_LAST_SYNC_RESULT = "cNumConsecutiveSyncFailures";

        // Request/Response fields
        constexpr const char* AIMX_JSON_KEY_REQUEST_TYPE = "requestType";

        // AIMX-specific JSON fields
        constexpr const char* AIMX_JSON_KEY_ERROR_CODE = "errorCode";
        constexpr const char* AIMX_JSON_KEY_ERROR_MESSAGE = "errorMessage";
        constexpr const char* AIMX_JSON_KEY_PLAN_TYPE = "planType";
        constexpr const char* AIMX_JSON_KEY_QUERY = "query";
        constexpr const char* AIMX_JSON_KEY_ORIGINAL_QUERY = "originalQuery";
        constexpr const char* AIMX_JSON_KEY_RISK_LEVEL = "riskLevel";
        constexpr const char* AIMX_JSON_KEY_SERVICE = "service";
        constexpr const char* AIMX_JSON_KEY_TIMEOUT = "timeout";
        constexpr const char* AIMX_JSON_KEY_TOOL_PARAMS = "toolParams";

        // llm inference specific fields
        constexpr const char* AIMX_JSON_KEY_TEMPERATURE = "temperature";
        constexpr const char* AIMX_JSON_KEY_TOP_K = "top_k";
        constexpr const char* AIMX_JSON_KEY_TOP_P = "top_p";
        constexpr const char* AIMX_JSON_KEY_STREAM = "stream";
        constexpr const char* AIMX_JSON_KEY_MODEL = "model";
        constexpr const char* AIMX_JSON_KEY_MESSAGES = "messages";
        constexpr const char* AIMX_JSON_KEY_ROLE = "role";
        constexpr const char* AIMX_JSON_KEY_CONTENT = "content";
        constexpr const char* AIMX_JSON_KEY_PROMPT = "prompt";
        constexpr const char* AIMX_JSON_KEY_N_PREDICT = "n_predict";
        constexpr const char* AIMX_JSON_KEY_CACHE_PROMPT = "cache_prompt";
        constexpr const char* AIMX_JSON_KEY_STOP = "stop";
        constexpr const char* AIMX_JSON_KEY_CHOICES = "choices";
        constexpr const char* AIMX_JSON_KEY_DELTA = "delta";
        constexpr const char* AIMX_JSON_KEY_TEXT = "text";
        constexpr const char* AIMX_JSON_KEY_RESPONSE = "response";
        

        // Execution result JSON fields
        constexpr const char* AIMX_JSON_KEY_EXECUTION_STATUS = "executionStatus";
        constexpr const char* AIMX_JSON_KEY_STEPS_EXECUTED = "stepsExecuted";
        constexpr const char* AIMX_JSON_KEY_EXECUTION_TIME = "executionTime";
        constexpr const char* AIMX_JSON_KEY_RESULT_DATA = "resultData";
        constexpr const char* AIMX_JSON_KEY_COMBINED_RESULTS = "combinedResults";
        constexpr const char* AIMX_JSON_KEY_AGGREGATION_STATUS = "aggregationStatus";
        constexpr const char* AIMX_JSON_KEY_SOURCE_DATA = "sourceData";
        constexpr const char* AIMX_JSON_KEY_FINAL_RESULT = "finalResult";
        constexpr const char* AIMX_JSON_KEY_SUMMARY = "summary";
        constexpr const char* AIMX_JSON_KEY_DATA_QUALITY = "dataQuality";
        constexpr const char* AIMX_JSON_KEY_COMPLETENESS = "completeness";
        constexpr const char* AIMX_JSON_KEY_AGGREGATION_TIME = "aggregationTime";

        // Status values
        constexpr const char* AIMX_JSON_VALUE_COMPLETED = "completed";
        constexpr const char* AIMX_JSON_VALUE_HIGH = "high";
        constexpr const char* AIMX_JSON_VALUE_100_PERCENT = "100%";
    }

    // Action Types for AD Operations
    namespace ActionTypes
    {
        constexpr const char* AIMX_GET_REPLICATION_STATUS_INDIVIDUAL_DC = "getReplicationStatusForIndividualDC";
        constexpr const char* AIMX_GET_REPLICATION_STATUS_ALL_DCS = "getReplicationStatusForAllDCsInADomain";
    }

    // Active Directory Areas
    namespace ADAreas
    {
        constexpr const wchar_t* AIMX_DC_DIAGNOSTICS = L"AD_DC_DIAGNOSTICS";
        constexpr const wchar_t* AIMX_REPLICATION = L"AD_REPLICATION";
        constexpr const wchar_t* AIMX_SECURITY_INSIGHTS_ACCOUNTS = L"AD_SECURITY_INSIGHTS_ACCOUNTS";
        constexpr const wchar_t* AIMX_SECURITY_INSIGHTS_COMPLIANCE = L"AD_SECURITY_INSIGHTS_COMPLIANCE";
        constexpr const wchar_t* AIMX_PERFORMANCE = L"AD_PERFORMANCE";
        constexpr const wchar_t* AIMX_DATABASE_HEALTH = L"AD_DATABASE_HEALTH";
        constexpr const wchar_t* AIMX_DEPENDENCIES = L"AD_DEPENDENCIES";
        constexpr const wchar_t* AIMX_DFSR = L"AD_DFSR";
        constexpr const wchar_t* AIMX_GROUP_POLICY = L"AD_GROUP_POLICY";
        constexpr const wchar_t* AIMX_UNKNOWN = L"UNKNOWN";
    }

    // Error Messages
    namespace ErrorMessages
    {
        constexpr const wchar_t* AIMX_FAILED_TO_OPEN_PIPE = L"Failed to open pipe";
        constexpr const wchar_t* AIMX_MISSING_REQUIRED_FIELD_ADAREA = L"Missing required field: ADArea";
        constexpr const char* AIMX_MISSING_JSONRPC_FIELD = "Missing 'jsonrpc' field.";
        constexpr const char* AIMX_MISSING_METHOD_FIELD = "Missing 'method' field.";
        constexpr const char* AIMX_MISSING_ID_FIELD = "Missing 'id' field.";
        constexpr const char* AIMX_MISSING_PARAMS_FIELD = "Missing 'params' field.";
        constexpr const char* AIMX_MISSING_TOOL_FIELD = "Missing 'tool' field.";
        constexpr const char* AIMX_MISSING_PROMPT_PARAM = "Missing required parameter: prompt";
        constexpr const char* AIMX_PROMPT_CANNOT_BE_EMPTY = "Prompt cannot be empty";
        constexpr const char* AIMX_COMMAND_MAPPING_NOT_INITIALIZED = "Command mapping system not initialized";
        constexpr const char* AIMX_NO_MATCHING_AD_COMMAND = "No matching AD command found for the given prompt";
        constexpr const char* AIMX_UNKNOWN_TOOL_PREFIX = "Unknown tool: ";
        constexpr const char* AIMX_UNKNOWN_METHOD_PREFIX = "Unknown method: ";
        constexpr const char* AIMX_INTERNAL_ERROR_PREFIX = "Internal error: ";
        constexpr const char* AIMX_NO_MATCHING_COMMAND_FOUND = "No matching command found";
        constexpr const char* AIMX_NO_SUITABLE_AD_COMMAND_FOUND = "No suitable Active Directory command found for the given query";
    }

    // Status String Constants
    namespace StatusStrings
    {
        constexpr const char* AIMX_STATUS_STR_PLANNING = "PLANNING";
        constexpr const char* AIMX_STATUS_STR_PLAN_READY = "PLAN_READY";
        constexpr const char* AIMX_STATUS_STR_EXECUTING = "EXECUTING";
        constexpr const char* AIMX_STATUS_STR_COMPLETED = "COMPLETED";
        constexpr const char* AIMX_STATUS_STR_FAILED = "FAILED";
        constexpr const char* AIMX_STATUS_STR_CANCELLED = "CANCELLED";
        constexpr const char* AIMX_STATUS_STR_UNKNOWN = "UNKNOWN";
    }

    // Plan Type Constants
    namespace PlanTypes
    {
        constexpr const char* AIMX_PLAN_TYPE_DIRECT = "direct";
        constexpr const char* AIMX_PLAN_TYPE_CHATBOT_QUERY = "chatbot_query";
    }

    // Action Constants
    namespace Actions
    {
        constexpr const char* AIMX_ACTION_EXECUTE_COMMAND = "execute_command";
        constexpr const char* AIMX_ACTION_PROCESS_NL = "process_natural_language";
    }

    // Target Constants
    namespace Targets
    {
        constexpr const char* AIMX_TARGET_EXTERNAL_SERVICE = "external_service";
        constexpr const char* AIMX_TARGET_AD_TOOLS_AGENT = "ad_tools_agent";
        constexpr const char* AIMX_TARGET_AI_LLM_AGENT = "ai_llm_agent";
    }

    // Message Constants
    namespace Messages
    {
        constexpr const wchar_t* AIMX_MSG_PLANNING_STARTED = L"Planning operation started successfully";
        constexpr const wchar_t* AIMX_MSG_EXECUTION_STARTED = L"Operation execution started successfully";
        constexpr const wchar_t* AIMX_MSG_CANCELLED = L"Operation cancelled successfully";
        constexpr const wchar_t* AIMX_MSG_PLAN_READY = L"Execution plan ready for review";
        constexpr const wchar_t* AIMX_MSG_ANALYZING_QUERY = L"Analyzing query and generating execution plan...";
        constexpr const wchar_t* AIMX_MSG_EXECUTING_OPERATIONS = L"Executing planned operations...";
        constexpr const wchar_t* AIMX_MSG_OPERATION_FAILED = L"Operation failed";
        constexpr const wchar_t* AIMX_MSG_OPERATION_CANCELLED = L"Operation was cancelled";
        constexpr const wchar_t* AIMX_MSG_OPERATION_COMPLETED = L"Operation completed successfully";
        constexpr const wchar_t* AIMX_MSG_EXECUTION_FAILED = L"Operation execution failed";
        constexpr const wchar_t* AIMX_MSG_EXECUTION_CANCELLED = L"Operation execution was cancelled";
        constexpr const wchar_t* AIMX_MSG_UNKNOWN_STATUS = L"Unknown status";
        constexpr const wchar_t* AIMX_MSG_UNKNOWN_EXEC_STATUS = L"Unknown execution status";
        constexpr const wchar_t* AIMX_MSG_NO_PLAN_AVAILABLE = L"No plan available";

        // Orchestrator execution messages
        constexpr const wchar_t* AIMX_MSG_STARTING_TOOL_EXECUTION = L"Starting tool execution...";
        constexpr const wchar_t* AIMX_MSG_TOOL_EXECUTION_FAILED = L"Tool execution failed";
        constexpr const wchar_t* AIMX_MSG_EXECUTION_FAILED_UNEXPECTED = L"Execution failed due to unexpected error";
        constexpr const wchar_t* AIMX_MSG_OPERATION_STARTED = L"Operation started...";

        // Planner messages
        constexpr const wchar_t* AIMX_MSG_ANALYZING_REQUEST = L"Analyzing your request...";
        constexpr const wchar_t* AIMX_MSG_FAILED_ANALYZE_REQUEST = L"Failed to analyze your request";
        constexpr const wchar_t* AIMX_MSG_CREATING_EXECUTION_PLAN = L"Creating execution plan...";
        constexpr const wchar_t* AIMX_MSG_FAILED_CREATE_PLAN = L"Failed to create execution plan";
        constexpr const wchar_t* AIMX_MSG_FAILED_VALIDATE_PLAN = L"Failed to validate execution plan";
        constexpr const wchar_t* AIMX_MSG_PLAN_CREATED_SUCCESSFULLY = L"Plan created successfully";
        constexpr const wchar_t* AIMX_MSG_PLANNING_FAILED_UNEXPECTED = L"Planning failed due to unexpected error";

        // Conversation session messages
        constexpr const wchar_t* AIMX_MSG_SESSION_INACTIVE = L"Session inactive";
        constexpr const wchar_t* AIMX_MSG_PROCESSING_REQUEST = L"Processing request";
        constexpr const wchar_t* AIMX_MSG_STAGE_IDLE = L"Idle";
        constexpr const wchar_t* AIMX_MSG_STAGE_UNKNOWN = L"Unknown";

        // Message type display names
        constexpr const wchar_t* AIMX_MSG_TYPE_USER_INPUT = L"User Input";
        constexpr const wchar_t* AIMX_MSG_TYPE_ASSISTANT_RESPONSE = L"Assistant Response";
        constexpr const wchar_t* AIMX_MSG_TYPE_PROGRESS_UPDATE = L"Progress Update";
        constexpr const wchar_t* AIMX_MSG_TYPE_TASK_BREAKDOWN = L"Task Breakdown";
        constexpr const wchar_t* AIMX_MSG_TYPE_APPROVAL_REQUEST = L"Approval Request";
        constexpr const wchar_t* AIMX_MSG_TYPE_TOOL_EXECUTION = L"Tool Execution";
        constexpr const wchar_t* AIMX_MSG_TYPE_ERROR_MESSAGE = L"Error Message";
        constexpr const wchar_t* AIMX_MSG_TYPE_LLM_INFERENCE = L"LLM Inference";
        constexpr const wchar_t* AIMX_MSG_TYPE_LLM_RESPONSE = L"LLM Response";
        constexpr const wchar_t* AIMX_MSG_TYPE_TOOL_ANALYSIS = L"Tool Analysis";
        constexpr const wchar_t* AIMX_MSG_TYPE_EXECUTION_PLAN = L"Execution Plan";
        constexpr const wchar_t* AIMX_MSG_TYPE_TOOL_EXECUTION_START = L"Tool Execution Start";
        constexpr const wchar_t* AIMX_MSG_TYPE_TOOL_EXECUTION_RESULT = L"Tool Execution Result";
        constexpr const wchar_t* AIMX_MSG_TYPE_STATUS_UPDATE = L"Status Update";
        constexpr const wchar_t* AIMX_MSG_TYPE_UNKNOWN = L"Unknown Message Type";
    }

    // Message Stage/Phase Constants for backend categorization
    namespace MessageStages
    {
        // === REQUEST PROCESSING STAGES ===
        constexpr const wchar_t* AIMX_STAGE_REQUEST_RECEIVED = L"REQUEST_RECEIVED";
        constexpr const wchar_t* AIMX_STAGE_REQUEST_VALIDATION = L"REQUEST_VALIDATION";
        constexpr const wchar_t* AIMX_STAGE_REQUEST_SETUP = L"REQUEST_SETUP";

        // === PLANNING STAGES ===
        constexpr const wchar_t* AIMX_STAGE_PLANNING_START = L"PLANNING_START";
        constexpr const wchar_t* AIMX_STAGE_PLANNING_ANALYSIS = L"PLANNING_ANALYSIS";
        constexpr const wchar_t* AIMX_STAGE_PLANNING_DISCOVERY = L"PLANNING_DISCOVERY";
        constexpr const wchar_t* AIMX_STAGE_PLANNING_CREATION = L"PLANNING_CREATION";
        constexpr const wchar_t* AIMX_STAGE_PLANNING_VALIDATION = L"PLANNING_VALIDATION";
        constexpr const wchar_t* AIMX_STAGE_PLANNING_READY = L"PLANNING_READY";

        // === AI/LLM PROCESSING STAGES ===
        constexpr const wchar_t* AIMX_STAGE_LLM_REQUEST = L"LLM_REQUEST";
        constexpr const wchar_t* AIMX_STAGE_LLM_PROCESSING = L"LLM_PROCESSING";
        constexpr const wchar_t* AIMX_STAGE_LLM_RESPONSE = L"LLM_RESPONSE";
        constexpr const wchar_t* AIMX_STAGE_LLM_PARSING = L"LLM_PARSING";

        // === EXECUTION STAGES ===
        constexpr const wchar_t* AIMX_STAGE_EXECUTION_START = L"EXECUTION_START";
        constexpr const wchar_t* AIMX_STAGE_EXECUTION_STEP = L"EXECUTION_STEP";
        constexpr const wchar_t* AIMX_STAGE_EXECUTION_TOOL_PREP = L"EXECUTION_TOOL_PREP";
        constexpr const wchar_t* AIMX_STAGE_EXECUTION_TOOL_RUN = L"EXECUTION_TOOL_RUN";
        constexpr const wchar_t* AIMX_STAGE_EXECUTION_TOOL_RESULT = L"EXECUTION_TOOL_RESULT";
        constexpr const wchar_t* AIMX_STAGE_EXECUTION_COMPLETED = L"EXECUTION_COMPLETED";


    }

    // JSON field names for message metadata
    namespace MetadataFields
    {
        constexpr const char* AIMX_META_STAGE = "stage";
        constexpr const char* AIMX_META_STEP_INFO = "step_info";
        constexpr const char* AIMX_META_CURRENT_STEP = "current_step";
        constexpr const char* AIMX_META_TOTAL_STEPS = "total_steps";
    }

    // Risk Level Constants
    namespace RiskLevels
    {
        constexpr const char* AIMX_RISK_LEVEL_LOW = "low";
    }


    //namespace AdPowerShellCommands
    namespace AdPowerShellCommands
    {
        // User management commands
        constexpr const char* AD_MCP_COMMAND_GET_AD_USER = "Get-ADUser";
        constexpr const char* AD_MCP_COMMAND_SEARCH_AD_ACCOUNT_LOCKED = "Search-ADAccount";

        // Group management commands
        constexpr const char* AD_MCP_COMMAND_GET_AD_GROUP = "Get-ADGroup";
        constexpr const char* AD_MCP_COMMAND_GET_AD_GROUP_MEMBER = "Get-ADGroupMember";

        // Domain controllers and replication
        constexpr const char* AD_MCP_COMMAND_GET_AD_DOMAIN_CONTROLLER = "Get-ADDomainController";
        constexpr const char* AD_MCP_COMMAND_GET_AD_DOMAIN_CONTROLLER_LIST = "Get-ADDomainController";
        constexpr const char* AD_MCP_COMMAND_DC_DIAG = "dcdiag";

        // Domain structure
        constexpr const char* AD_MCP_COMMAND_GET_AD_DOMAIN = "Get-ADDomain";
        constexpr const char* AD_MCP_COMMAND_GET_AD_FOREST = "Get-ADForest";
        constexpr const char* AD_MCP_COMMAND_GET_AD_TRUST = "Get-ADTrust";

        // FSMO roles
        constexpr const char* AD_MCP_COMMAND_GET_AD_FSMO_ROLES = "Get-ADDomain";

        // Password policy
        constexpr const char* AD_MCP_COMMAND_GET_AD_DOMAIN_PASSWORD_POLICY = "net accounts";
    }

    namespace FoundryCommands
    {
        constexpr const char* FOUNDRY_EXECUTABLE = "foundry";
    }


    namespace AdMcpCommandDescriptions
    {
        // User management commands
        constexpr const char* AD_MCP_COMMAND_USERS_DESC = "Get all attributes for a user in AD";
        constexpr const char* AD_MCP_COMMAND_USER_LOCKED_DESC = "Get Locked User accounts in AD";

        // Group management commands
        constexpr const char* AD_MCP_COMMAND_GROUPS_DESC = "Get Information about a group";
        constexpr const char* AD_MCP_COMMAND_GROUP_MEMBERS_DESC = "List members of a group";

        // Domain controllers and replication
        constexpr const char* AD_MCP_COMMAND_DOMAIN_CONTROLLERS_DESC = "Get Domain controller information";
        constexpr const char* AD_MCP_COMMAND_DC_LIST_DESC = "List domain controllers";
        constexpr const char* AD_MCP_COMMAND_DC_HEALTH_DESC = "Check DC health";

        // Domain structure
        constexpr const char* AD_MCP_COMMAND_DOMAIN_DESC = "Domain information";
        constexpr const char* AD_MCP_COMMAND_FOREST_DESC = "Forest information";
        constexpr const char* AD_MCP_COMMAND_TRUST_DESC = "Trust information";

        // FSMO roles
        constexpr const char* AD_MCP_COMMAND_FSMO_ROLES_DESC = "List FSMO roles";
        constexpr const char* AD_MCP_COMMAND_REPLICATION_DC_DESC = "Get replication status for a specified domain controller";
        constexpr const char* AD_MCP_COMMAND_REPLICATION_ALL_DESC = "Get replication status for all domain controllers in a domain";

        // Password policy
        constexpr const char* AD_MCP_COMMAND_PASSWORD_POLICY_DESC = "Show domain password policy";
    }

    namespace AdMcpCommandParams
    {
        // User management commands
        constexpr const char* AD_COMMAND_PARAMS_IDENTITY = "Identity";
        constexpr const char* AD_COMMAND_PARAMS_PROPERTIES = "Properties";
        constexpr const char* AD_COMMAND_PARAMS_LOCKEDOUT = "LockedOut";
        constexpr const char* AD_COMMAND_PARAMS_FILTER = "Filter";
        constexpr const char* AD_COMMAND_PARAMS_DOMAIN = "Domain";
        constexpr const char* AD_COMMAND_PARAMS_SERVER = "Server";
        constexpr const char* AD_COMMAND_PARAMS_DOMAINCONTROLLER = "DomainController";
    }

    // Command Type Constants
    namespace CommandTypes
    {
        constexpr const char* AD_COMMAND_TYPE_POWERSHELL = "0";
        constexpr const char* AD_COMMAND_TYPE_AGENT_API = "1";
        constexpr const char* AD_COMMAND_TYPE_EXECUTABLE = "2";

        // Command type string constants for parsing
        constexpr const char* AD_COMMAND_TYPE_POWERSHELL_STR = "POWERSHELL";
        constexpr const char* AD_COMMAND_TYPE_FUNCTION_STR = "FUNCTION";
        constexpr const char* AD_COMMAND_TYPE_EXECUTABLE_STR = "EXECUTABLE";
        constexpr const char* AD_COMMAND_TYPE_UNKNOWN_STR = "UNKNOWN";
    }

    // Parameter Default Values
    namespace ParameterDefaults
    {
        constexpr const char* AD_PARAM_DEFAULT_EMPTY = "";
        constexpr const char* AD_PARAM_DEFAULT_WILDCARD = "*";
        constexpr const char* AD_PARAM_DEFAULT_TRUE = "true";
        constexpr const char* AD_PARAM_DEFAULT_DOMAIN = "/domain";
    }

    // Parameter Descriptions
    namespace ParameterDescriptions
    {
        constexpr const char* AD_PARAM_DESC_USERNAME = "Username or distinguished name of the user";
        constexpr const char* AD_PARAM_DESC_PROPERTIES = "Properties to retrieve";
        constexpr const char* AD_PARAM_DESC_LOCKEDOUT = "Search for locked out accounts";
        constexpr const char* AD_PARAM_DESC_GROUP_NAME = "Group name or distinguished name";
        constexpr const char* AD_PARAM_DESC_GROUP_MEMBERS = "Group name to get members for";
        constexpr const char* AD_PARAM_DESC_DC_NAME = "Domain controller name";
        constexpr const char* AD_PARAM_DESC_SERVER = "Domain controller to check";
        constexpr const char* AD_PARAM_DESC_TRUST_FILTER = "Filter for trusts";
        constexpr const char* AD_PARAM_DESC_DOMAIN = "Show domain password policy";
        constexpr const char* AD_PARAM_DESC_DC_REPLICATION = "Domain controller to check replication for";
    }

    // Parameter Status Constants
    namespace ParameterStatus
    {
        constexpr const char* AD_PARAM_STATUS_REQUIRED = "Required";
        constexpr const char* AD_PARAM_STATUS_OPTIONAL = "Optional";
    }

    // Query Keywords for Command Matching
    namespace QueryKeywords
    {
        constexpr const char* KEYWORD_USER = "user";
        constexpr const char* KEYWORD_PROPERTIES = "properties";
        constexpr const char* KEYWORD_GET = "get";
        constexpr const char* KEYWORD_GROUP = "group";
        constexpr const char* KEYWORD_MEMBER = "member";
        constexpr const char* KEYWORD_LOCKED = "locked";
        constexpr const char* KEYWORD_DC = "dc";
        constexpr const char* KEYWORD_DOMAIN_CONTROLLER = "domain controller";
        constexpr const char* KEYWORD_DOMAIN = "domain";
        constexpr const char* KEYWORD_CONTROLLER = "controller";
        constexpr const char* KEYWORD_FOREST = "forest";
        constexpr const char* KEYWORD_TRUST = "trust";
        constexpr const char* KEYWORD_PASSWORD = "password";
        constexpr const char* KEYWORD_POLICY = "policy";
        constexpr const char* KEYWORD_REPLICATION = "replication";
        constexpr const char* KEYWORD_ALL = "all";
        constexpr const char* KEYWORD_DETAILS = "details";
        constexpr const char* KEYWORD_INFO = "info";
    }

    // Command Keywords for Command Matching
    namespace CommandKeywords
    {
        constexpr const char* CMD_KEYWORD_GET_ADUSER = "get-aduser";
        constexpr const char* CMD_KEYWORD_GET_ADGROUP = "get-adgroup";
        constexpr const char* CMD_KEYWORD_GET_ADGROUPMEMBER = "get-adgroupmember";
        constexpr const char* CMD_KEYWORD_SEARCH_ADACCOUNT = "search-adaccount";
        constexpr const char* CMD_KEYWORD_GET_ADDOMAINCONTROLLER = "get-addomaincontroller";
        constexpr const char* CMD_KEYWORD_GET_ADDOMAIN = "get-addomain";
        constexpr const char* CMD_KEYWORD_GET_ADFOREST = "get-adforest";
        constexpr const char* CMD_KEYWORD_GET_ADTRUST = "get-adtrust";
        constexpr const char* CMD_KEYWORD_NET_ACCOUNTS = "net accounts";
        constexpr const char* CMD_KEYWORD_ADREPLICATIONTOOLAGENT = "adreplicationtoolagent";
        constexpr const char* CMD_KEYWORD_GETREPLICATIONSTATUSFORALLDOMAINCONTROLLERS = "getreplicationstatusforalldomaincontrollers";
        constexpr const char* CMD_KEYWORD_GETREPLICATIONSTATUSFORDOMAINCONTROLLER = "getreplicationstatusfordomaincontroller";
    }

    // Identity Pattern Keywords
    namespace IdentityPatterns
    {
        constexpr const char* PATTERN_FOR = "for ";
        constexpr const char* PATTERN_USER = "user ";
        constexpr const char* PATTERN_ACCOUNT = "account ";
        constexpr const char* PATTERN_USERNAME = "username ";
        constexpr const char* PATTERN_NAME = "name ";
    }

    // Common Command Words
    namespace CommonCommandWords
    {
        constexpr const char* WORD_GET = "get";
        constexpr const char* WORD_SHOW = "show";
        constexpr const char* WORD_LIST = "list";
        constexpr const char* WORD_FIND = "find";
        constexpr const char* WORD_PROPERTIES = "properties";
        constexpr const char* WORD_ALL = "all";
        constexpr const char* WORD_INFO = "info";
        constexpr const char* WORD_DETAILS = "details";
    }

    // Extract Pattern Keywords
    namespace ExtractPatterns
    {
        constexpr const char* PATTERN_FOR_EXTRACT = "for";
        constexpr const char* PATTERN_IS_EXTRACT = "is";
    }

    // AD Agent API Constants
    namespace ADAgentTools
    {
        constexpr const char* AIMX_AD_TOOLS_POWERSHELL = "Powershell.exe -Command ";
        constexpr const wchar_t* AIMX_AD_TOOLS_POWERSHELL_EXE = L"Powershell.exe ";
        constexpr const wchar_t* AIMX_AD_TOOLS_POWERSHELL_SWITCHE_NOPROFILE = L"-NoProfile ";
        constexpr const wchar_t* AIMX_AD_TOOLS_POWERSHELL_SWITCHE_NONINTERACTIVE = L"-NonInteractive ";
        constexpr const wchar_t* AIMX_AD_TOOLS_POWERSHELL_SWITCHE_COMMAND = L"-Command ";
    }

    namespace ADAgentAPI
    {
        constexpr const char* GET_REPLICATION_STATUS_FOR_INDIVIDUAL_DC = "ADReplicationToolAgent::GetReplicationStatusForIndividualDC";
        constexpr const char* GET_REPLICATION_STATUS_FOR_ALL_DCS_IN_ADOMAIN = "ADReplicationToolAgent::GetReplicationStatusForAllDCsInADomain";
    }

    // HTTP Constants
    namespace Http
    {
        constexpr const wchar_t* AIMX_HTTP_USER_AGENT = L"AIMX Client/1.0";
        constexpr const wchar_t* AIMX_HTTP_METHOD_POST = L"POST";
        constexpr const wchar_t* AIMX_HTTP_CONTENT_TYPE_JSON = L"Content-Type: application/json\r\n";
        constexpr const char* AIMX_HTTP_CONTENT_TYPE_JSON_VALUE = "application/json";

        // HTTP timeout constants (in milliseconds)
        constexpr DWORD AIMX_HTTP_CONNECT_TIMEOUT = 30000;   // 30 seconds for connection
        constexpr DWORD AIMX_HTTP_SEND_TIMEOUT = 60000;      // 60 seconds for sending request
        constexpr DWORD AIMX_HTTP_RECEIVE_TIMEOUT = 300000;  // 5 minutes for receiving response (LLM processing time)
        constexpr DWORD AIMX_HTTP_STREAMING_TIMEOUT = 60000; // 60 seconds for streaming operations

        // HTTP status codes
        constexpr DWORD AIMX_HTTP_STATUS_OK = 200;
        constexpr DWORD AIMX_HTTP_STATUS_BAD_REQUEST = 400;
        constexpr DWORD AIMX_HTTP_STATUS_NOT_FOUND = 404;
        constexpr DWORD AIMX_HTTP_STATUS_METHOD_NOT_ALLOWED = 405;
        constexpr DWORD AIMX_HTTP_STATUS_INTERNAL_SERVER_ERROR = 500;
        constexpr DWORD AIMX_HTTP_STATUS_SERVICE_UNAVAILABLE = 503;
    }



    // MCP Tool Manager Configuration Constants
    namespace McpToolManager
    {
        // Execution timeouts (in milliseconds)
        constexpr DWORD AIMX_MCP_DEFAULT_TIMEOUT_MS = 30000;     // 30 seconds default timeout
        constexpr DWORD AIMX_MCP_RETRY_DELAY_MS = 1000;         // 1 second retry delay

        // Execution limits
        constexpr DWORD AIMX_MCP_MAX_CONCURRENT_EXECUTIONS = 10; // Maximum concurrent tool executions
        constexpr DWORD AIMX_MCP_DEFAULT_RETRY_COUNT = 3;        // Default number of retries

        // Default port for testing
        constexpr int AIMX_DEFAULT_FOUNDRY_PORT = 5273;          // Default Foundry Local port
    }

    // MCP Configuration Management Constants
    namespace McpConfiguration
    {
        // Configuration file paths
        constexpr const wchar_t* AIMX_MCP_CONFIG_REGISTRY_KEY = L"McpConfigPath";
        constexpr const wchar_t* AIMX_MCP_DEFAULT_CONFIG_FILENAME = L"aimx_mcpsvr.json";
        constexpr const wchar_t* AIMX_MCP_CONFIG_DIRECTORY_PATH = L"\\Microsoft\\AIMX\\";
        constexpr const wchar_t* AIMX_MCP_CONFIG_FALLBACK_BASE_PATH = L"C:\\ProgramData";

        // Connection pool settings
        constexpr DWORD AIMX_MCP_CONNECTION_POOL_MAX_SIZE = 50;
        constexpr DWORD AIMX_MCP_CONNECTION_IDLE_TIMEOUT_MS = 300000;  // 5 minutes
        constexpr DWORD AIMX_MCP_CONNECTION_HEALTH_CHECK_INTERVAL_MS = 60000;  // 1 minute

        // Process management
        constexpr DWORD AIMX_MCP_PROCESS_STARTUP_TIMEOUT_MS = 10000;  // 10 seconds
        constexpr DWORD AIMX_MCP_PROCESS_SHUTDOWN_TIMEOUT_MS = 5000;  // 5 seconds


    }

    // LLM Protocol Constants
    namespace LlmProtocol
    {
        constexpr const char* AIMX_LLM_STOP_TOKEN = "[INST]";
        constexpr const char* AIMX_LLM_DONE_MARKER = "[DONE]";
        constexpr const char* AIMX_LLM_RESPONSE_MARKER = "[response]:";
        constexpr const char* AIMX_SSE_DATA_PREFIX = "data: ";
    }

    // LLM Response Parsing Constants
    namespace LlmResponseParsing
    {
        constexpr const char* AIMX_LLM_NO_MATCH_LOWER = "no match";
        constexpr const char* AIMX_WHITESPACE_CHARS = " \n\r\t";
        constexpr const char* AIMX_TRIM_CHARS = " \t\r\n";
    }

    // Prompt Formatting Constants
    namespace PromptFormatting
    {
        constexpr const wchar_t* AIMX_URL_PORT_SEPARATOR = L":";
        constexpr const wchar_t* AIMX_NEWLINE = L"\n ";
        constexpr const wchar_t* AIMX_SENTENCE_SEPARATOR = L". ";
        constexpr const wchar_t* AIMX_BULLET_POINT = L"- ";
        constexpr const wchar_t* AIMX_DOUBLE_NEWLINE = L"\n\n";
        constexpr const wchar_t* AIMX_USER_QUERY_PREFIX = L"User query: ";

        // Common execution strings
        constexpr const wchar_t* AIMX_EXECUTING_STEP_PREFIX = L"Executing step ";
        constexpr const wchar_t* AIMX_OF_SEPARATOR = L" of ";
        constexpr const wchar_t* AIMX_STEP_PREFIX = L"Step ";
        constexpr const wchar_t* AIMX_SECONDS_SUFFIX = L" seconds";
        constexpr const wchar_t* AIMX_TOOLS_SUFFIX = L" tools";
        constexpr const wchar_t* AIMX_STEPS_SUFFIX = L" steps";
        constexpr const wchar_t* AIMX_MORE_STEPS_PREFIX = L"  ... and ";
        constexpr const wchar_t* AIMX_MORE_STEPS_SUFFIX = L" more steps";

        // LLM response prefixes
        constexpr const wchar_t* AIMX_LLM_RESPONSE_PREFIX = L"LLM Response to: ";
        constexpr const wchar_t* AIMX_TOOL_EXECUTION_FAILED_PREFIX = L"Tool execution failed: ";
        constexpr const wchar_t* AIMX_BASED_ON_INFO_PREFIX = L"Based on the available information:\n\n";
        constexpr const wchar_t* AIMX_ADDRESSES_QUERY_PREFIX = L"\n\nThis information addresses your query: \"";
        constexpr const wchar_t* AIMX_ADDRESSES_QUERY_SUFFIX = L"\"";
        constexpr const wchar_t* AIMX_UNABLE_TO_GENERATE_PREFIX = L"Unable to generate a comprehensive response. Raw results: ";

        // JSON formatting strings
        constexpr const wchar_t* AIMX_JSON_OPEN_BRACE = L"{\n";
        constexpr const wchar_t* AIMX_JSON_CLOSE_BRACE = L"}";
        constexpr const wchar_t* AIMX_JSON_COMMA_NEWLINE = L",\n";
        constexpr const wchar_t* AIMX_JSON_NEWLINE = L"\n";
        constexpr const wchar_t* AIMX_COLON_SEPARATOR = L": ";
    }
}

// Component-specific error codes
#define AIMX_E_COMPONENT_NOT_INITIALIZED    MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x1001)
#define AIMX_E_COMPONENT_ALREADY_INITIALIZED MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x1002)
#define AIMX_E_COMPONENT_NOT_FOUND          MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x1003)
#define AIMX_E_INVALID_COMPONENT_CONFIG     MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x1004)
#define AIMX_E_COMPONENT_HEALTH_CHECK_FAILED MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x1005)
#define AIMX_E_MCP_SERVER_NOT_AVAILABLE     MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x1006)
#define AIMX_E_TOOL_EXECUTION_FAILED        MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x1007)
#define AIMX_E_TOOL_NOT_FOUND               MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x1008)
#define AIMX_E_LLM_INFERENCE_FAILED         MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x1009)
#define AIMX_E_PLAN_GENERATION_FAILED       MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x100A)
#define AIMX_E_ORCHESTRATION_FAILED         MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x100B)

// Component-specific success codes
#define AIMX_S_COMPONENT_ALREADY_INITIALIZED MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0x1001)
#define AIMX_S_OPERATION_PENDING            MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0x1002)
#define AIMX_S_PARTIAL_SUCCESS              MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0x1003)
