# PowerShell Active Directory RAG Database Optimizer
# Transforms comprehensive scraped data into RAG-optimized format for better search and LLM integration

param(
    [string]$InputFile = "comprehensive_ad_data.json",
    [string]$OutputFile = "ad_powershell_rag_database.json",
    [switch]$Verbose
)

Write-Host "PowerShell Active Directory RAG Database Optimizer" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Load the comprehensive data
if (-not (Test-Path $InputFile)) {
    Write-Error "Input file not found: $InputFile"
    exit 1
}

Write-Host "Loading comprehensive AD data from: $InputFile" -ForegroundColor Yellow
$rawData = Get-Content $InputFile -Raw | ConvertFrom-Json

# Initialize RAG-optimized structure
$ragDatabase = @{
    metadata = @{
        created_at = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        source_file = $InputFile
        total_commands = 0
        total_searchable_entries = 0
        optimization_version = "1.0"
        description = "RAG-optimized PowerShell Active Directory command database for LLM integration"
    }
    search_index = @{}
    commands = @{}
    categories = @{
        user_management = @()
        group_management = @()
        computer_management = @()
        domain_management = @()
        security_management = @()
        replication_management = @()
        service_account_management = @()
        organizational_unit_management = @()
        policy_management = @()
        general_queries = @()
    }
    examples_by_scenario = @{}
    parameter_reference = @{}
}

Write-Host "Processing commands for RAG optimization..." -ForegroundColor Yellow

$processedCount = 0
foreach ($command in $rawData.scraped_commands) {
    if ($command.status -eq "failed") {
        continue
    }
    
    $commandName = $command.page_info.command_name
    $processedCount++
    
    if ($Verbose) {
        Write-Host "  Processing: $commandName" -ForegroundColor Cyan
    }
    
    # Create comprehensive command entry optimized for RAG
    $ragCommand = @{
        # Core identification
        name = $commandName
        verb = ($commandName -split '-')[0]
        noun = ($commandName -split '-')[1]
        
        # Searchable content
        synopsis = $command.synopsis
        description = $command.description
        
        # Categorization for better retrieval
        category = Get-CommandCategory -CommandName $commandName
        use_cases = Get-UseCases -CommandName $commandName -Description $command.description
        
        # Detailed parameter information optimized for LLM context
        parameters = @{}
        
        # Examples formatted for LLM learning
        examples = @()
        
        # Search keywords for better matching
        search_keywords = @()
        
        # Context for LLM
        llm_context = @{
            when_to_use = ""
            common_scenarios = @()
            related_commands = @()
            best_practices = @()
        }
        
        # Original metadata
        source_url = $command.url
        scraped_at = $command.scraped_at
    }
    
    # Process parameters for RAG optimization
    foreach ($paramName in $command.parameters.Keys) {
        $param = $command.parameters[$paramName]
        
        $ragParam = @{
            name = $paramName
            description = $param.description
            type = $param.properties.Type
            mandatory = $param.properties.Mandatory -eq "True"
            position = $param.properties.Position
            default_value = $param.properties."Default value"
            accepts_pipeline = $param.properties."Value from pipeline" -eq "True"
            supports_wildcards = $param.properties."Supports wildcards" -eq "True"
            
            # LLM-friendly context
            usage_context = Get-ParameterUsageContext -ParamName $paramName -Description $param.description
            common_values = Get-CommonParameterValues -ParamName $paramName
        }
        
        $ragCommand.parameters[$paramName] = $ragParam
        
        # Add to global parameter reference
        if (-not $ragDatabase.parameter_reference.ContainsKey($paramName)) {
            $ragDatabase.parameter_reference[$paramName] = @{
                description = $param.description
                used_in_commands = @()
                common_patterns = @()
            }
        }
        $ragDatabase.parameter_reference[$paramName].used_in_commands += $commandName
    }
    
    # Process examples for LLM context
    foreach ($example in $command.examples) {
        $ragExample = @{
            title = $example.title
            description = $example.description
            code_blocks = $example.code_blocks
            
            # Extract scenario and intent for better matching
            scenario = Extract-Scenario -Title $example.title -Description $example.description
            intent = Extract-Intent -Title $example.title -Description $example.description
            
            # LLM-friendly format
            llm_prompt_format = Format-ExampleForLLM -Example $example -CommandName $commandName
        }
        
        $ragCommand.examples += $ragExample
        
        # Add to scenario-based examples
        $scenario = $ragExample.scenario
        if (-not $ragDatabase.examples_by_scenario.ContainsKey($scenario)) {
            $ragDatabase.examples_by_scenario[$scenario] = @()
        }
        $ragDatabase.examples_by_scenario[$scenario] += @{
            command = $commandName
            example = $ragExample
        }
    }
    
    # Generate search keywords
    $ragCommand.search_keywords = Generate-SearchKeywords -Command $ragCommand
    
    # Generate LLM context
    $ragCommand.llm_context = Generate-LLMContext -Command $ragCommand
    
    # Add to appropriate category
    $category = $ragCommand.category
    if ($ragDatabase.categories.ContainsKey($category)) {
        $ragDatabase.categories[$category] += $commandName
    }
    
    # Create search index entries
    Create-SearchIndexEntries -Command $ragCommand -Database ([ref]$ragDatabase)
    
    # Add to main commands collection
    $ragDatabase.commands[$commandName] = $ragCommand
}

# Helper functions for data processing
function Get-CommandCategory {
    param([string]$CommandName)
    
    $noun = ($CommandName -split '-')[1]
    
    switch -Regex ($noun) {
        'User' { return 'user_management' }
        'Group' { return 'group_management' }
        'Computer' { return 'computer_management' }
        'Domain|Forest' { return 'domain_management' }
        'Replication|Site' { return 'replication_management' }
        'ServiceAccount' { return 'service_account_management' }
        'OrganizationalUnit' { return 'organizational_unit_management' }
        'Policy|Authentication' { return 'policy_management' }
        'Account|Object' { return 'security_management' }
        default { return 'general_queries' }
    }
}

function Get-UseCases {
    param([string]$CommandName, [string]$Description)
    
    $useCases = @()
    $verb = ($CommandName -split '-')[0]
    $noun = ($CommandName -split '-')[1]
    
    switch ($verb) {
        'Get' { $useCases += "Query $noun information", "Search for $noun objects", "Retrieve $noun details" }
        'New' { $useCases += "Create new $noun", "Add $noun to directory", "Provision $noun" }
        'Set' { $useCases += "Modify $noun properties", "Update $noun configuration", "Change $noun settings" }
        'Remove' { $useCases += "Delete $noun", "Remove $noun from directory", "Clean up $noun objects" }
        'Add' { $useCases += "Add to $noun", "Associate with $noun", "Include in $noun" }
        'Enable' { $useCases += "Activate $noun", "Turn on $noun", "Enable $noun functionality" }
        'Disable' { $useCases += "Deactivate $noun", "Turn off $noun", "Disable $noun functionality" }
    }
    
    return $useCases
}

function Get-ParameterUsageContext {
    param([string]$ParamName, [string]$Description)
    
    $context = @{
        purpose = $Description
        typical_usage = ""
        examples = @()
    }
    
    switch -Regex ($ParamName) {
        'Identity' { 
            $context.typical_usage = "Specify the target object by name, DN, GUID, or SID"
            $context.examples = @('john.doe', 'CN=John Doe,OU=Users,DC=contoso,DC=com', '{12345678-1234-1234-1234-123456789012}')
        }
        'Filter' { 
            $context.typical_usage = "Use PowerShell expression language to filter results"
            $context.examples = @('Name -like "John*"', 'Enabled -eq $true', 'Department -eq "IT"')
        }
        'Properties' { 
            $context.typical_usage = "Specify additional properties to retrieve"
            $context.examples = @('*', 'Department,Manager,Title', 'MemberOf')
        }
        'Server' { 
            $context.typical_usage = "Specify domain controller to connect to"
            $context.examples = @('dc01.contoso.com', 'contoso.com')
        }
    }
    
    return $context
}

function Get-CommonParameterValues {
    param([string]$ParamName)
    
    switch -Regex ($ParamName) {
        'Enabled' { return @('$true', '$false') }
        'Scope' { return @('Base', 'OneLevel', 'Subtree') }
        'AuthType' { return @('Negotiate', 'Basic') }
        'GroupScope' { return @('DomainLocal', 'Global', 'Universal') }
        'GroupCategory' { return @('Distribution', 'Security') }
        default { return @() }
    }
}

function Extract-Scenario {
    param([string]$Title, [string]$Description)
    
    # Extract common scenarios from example titles
    switch -Regex ($Title) {
        'container|OU' { return 'organizational_unit_operations' }
        'filter|search' { return 'search_and_filter' }
        'properties|attributes' { return 'property_management' }
        'group|member' { return 'group_membership' }
        'password|account' { return 'account_management' }
        'domain|forest' { return 'domain_operations' }
        default { return 'general_usage' }
    }
}

function Extract-Intent {
    param([string]$Title, [string]$Description)
    
    # Extract user intent from examples
    if ($Title -match 'Get all|List all') { return 'list_all' }
    if ($Title -match 'Get.*specific|Get a specified') { return 'get_specific' }
    if ($Title -match 'filter|search') { return 'search_filter' }
    if ($Title -match 'properties') { return 'get_properties' }
    if ($Title -match 'Create|New') { return 'create_new' }
    if ($Title -match 'Modify|Update|Set') { return 'modify_existing' }
    if ($Title -match 'Remove|Delete') { return 'remove_object' }
    
    return 'general_usage'
}

function Format-ExampleForLLM {
    param($Example, [string]$CommandName)
    
    return @{
        task_description = $Example.description
        command_template = $Example.code_blocks[0]
        explanation = "This example shows how to use $CommandName to " + $Example.description.ToLower()
        parameters_used = Extract-ParametersFromCode -Code $Example.code_blocks[0]
    }
}

function Extract-ParametersFromCode {
    param([string]$Code)
    
    $parameters = @()
    $matches = [regex]::Matches($Code, '-(\w+)')
    foreach ($match in $matches) {
        $parameters += $match.Groups[1].Value
    }
    return $parameters | Sort-Object -Unique
}

function Generate-SearchKeywords {
    param($Command)
    
    $keywords = @()
    $keywords += $Command.name
    $keywords += $Command.verb
    $keywords += $Command.noun
    $keywords += $Command.use_cases
    
    # Add parameter names as keywords
    $keywords += $Command.parameters.Keys
    
    # Add words from description
    if ($Command.description) {
        $words = $Command.description -split '\s+' | Where-Object { $_.Length -gt 3 }
        $keywords += $words
    }
    
    return ($keywords | Sort-Object -Unique)
}

function Generate-LLMContext {
    param($Command)
    
    $context = @{
        when_to_use = "Use $($Command.name) when you need to " + ($Command.use_cases -join ', ')
        common_scenarios = $Command.examples | ForEach-Object { $_.scenario } | Sort-Object -Unique
        related_commands = Get-RelatedCommands -CommandName $Command.name
        best_practices = Get-BestPractices -CommandName $Command.name
    }
    
    return $context
}

function Get-RelatedCommands {
    param([string]$CommandName)
    
    $noun = ($CommandName -split '-')[1]
    $verb = ($CommandName -split '-')[0]
    
    $related = @()
    
    # Same noun, different verbs
    @('Get', 'New', 'Set', 'Remove') | ForEach-Object {
        if ($_ -ne $verb) {
            $related += "$_-$noun"
        }
    }
    
    return $related
}

function Get-BestPractices {
    param([string]$CommandName)
    
    $practices = @()
    $verb = ($CommandName -split '-')[0]
    
    switch ($verb) {
        'Get' { 
            $practices += "Use -Filter parameter for better performance with large datasets"
            $practices += "Specify -Properties parameter to get additional attributes"
            $practices += "Use -Server parameter to target specific domain controller"
        }
        'New' { 
            $practices += "Always specify required parameters"
            $practices += "Use -WhatIf parameter to preview changes"
            $practices += "Consider using -PassThru to return the created object"
        }
        'Set' { 
            $practices += "Use -Identity parameter to specify the target object"
            $practices += "Use -WhatIf parameter to preview changes"
            $practices += "Be careful with bulk operations"
        }
        'Remove' { 
            $practices += "Always use -WhatIf parameter first to preview deletions"
            $practices += "Consider using -Confirm parameter for interactive confirmation"
            $practices += "Have a backup strategy before removing objects"
        }
    }
    
    return $practices
}

function Create-SearchIndexEntries {
    param($Command, [ref]$Database)
    
    # Create multiple search index entries for different search patterns
    $commandName = $Command.name
    
    # Index by command name
    $Database.Value.search_index[$commandName.ToLower()] = @{
        type = "command_name"
        command = $commandName
        relevance_score = 100
    }
    
    # Index by verb-noun combination
    $verbNoun = "$($Command.verb.ToLower())-$($Command.noun.ToLower())"
    $Database.Value.search_index[$verbNoun] = @{
        type = "verb_noun"
        command = $commandName
        relevance_score = 90
    }
    
    # Index by use cases
    foreach ($useCase in $Command.use_cases) {
        $key = $useCase.ToLower() -replace '[^\w\s]', '' -replace '\s+', '_'
        if (-not $Database.Value.search_index.ContainsKey($key)) {
            $Database.Value.search_index[$key] = @()
        }
        $Database.Value.search_index[$key] += @{
            type = "use_case"
            command = $commandName
            relevance_score = 80
            context = $useCase
        }
    }
    
    # Index by keywords
    foreach ($keyword in $Command.search_keywords) {
        $key = $keyword.ToLower()
        if ($key.Length -gt 3) {  # Only index meaningful keywords
            if (-not $Database.Value.search_index.ContainsKey($key)) {
                $Database.Value.search_index[$key] = @()
            }
            $Database.Value.search_index[$key] += @{
                type = "keyword"
                command = $commandName
                relevance_score = 60
            }
        }
    }
}

# Finalize metadata
$ragDatabase.metadata.total_commands = $processedCount
$ragDatabase.metadata.total_searchable_entries = $ragDatabase.search_index.Count

Write-Host "Creating additional RAG optimizations..." -ForegroundColor Yellow

# Create command similarity matrix for better recommendations
$ragDatabase.command_similarity = @{}
foreach ($cmd1 in $ragDatabase.commands.Keys) {
    $ragDatabase.command_similarity[$cmd1] = @{}
    foreach ($cmd2 in $ragDatabase.commands.Keys) {
        if ($cmd1 -ne $cmd2) {
            $similarity = Calculate-CommandSimilarity -Command1 $ragDatabase.commands[$cmd1] -Command2 $ragDatabase.commands[$cmd2]
            if ($similarity -gt 0.3) {  # Only store meaningful similarities
                $ragDatabase.command_similarity[$cmd1][$cmd2] = $similarity
            }
        }
    }
}

# Create parameter patterns for LLM guidance
$ragDatabase.parameter_patterns = @{}
foreach ($paramName in $ragDatabase.parameter_reference.Keys) {
    $commands = $ragDatabase.parameter_reference[$paramName].used_in_commands
    $ragDatabase.parameter_patterns[$paramName] = @{
        frequency = $commands.Count
        command_types = $commands | ForEach-Object { ($_ -split '-')[0] } | Group-Object | ForEach-Object { @{ verb = $_.Name; count = $_.Count } }
        usage_examples = Get-ParameterUsageExamples -ParamName $paramName -Commands $commands
    }
}

# Create LLM prompt templates for common scenarios
$ragDatabase.llm_prompt_templates = @{
    search_user = @{
        template = "To search for users in Active Directory, use Get-ADUser with appropriate filters. Common patterns: Get-ADUser -Filter 'Name -like `"*{search_term}*`"' -Properties {properties}"
        parameters = @('Filter', 'Properties', 'SearchBase', 'Server')
        examples = @('Get-ADUser -Filter "Name -like `"John*`"" -Properties Department,Title')
    }
    create_user = @{
        template = "To create a new user in Active Directory, use New-ADUser with required parameters. Essential parameters: Name, SamAccountName, UserPrincipalName"
        parameters = @('Name', 'SamAccountName', 'UserPrincipalName', 'Path', 'AccountPassword')
        examples = @('New-ADUser -Name "John Doe" -SamAccountName "jdoe" -UserPrincipalName "<EMAIL>" -Path "OU=Users,DC=contoso,DC=com"')
    }
    modify_user = @{
        template = "To modify user properties in Active Directory, use Set-ADUser with Identity and the properties to change"
        parameters = @('Identity', 'Department', 'Title', 'Manager', 'Description')
        examples = @('Set-ADUser -Identity "jdoe" -Department "IT" -Title "System Administrator"')
    }
    search_group = @{
        template = "To search for groups in Active Directory, use Get-ADGroup with filters. Common patterns: Get-ADGroup -Filter 'Name -like `"*{search_term}*`"'"
        parameters = @('Filter', 'Properties', 'SearchBase')
        examples = @('Get-ADGroup -Filter "Name -like `"IT*`"" -Properties Description,Members')
    }
    manage_group_membership = @{
        template = "To manage group membership, use Add-ADGroupMember or Remove-ADGroupMember with group Identity and Members"
        parameters = @('Identity', 'Members')
        examples = @('Add-ADGroupMember -Identity "IT-Group" -Members "jdoe"', 'Remove-ADGroupMember -Identity "IT-Group" -Members "jdoe"')
    }
}

# Helper function for command similarity calculation
function Calculate-CommandSimilarity {
    param($Command1, $Command2)

    $score = 0

    # Same verb = +0.3
    if ($Command1.verb -eq $Command2.verb) { $score += 0.3 }

    # Same noun = +0.4
    if ($Command1.noun -eq $Command2.noun) { $score += 0.4 }

    # Same category = +0.2
    if ($Command1.category -eq $Command2.category) { $score += 0.2 }

    # Common parameters = +0.1 per common parameter (max 0.3)
    $commonParams = ($Command1.parameters.Keys | Where-Object { $Command2.parameters.ContainsKey($_) }).Count
    $score += [Math]::Min(0.3, $commonParams * 0.1)

    return $score
}

function Get-ParameterUsageExamples {
    param([string]$ParamName, [array]$Commands)

    $examples = @()

    # Get examples from the first few commands that use this parameter
    $sampleCommands = $Commands | Select-Object -First 3
    foreach ($cmdName in $sampleCommands) {
        if ($ragDatabase.commands.ContainsKey($cmdName)) {
            $cmd = $ragDatabase.commands[$cmdName]
            foreach ($example in $cmd.examples) {
                if ($example.code_blocks -match "-$ParamName") {
                    $examples += @{
                        command = $cmdName
                        usage = $example.code_blocks[0]
                        context = $example.description
                    }
                    break
                }
            }
        }
    }

    return $examples
}

Write-Host "Saving RAG-optimized database..." -ForegroundColor Yellow

# Save the RAG-optimized database
$json = $ragDatabase | ConvertTo-Json -Depth 25
$json | Out-File -FilePath $OutputFile -Encoding UTF8

# Display comprehensive summary
Write-Host "`n" + "="*70 -ForegroundColor Green
Write-Host "RAG OPTIMIZATION COMPLETE!" -ForegroundColor Green
Write-Host "="*70 -ForegroundColor Green
Write-Host "Input file: $InputFile" -ForegroundColor White
Write-Host "Output file: $OutputFile" -ForegroundColor White
Write-Host "Commands processed: $processedCount" -ForegroundColor Cyan
Write-Host "Search index entries: $($ragDatabase.search_index.Count)" -ForegroundColor Cyan
Write-Host "Parameter references: $($ragDatabase.parameter_reference.Count)" -ForegroundColor Cyan
Write-Host "Example scenarios: $($ragDatabase.examples_by_scenario.Count)" -ForegroundColor Cyan
Write-Host "LLM prompt templates: $($ragDatabase.llm_prompt_templates.Count)" -ForegroundColor Cyan
Write-Host "File size: $([math]::Round((Get-Item $OutputFile).Length / 1MB, 2)) MB" -ForegroundColor Yellow

Write-Host "`nRAG Database Features:" -ForegroundColor Yellow
Write-Host "✓ Optimized search indexing with relevance scoring" -ForegroundColor Green
Write-Host "✓ Command categorization for better retrieval" -ForegroundColor Green
Write-Host "✓ Parameter usage context and examples" -ForegroundColor Green
Write-Host "✓ LLM-friendly example formatting" -ForegroundColor Green
Write-Host "✓ Command similarity matrix for recommendations" -ForegroundColor Green
Write-Host "✓ Scenario-based example grouping" -ForegroundColor Green
Write-Host "✓ Pre-built LLM prompt templates" -ForegroundColor Green
Write-Host "✓ Best practices and usage guidance" -ForegroundColor Green

Write-Host "`nThe RAG database is now ready for integration with your language model!" -ForegroundColor Green
Write-Host "You can use this data to:" -ForegroundColor White
Write-Host "  • Search for relevant commands based on user queries" -ForegroundColor Gray
Write-Host "  • Provide detailed parameter information to the LLM" -ForegroundColor Gray
Write-Host "  • Include relevant examples for context" -ForegroundColor Gray
Write-Host "  • Use prompt templates for common scenarios" -ForegroundColor Gray
Write-Host "  • Get command recommendations based on similarity" -ForegroundColor Gray
