{"built": [{"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:02.588", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:02.600"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:02.608", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:02.615"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:02.576", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:02.617"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:02.601", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:02.635"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:02.643", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:02.649"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:02.644", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:02.663"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:02.667", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:02.674"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpprotocollib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:02.698", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:02.702"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:02.708", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:02.710"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:02.721", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:02.724"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:03.009", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:04.522"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.527", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:04.529"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.533", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:04.536"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.542", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:04.544"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS_MISC", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.550", "pip start time": "0-00-00 00:00:00.000", "pip end time": "0-00-00 00:00:00.000", "job end time": "2025-07-25 04:41:04.560"}], "i/o": [{"bytes read": 0, "bytes written": 0, "bytes transferred (non-read/write)": 0, "read operation count": 0, "write operation count": 0, "non-read/write operations count": 0}], "peak memory": [{"job": 0, "process": 0}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.841", "pip start time": "2025-07-25 04:41:04.883", "pip end time": "2025-07-25 04:41:06.687", "job end time": "2025-07-25 04:41:06.711"}], "i/o": [{"bytes read": 1781900, "bytes written": 641059, "bytes transferred (non-read/write)": 2068580, "read operation count": 573, "write operation count": 49, "non-read/write operations count": 11255}], "peak memory": [{"job": 61345792, "process": 31354880}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.844", "pip start time": "2025-07-25 04:41:04.880", "pip end time": "2025-07-25 04:41:06.687", "job end time": "2025-07-25 04:41:06.715"}], "i/o": [{"bytes read": 1776753, "bytes written": 474231, "bytes transferred (non-read/write)": 2067776, "read operation count": 572, "write operation count": 49, "non-read/write operations count": 11327}], "peak memory": [{"job": 60727296, "process": 30765056}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.828", "pip start time": "2025-07-25 04:41:04.883", "pip end time": "2025-07-25 04:41:06.717", "job end time": "2025-07-25 04:41:06.733"}], "i/o": [{"bytes read": 1772682, "bytes written": 447485, "bytes transferred (non-read/write)": 2091830, "read operation count": 571, "write operation count": 47, "non-read/write operations count": 11571}], "peak memory": [{"job": 62373888, "process": 31817728}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.839", "pip start time": "2025-07-25 04:41:04.883", "pip end time": "2025-07-25 04:41:06.747", "job end time": "2025-07-25 04:41:06.766"}], "i/o": [{"bytes read": 1773011, "bytes written": 146665, "bytes transferred (non-read/write)": 2074058, "read operation count": 571, "write operation count": 15836, "non-read/write operations count": 11388}], "peak memory": [{"job": 61349888, "process": 31084544}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.832", "pip start time": "2025-07-25 04:41:04.883", "pip end time": "2025-07-25 04:41:06.766", "job end time": "2025-07-25 04:41:06.781"}], "i/o": [{"bytes read": 1778154, "bytes written": 502388, "bytes transferred (non-read/write)": 2085190, "read operation count": 593, "write operation count": 54, "non-read/write operations count": 11621}], "peak memory": [{"job": 62119936, "process": 31002624}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.837", "pip start time": "2025-07-25 04:41:04.883", "pip end time": "2025-07-25 04:41:06.784", "job end time": "2025-07-25 04:41:06.797"}], "i/o": [{"bytes read": 1779349, "bytes written": 449774, "bytes transferred (non-read/write)": 2097620, "read operation count": 592, "write operation count": 54, "non-read/write operations count": 11786}], "peak memory": [{"job": 62029824, "process": 31555584}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.831", "pip start time": "2025-07-25 04:41:04.881", "pip end time": "2025-07-25 04:41:06.831", "job end time": "2025-07-25 04:41:06.843"}], "i/o": [{"bytes read": 1773772, "bytes written": 3473519, "bytes transferred (non-read/write)": 2090002, "read operation count": 573, "write operation count": 15867, "non-read/write operations count": 11687}], "peak memory": [{"job": 61603840, "process": 31416320}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.834", "pip start time": "2025-07-25 04:41:04.883", "pip end time": "2025-07-25 04:41:07.306", "job end time": "2025-07-25 04:41:07.318"}], "i/o": [{"bytes read": 6055450, "bytes written": 315712, "bytes transferred (non-read/write)": 2184602, "read operation count": 998, "write operation count": 16014, "non-read/write operations count": 13984}], "peak memory": [{"job": 60989440, "process": 31051776}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS0", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:04.829", "pip start time": "2025-07-25 04:41:04.880", "pip end time": "2025-07-25 04:41:07.933", "job end time": "2025-07-25 04:41:07.945"}], "i/o": [{"bytes read": 3808848, "bytes written": 157681, "bytes transferred (non-read/write)": 2251692, "read operation count": 2622, "write operation count": 15977, "non-read/write operations count": 21009}], "peak memory": [{"job": 78307328, "process": 31436800}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:08.897", "pip start time": "2025-07-25 04:41:08.944", "pip end time": "2025-07-25 04:41:12.931", "job end time": "2025-07-25 04:41:12.949"}], "i/o": [{"bytes read": 18053987, "bytes written": 3901188, "bytes transferred (non-read/write)": 3401594, "read operation count": 1971, "write operation count": 20801, "non-read/write operations count": 20639}], "peak memory": [{"job": 83861504, "process": 53571584}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:08.903", "pip start time": "2025-07-25 04:41:08.943", "pip end time": "2025-07-25 04:41:14.609", "job end time": "2025-07-25 04:41:14.631"}], "i/o": [{"bytes read": 21033816, "bytes written": 11138697, "bytes transferred (non-read/write)": 3442460, "read operation count": 2802, "write operation count": 4946, "non-read/write operations count": 21954}], "peak memory": [{"job": 209534976, "process": 178814976}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:08.900", "pip start time": "2025-07-25 04:41:08.944", "pip end time": "2025-07-25 04:41:14.633", "job end time": "2025-07-25 04:41:14.651"}], "i/o": [{"bytes read": 22346987, "bytes written": 11240073, "bytes transferred (non-read/write)": 3475514, "read operation count": 2867, "write operation count": 21886, "non-read/write operations count": 22406}], "peak memory": [{"job": 209526784, "process": 178671616}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:08.893", "pip start time": "2025-07-25 04:41:08.943", "pip end time": "2025-07-25 04:41:15.071", "job end time": "2025-07-25 04:41:15.087"}], "i/o": [{"bytes read": 13948156, "bytes written": 579032, "bytes transferred (non-read/write)": 2370062, "read operation count": 2753, "write operation count": 16975, "non-read/write operations count": 28172}], "peak memory": [{"job": 92897280, "process": 62873600}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:08.892", "pip start time": "2025-07-25 04:41:08.940", "pip end time": "2025-07-25 04:41:15.352", "job end time": "2025-07-25 04:41:15.367"}], "i/o": [{"bytes read": 31439355, "bytes written": 35620387, "bytes transferred (non-read/write)": 5266488, "read operation count": 4029, "write operation count": 9027, "non-read/write operations count": 30280}], "peak memory": [{"job": 196481024, "process": 165785600}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:08.894", "pip start time": "2025-07-25 04:41:08.939", "pip end time": "2025-07-25 04:41:15.360", "job end time": "2025-07-25 04:41:15.373"}], "i/o": [{"bytes read": 31443856, "bytes written": 35703231, "bytes transferred (non-read/write)": 5262830, "read operation count": 4048, "write operation count": 26286, "non-read/write operations count": 30110}], "peak memory": [{"job": 194408448, "process": 165863424}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpprotocollib", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:08.899", "pip start time": "2025-07-25 04:41:08.944", "pip end time": "2025-07-25 04:41:17.143", "job end time": "2025-07-25 04:41:17.156"}], "i/o": [{"bytes read": 39295830, "bytes written": 22434788, "bytes transferred (non-read/write)": 3516690, "read operation count": 4823, "write operation count": 23775, "non-read/write operations count": 26531}], "peak memory": [{"job": 208031744, "process": 177577984}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:08.905", "pip start time": "2025-07-25 04:41:08.949", "pip end time": "2025-07-25 04:41:27.246", "job end time": "2025-07-25 04:41:27.268"}], "i/o": [{"bytes read": 154125468, "bytes written": 76432834, "bytes transferred (non-read/write)": 3923914, "read operation count": 15967, "write operation count": 31123, "non-read/write operations count": 54491}], "peak memory": [{"job": 209604608, "process": 178831360}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:08.902", "pip start time": "2025-07-25 04:41:08.912", "pip end time": "2025-07-25 04:41:34.333", "job end time": "2025-07-25 04:41:35.393"}], "i/o": [{"bytes read": 380183732, "bytes written": 279397467, "bytes transferred (non-read/write)": 9766123, "read operation count": 110240, "write operation count": 35255, "non-read/write operations count": 565404}], "peak memory": [{"job": 502779904, "process": 212631552}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:35.406", "pip start time": "2025-07-25 04:41:35.434", "pip end time": "2025-07-25 04:41:40.975", "job end time": "2025-07-25 04:41:40.987"}], "i/o": [{"bytes read": 50738570, "bytes written": 88045986, "bytes transferred (non-read/write)": 5336612, "read operation count": 7082, "write operation count": 29313, "non-read/write operations count": 34557}], "peak memory": [{"job": 274960384, "process": 244338688}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS1", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:35.407", "pip start time": "2025-07-25 04:41:35.433", "pip end time": "2025-07-25 04:41:50.357", "job end time": "2025-07-25 04:41:50.376"}], "i/o": [{"bytes read": 120188064, "bytes written": 229837870, "bytes transferred (non-read/write)": 5434224, "read operation count": 20620, "write operation count": 43045, "non-read/write operations count": 55151}], "peak memory": [{"job": 283680768, "process": 254517248}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS2", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:50.578", "pip start time": "2025-07-25 04:41:50.616", "pip end time": "2025-07-25 04:41:54.269", "job end time": "2025-07-25 04:41:54.291"}], "i/o": [{"bytes read": 6540166, "bytes written": 285455, "bytes transferred (non-read/write)": 4226691, "read operation count": 2080, "write operation count": 34079, "non-read/write operations count": 27141}], "peak memory": [{"job": 99692544, "process": 35524608}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS2", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:50.584", "pip start time": "2025-07-25 04:41:50.613", "pip end time": "2025-07-25 04:41:56.960", "job end time": "2025-07-25 04:41:56.980"}], "i/o": [{"bytes read": 11014715, "bytes written": 7529044, "bytes transferred (non-read/write)": 4353212, "read operation count": 2365, "write operation count": 36129, "non-read/write operations count": 31069}], "peak memory": [{"job": 320372736, "process": 288890880}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller", "pass": "PASS2", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:50.585", "pip start time": "2025-07-25 04:41:50.621", "pip end time": "2025-07-25 04:41:57.761", "job end time": "2025-07-25 04:41:57.782"}], "i/o": [{"bytes read": 12917660, "bytes written": 3145420, "bytes transferred (non-read/write)": 4461740, "read operation count": 5056, "write operation count": 36525, "non-read/write operations count": 41210}], "peak memory": [{"job": 268574720, "process": 237379584}], "network": [{"bandwidth": 0}]}]}, {"name": "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS2", "returncode": 0, "stats": [{"execution times": [{"job start time": "2025-07-25 04:41:50.579", "pip start time": "2025-07-25 04:41:50.613", "pip end time": "2025-07-25 04:42:02.574", "job end time": "2025-07-25 04:42:02.597"}], "i/o": [{"bytes read": 48197916, "bytes written": 54683928, "bytes transferred (non-read/write)": 4484338, "read operation count": 15440, "write operation count": 38811, "non-read/write operations count": 54694}], "peak memory": [{"job": 920752128, "process": 889511936}], "network": [{"bandwidth": 0}]}]}]}