using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;

namespace NetRagService;

class Program
{
    static async Task Main(string[] args)
    {
        // Log startup to Event Log for debugging
        if (OperatingSystem.IsWindows())
        {
            LogToEventLog("NetRag service starting with args: " + string.Join(" ", args), System.Diagnostics.EventLogEntryType.Information);
        }

        // Check if running as Windows service or console application
        // Service mode: no arguments or --service argument, or when not running interactively
        bool isWindowsService = RuntimeInformation.IsOSPlatform(OSPlatform.Windows) &&
                               (args.Length == 0 || args.Contains("--service") || !Environment.UserInteractive);

        if (isWindowsService)
        {
            if (OperatingSystem.IsWindows())
            {
                LogToEventLog("Running as Windows service", System.Diagnostics.EventLogEntryType.Information);
                await RunAsWindowsService(args);
            }
            else
            {
                throw new PlatformNotSupportedException("Windows service mode is only supported on Windows.");
            }
        }
        else
        {
            if (OperatingSystem.IsWindows())
            {
                LogToEventLog("Running as console application", System.Diagnostics.EventLogEntryType.Information);
            }
            await RunAsConsoleApplication(args);
        }
    }

    [SupportedOSPlatform("windows")]
    static async Task RunAsWindowsService(string[] args)
    {
        LogToEventLog("RunAsWindowsService called", System.Diagnostics.EventLogEntryType.Information);

        var builder = Host.CreateDefaultBuilder(args);

        LogToEventLog("Configuring as Windows service", System.Diagnostics.EventLogEntryType.Information);

        // Configure as Windows service
        builder.UseWindowsService(options =>
        {
            options.ServiceName = "NetRagService";
        });

        LogToEventLog("Configuring registry-based configuration", System.Diagnostics.EventLogEntryType.Information);

        // Build configuration - registry only
        builder.ConfigureAppConfiguration((context, config) =>
        {
            config.AddAimxServiceRegistry();
        });

        LogToEventLog("Configuring services", System.Diagnostics.EventLogEntryType.Information);

        // Configure services
        builder.ConfigureServices((context, services) =>
        {
            LogToEventLog("Binding configuration from registry", System.Diagnostics.EventLogEntryType.Information);

            // Bind configuration
            var ragConfig = new RagConfiguration();
            context.Configuration.Bind(ragConfig);
            services.AddSingleton(ragConfig);

            LogToEventLog("Registering NetRagWindowsService", System.Diagnostics.EventLogEntryType.Information);

            // Register the Windows service
            services.AddHostedService<NetRagWindowsService>();
        });

        // Configure logging for Windows service
        builder.ConfigureLogging((context, logging) =>
        {
            logging.ClearProviders();
            logging.AddEventLog(options =>
            {
                options.SourceName = "NetRag Service";
                options.LogName = "Application";
            });
            logging.SetMinimumLevel(LogLevel.Information);
        });

        LogToEventLog("Building host", System.Diagnostics.EventLogEntryType.Information);

        var host = builder.Build();

        LogToEventLog("Starting host", System.Diagnostics.EventLogEntryType.Information);

        await host.RunAsync();

        LogToEventLog("Host completed", System.Diagnostics.EventLogEntryType.Information);
    }

    static async Task RunAsConsoleApplication(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        // Build configuration - registry only for consistency with Windows system services
        builder.Configuration.AddAimxServiceRegistry();

        // Bind configuration (registry values will automatically override appsettings.json)
        var ragConfig = new RagConfiguration();
        builder.Configuration.Bind(ragConfig);
        builder.Services.AddSingleton(ragConfig);
        builder.Services.AddSingleton(ragConfig.FoundryLocal);
        builder.Services.AddSingleton(ragConfig.Embedding);
        builder.Services.AddSingleton(ragConfig.DocumentIngestion);
        builder.Services.AddSingleton(ragConfig.InMemoryVectorStore);
        builder.Services.AddSingleton(ragConfig.McpService);

        // Configure Semantic Kernel
        var kernelBuilder = Kernel.CreateBuilder();

        // Add BERT ONNX embedding generator
        kernelBuilder.AddBertOnnxEmbeddingGenerator(
            ragConfig.Embedding.ModelPath,
            ragConfig.Embedding.VocabPath);

        // Add OpenAI chat completion (Foundry Local)
        kernelBuilder.AddOpenAIChatCompletion(
            ragConfig.FoundryLocal.ModelName,
            new Uri(ragConfig.FoundryLocal.BaseUrl),
            apiKey: "",
            serviceId: ragConfig.FoundryLocal.ServiceId);

        var kernel = kernelBuilder.Build();
        builder.Services.AddSingleton(kernel);

        // Get services from kernel
        var embeddingService = kernel.GetRequiredService<Microsoft.Extensions.AI.IEmbeddingGenerator<string, Microsoft.Extensions.AI.Embedding<float>>>();
        var chatService = kernel.GetRequiredService<Microsoft.SemanticKernel.ChatCompletion.IChatCompletionService>(serviceKey: ragConfig.FoundryLocal.ServiceId);

        builder.Services.AddSingleton(embeddingService);
        builder.Services.AddSingleton(chatService);

        // Register services
        builder.Services.AddSingleton<VectorStoreService>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<VectorStoreService>>();
            var persistenceFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, ragConfig.InMemoryVectorStore.PersistenceFilePath);
            return new VectorStoreService(logger, persistenceFilePath);
        });
        builder.Services.AddSingleton<DocumentIngestionService>();
        builder.Services.AddSingleton<RagQueryService>();
        builder.Services.AddSingleton<McpToolService>();

        // Register PowerShell command services
        builder.Services.AddSingleton<PowerShellCommandIngestionService>();
        builder.Services.AddSingleton<PowerShellCommandSearchService>();

        // Note: MicrosoftLearnDataService removed - no automatic data ingestion
        // Use the separate DatabaseConverter utility to create database.bin files

        // Add controllers and API services
        builder.Services.AddControllers();

        // Configure CORS for local development
        builder.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(policy =>
            {
                policy.AllowAnyOrigin()
                      .AllowAnyMethod()
                      .AllowAnyHeader();
            });
        });

        // Configure logging
        builder.Logging.ClearProviders();
        builder.Logging.AddConsole();
        builder.Logging.SetMinimumLevel(LogLevel.Information);

        var app = builder.Build();

        // Configure the HTTP request pipeline

        app.UseCors();
        app.UseRouting();
        app.MapControllers();

        // Initialize vector store
        var vectorStoreService = app.Services.GetRequiredService<VectorStoreService>();
        await vectorStoreService.InitializeAsync(ragConfig.Embedding.VectorSize);

        // Load existing data from disk if available
        await vectorStoreService.LoadFromDiskAsync();

        var vectorCount = await vectorStoreService.GetVectorCountAsync();
        Console.WriteLine($"Loaded {vectorCount} vectors from persistent storage");

        Console.WriteLine("MCP Tools RAG Service starting...");
        Console.WriteLine($"Service will be available at: http://{ragConfig.McpService.Host}:{ragConfig.McpService.Port}");
        Console.WriteLine("Press Ctrl+C to stop the service");

        await app.RunAsync();
    }

    /// <summary>
    /// Helper method to log messages to Windows Event Log
    /// </summary>
    /// <param name="message">Message to log</param>
    /// <param name="entryType">Type of log entry</param>
    [SupportedOSPlatform("windows")]
    private static void LogToEventLog(string message, System.Diagnostics.EventLogEntryType entryType)
    {
        try
        {
            using var eventLog = new System.Diagnostics.EventLog("Application");
            eventLog.Source = "NetRag Service";
            eventLog.WriteEntry(message, entryType);
        }
        catch
        {
            // If we can't log to event log, there's not much we can do
            // This prevents infinite recursion if logging itself fails
        }
    }
}
