using Microsoft.Extensions.AI;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace NetRagService;

/// <summary>
/// Enhanced search service for RAG-optimized PowerShell Active Directory commands
/// Supports hybrid search with vector similarity and keyword filtering
/// </summary>
public class EnhancedPowerShellCommandSearchService
{
    private readonly IEmbeddingGenerator<string, Embedding<float>> _embeddingService;
    private readonly VectorStoreService _vectorStoreService;
    private readonly ILogger<EnhancedPowerShellCommandSearchService> _logger;

    public EnhancedPowerShellCommandSearchService(
        IEmbeddingGenerator<string, Embedding<float>> embeddingService,
        VectorStoreService vectorStoreService,
        ILogger<EnhancedPowerShellCommandSearchService> logger)
    {
        _embeddingService = embeddingService;
        _vectorStoreService = vectorStoreService;
        _logger = logger;
    }

    /// <summary>
    /// Enhanced semantic search with optional filtering
    /// </summary>
    public async Task<List<EnhancedPowerShellCommandResult>> SearchAsync(
        string query, 
        int limit = 5, 
        SearchFilters? filters = null)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return new List<EnhancedPowerShellCommandResult>();
        }

        try
        {
            _logger.LogInformation("Enhanced search for PowerShell commands with query: '{Query}', limit: {Limit}", query, limit);

            // Generate embedding for the search query
            var queryEmbeddingResult = await _embeddingService.GenerateAsync(query);
            var queryEmbedding = queryEmbeddingResult.Vector;

            // Get more results initially for filtering
            var searchLimit = filters != null ? Math.Max(limit * 3, 50) : limit;
            var searchResults = await _vectorStoreService.SearchAsync(queryEmbedding, searchLimit);

            // Apply filters if specified
            if (filters != null)
            {
                searchResults = ApplyFilters(searchResults, filters);
            }

            // Convert to enhanced command results
            var commandResults = searchResults.Take(limit).Select(result => new EnhancedPowerShellCommandResult
            {
                CommandName = GetMetadataValue(result.Metadata, "command_name", "Unknown"),
                Verb = GetMetadataValue(result.Metadata, "verb", ""),
                Noun = GetMetadataValue(result.Metadata, "noun", ""),
                Category = GetMetadataValue(result.Metadata, "category", ""),
                PrimaryPurpose = GetMetadataValue(result.Metadata, "primary_purpose", ""),
                Description = GetMetadataValue(result.Metadata, "description", ""),
                Score = result.Score,
                
                // RAG-specific fields
                RagDocument = GetMetadataValue(result.Metadata, "rag_document", ""),
                Keywords = DeserializeStringList(GetMetadataValue(result.Metadata, "keywords", "[]")),
                
                // Parameter information
                RequiredParameters = DeserializeStringList(GetMetadataValue(result.Metadata, "required_parameters", "[]")),
                OptionalParameters = DeserializeStringList(GetMetadataValue(result.Metadata, "optional_parameters", "[]")),
                ParameterCount = GetMetadataInt(result.Metadata, "parameter_count", 0),
                ParameterNames = GetMetadataValue(result.Metadata, "parameter_names", ""),
                
                // Example information
                ExampleCount = GetMetadataInt(result.Metadata, "example_count", 0),
                ExampleScenarios = DeserializeStringList(GetMetadataValue(result.Metadata, "example_scenarios", "[]")),
                
                // Full structured data for LLM context
                Parameters = DeserializeParameters(GetMetadataValue(result.Metadata, "parameters_full", "[]")),
                Examples = DeserializeExamples(GetMetadataValue(result.Metadata, "examples_full", "[]")),
                
                // Search metadata
                Id = result.Id,
                FullText = GetMetadataValue(result.Metadata, "full_text", ""),
                Metadata = result.Metadata
            }).ToList();

            _logger.LogInformation("Found {ResultCount} matching PowerShell commands", commandResults.Count);

            return commandResults;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search PowerShell commands for query: {Query}", query);
            throw;
        }
    }

    /// <summary>
    /// Search by category for browsing commands
    /// </summary>
    public async Task<List<EnhancedPowerShellCommandResult>> SearchByCategoryAsync(string category, int limit = 20)
    {
        var filters = new SearchFilters { Category = category };
        
        // Use a generic query that will match most commands, then filter by category
        return await SearchAsync("PowerShell Active Directory command", limit, filters);
    }

    /// <summary>
    /// Search by verb (Get, Set, New, Remove, etc.)
    /// </summary>
    public async Task<List<EnhancedPowerShellCommandResult>> SearchByVerbAsync(string verb, int limit = 20)
    {
        var filters = new SearchFilters { Verb = verb };
        
        return await SearchAsync($"{verb} Active Directory", limit, filters);
    }

    /// <summary>
    /// Get command by exact name
    /// </summary>
    public async Task<EnhancedPowerShellCommandResult?> GetCommandByNameAsync(string commandName)
    {
        if (string.IsNullOrWhiteSpace(commandName))
        {
            return null;
        }

        try
        {
            // Search with the exact command name
            var results = await SearchAsync(commandName, 10);
            
            // Find exact match
            var exactMatch = results.FirstOrDefault(r => 
                string.Equals(r.CommandName, commandName, StringComparison.OrdinalIgnoreCase));
            
            if (exactMatch != null)
            {
                _logger.LogInformation("Found exact match for command: {CommandName}", commandName);
                return exactMatch;
            }

            _logger.LogWarning("PowerShell command not found: {CommandName}", commandName);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get PowerShell command by name: {CommandName}", commandName);
            throw;
        }
    }

    /// <summary>
    /// Get all available categories
    /// </summary>
    public async Task<List<string>> GetCategoriesAsync()
    {
        try
        {
            // Get a large sample of commands to extract categories
            var allResults = await _vectorStoreService.SearchAsync(new float[768], 500);
            
            var categories = allResults
                .Where(r => r.Metadata.ContainsKey("category"))
                .Select(r => r.Metadata["category"].ToString() ?? "")
                .Where(cat => !string.IsNullOrEmpty(cat))
                .Distinct()
                .OrderBy(cat => cat)
                .ToList();

            _logger.LogInformation("Retrieved {CategoryCount} unique categories", categories.Count);
            return categories;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get categories");
            throw;
        }
    }

    /// <summary>
    /// Get search statistics
    /// </summary>
    public async Task<Dictionary<string, object>> GetEnhancedSearchStatsAsync()
    {
        try
        {
            var vectorCount = await _vectorStoreService.GetVectorCountAsync();
            var categories = await GetCategoriesAsync();
            
            return new Dictionary<string, object>
            {
                ["total_commands"] = vectorCount,
                ["categories"] = categories,
                ["category_count"] = categories.Count,
                ["data_type"] = "rag_optimized_powershell_commands",
                ["features"] = new[]
                {
                    "semantic_search",
                    "keyword_filtering", 
                    "category_browsing",
                    "hybrid_search",
                    "llm_context_ready"
                },
                ["last_updated"] = DateTime.UtcNow.ToString("O")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get enhanced search statistics");
            throw;
        }
    }

    private List<SearchResult> ApplyFilters(List<SearchResult> results, SearchFilters filters)
    {
        var filtered = results.AsEnumerable();

        if (!string.IsNullOrEmpty(filters.Category))
        {
            filtered = filtered.Where(r => 
                GetMetadataValue(r.Metadata, "category", "").Contains(filters.Category, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(filters.Verb))
        {
            filtered = filtered.Where(r => 
                string.Equals(GetMetadataValue(r.Metadata, "verb", ""), filters.Verb, StringComparison.OrdinalIgnoreCase));
        }

        if (!string.IsNullOrEmpty(filters.ObjectType))
        {
            filtered = filtered.Where(r => 
                GetMetadataValue(r.Metadata, "object_type", "").Contains(filters.ObjectType, StringComparison.OrdinalIgnoreCase));
        }

        if (filters.RequiredOnly)
        {
            filtered = filtered.Where(r => 
                GetMetadataInt(r.Metadata, "required_param_count", 0) > 0);
        }

        if (!string.IsNullOrEmpty(filters.Complexity))
        {
            filtered = filtered.Where(r => 
                string.Equals(GetMetadataValue(r.Metadata, "complexity", ""), filters.Complexity, StringComparison.OrdinalIgnoreCase));
        }

        return filtered.ToList();
    }

    private string GetMetadataValue(Dictionary<string, object> metadata, string key, string defaultValue)
    {
        return metadata.TryGetValue(key, out var value) ? value?.ToString() ?? defaultValue : defaultValue;
    }

    private int GetMetadataInt(Dictionary<string, object> metadata, string key, int defaultValue)
    {
        return metadata.TryGetValue(key, out var value) && int.TryParse(value?.ToString(), out var intValue) 
            ? intValue : defaultValue;
    }

    private List<string> DeserializeStringList(string json)
    {
        try
        {
            return JsonSerializer.Deserialize<List<string>>(json) ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }

    private List<ParameterData> DeserializeParameters(string json)
    {
        try
        {
            return JsonSerializer.Deserialize<List<ParameterData>>(json) ?? new List<ParameterData>();
        }
        catch
        {
            return new List<ParameterData>();
        }
    }

    private List<ExampleData> DeserializeExamples(string json)
    {
        try
        {
            return JsonSerializer.Deserialize<List<ExampleData>>(json) ?? new List<ExampleData>();
        }
        catch
        {
            return new List<ExampleData>();
        }
    }
}

/// <summary>
/// Enhanced result structure with all RAG-optimized fields
/// </summary>
public class EnhancedPowerShellCommandResult
{
    // Core command information
    public string CommandName { get; set; } = string.Empty;
    public string Verb { get; set; } = string.Empty;
    public string Noun { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string PrimaryPurpose { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public float Score { get; set; }
    
    // RAG-specific fields
    public string RagDocument { get; set; } = string.Empty;
    public List<string> Keywords { get; set; } = new();
    
    // Parameter information
    public List<string> RequiredParameters { get; set; } = new();
    public List<string> OptionalParameters { get; set; } = new();
    public int ParameterCount { get; set; }
    public string ParameterNames { get; set; } = string.Empty;
    
    // Example information
    public int ExampleCount { get; set; }
    public List<string> ExampleScenarios { get; set; } = new();
    
    // Full structured data for LLM context
    public List<ParameterData> Parameters { get; set; } = new();
    public List<ExampleData> Examples { get; set; } = new();
    
    // Search metadata
    public string Id { get; set; } = string.Empty;
    public string FullText { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Search filters for hybrid search functionality
/// </summary>
public class SearchFilters
{
    public string? Category { get; set; }
    public string? Verb { get; set; }
    public string? ObjectType { get; set; }
    public bool RequiredOnly { get; set; }
    public string? Complexity { get; set; } // "simple", "moderate", "complex"
}
