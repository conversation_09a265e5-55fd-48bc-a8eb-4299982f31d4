{"building": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS0", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\\os\\src\\tools\\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS0", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\\os\\src\\tools\\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS0", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\\os\\src\\tools\\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\..\\..\\cpprestsdk\\consume.inc", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS0", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\\os\\src\\tools\\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll\\..\\..\\cpprestsdk\\consume.inc", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS0", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\\os\\src\\tools\\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl\\makefile.inc", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS0", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\\os\\src\\tools\\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib", "pass": "PASS0", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\\os\\src\\tools\\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS0", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\\os\\src\\tools\\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS0", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. BUILD_PASS=PASS0 /nologo /f e:\\os\\src\\tools\\makefile.def NOLINK=1 PASS0ONLY=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS1", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "dependencies": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS0"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS0"}], "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib\\..\\aimxclient.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib\\..\\aimxrpcclient.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib\\..\\memory.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS1", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "dependencies": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS0"}], "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell\\aimxservercmdlets.cs", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell\\foundrylocalwizard.cs", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell\\nativemethods.cs", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS1", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "dependencies": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS0"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS0"}], "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll\\..\\aimxclient.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll\\..\\aimxrpcclient.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll\\..\\memory.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller", "pass": "PASS1", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller\\main.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\mcpprotocollib", "pass": "PASS1", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\mcpprotocollib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpprotocollib\\mcpjsonrpc.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpprotocollib\\mcpserverutils.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpprotocollib\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib", "pass": "PASS1", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "dependencies": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS0"}], "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib\\..\\hellomcpserver.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk", "pass": "PASS1", "cmd": "msbuild.cmd \"vcpkg.proj\" /nologo /p:BuildingInSeparatePasses=true /p:BuildingWithBuildExe=true /clp:NoSummary /verbosity:normal /Target:BuildCompiled /p:Pass=Compile /p:ObjectPath=e:\\os\\obj\\amd64fre\\onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk\\ /p:TARGET_PLATFORM=windows /p:CODE_SETS_BUILDING=cs_windows:cs_xbox:cs_phone /p:CODE_SETS_TAGGED=cs_windows:cs_xbox", "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk\\vcpkg.proj"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS1", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "dependencies": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS0"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS0"}], "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib\\llmclientlib.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS1", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "dependencies": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS0"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS0"}], "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\admcpsvr.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\getadcomputertool.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\getaddomaintool.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\getadforesttool.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\getadgrouptool.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\getadusertool.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\powershellfilterparser.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS1", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "dependencies": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS0"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS0"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk", "pass": "PASS1"}], "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll\\..\\..\\cpprestsdk\\consume.inc", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll\\aimxservice.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll\\dllmain.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS1", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS1 NOLINK=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "dependencies": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\idl", "pass": "PASS0"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS0"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk", "pass": "PASS1"}], "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\..\\..\\cpprestsdk\\consume.inc", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\aimxllmconfig.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\aimxrpcserver.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\conversationmanager.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\inprocessmcpserverbase.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\inprocessmcputils.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\llminfer.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\mcpstdioclient.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\mcpsvrmgr.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\mcptoolmanager.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\orchestrator.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\planner.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\ragservicemanager.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\requesthandler.cpp", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\sources", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server\\systempromptmanager.cpp", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS2", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS2 LINKONLY=1 NOPASS0=1 MAKEDLL=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "dependencies": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS0"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell", "pass": "PASS1"}], "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\powershell\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS2", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS2 LINKONLY=1 NOPASS0=1 MAKEDLL=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "dependencies": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS0"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\admcpsvr", "pass": "PASS1"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\lib", "pass": "PASS1"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll", "pass": "PASS1"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\server", "pass": "PASS1"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\cpprestsdk", "pass": "PASS1"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\llmclientlib", "pass": "PASS1"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\mcpprotocollib", "pass": "PASS1"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\mcpserversample\\lib", "pass": "PASS1"}], "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll\\..\\..\\cpprestsdk\\consume.inc", "e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\dll\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS2", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS2 LINKONLY=1 NOPASS0=1 MAKEDLL=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "dependencies": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS0"}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll", "pass": "PASS1"}], "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\client\\dll\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}, {"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller", "pass": "PASS2", "cmd": "e:\\os\\tools\\NMakeRust\\x64\\bin\\nmake.exe /nologo BUILDMSG=Stop. /nologo /f e:\\os\\src\\tools\\makefile.def BUILD_PASS=PASS2 LINKONLY=1 NOPASS0=1 MAKEDIR_RELATIVE_TO_BASEDIR=onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller TARGET_PLATFORM=windows CODE_SETS_BUILDING=cs_windows,cs_xbox,cs_phone CODE_SETS_TAGGED=cs_windows,cs_xbox", "dependencies": [{"path": "onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller", "pass": "PASS1"}], "inputs": ["e:\\os\\src\\onecore\\ds\\ds\\src\\aimx\\prod\\aimxsrv\\serviceinstaller\\sources", "e:\\os\\src\\onecore\\ds\\project.mk", "e:\\os\\src\\tools\\makefile.def", "e:\\os\\src\\tools\\projects.inc"]}]}