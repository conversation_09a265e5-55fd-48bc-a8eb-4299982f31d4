# Final RAG Format Creator - Creates the exact format specified with rag_document field
# This creates the optimized structure for vector embedding and semantic search

param(
    [string]$InputFile = "ad_powershell_rag_optimized.json",
    [string]$OutputFile = "ad_powershell_final_rag.json",
    [switch]$Verbose
)

Write-Host "Final RAG Format Creator for PowerShell AD Commands" -ForegroundColor Green
Write-Host "===================================================" -ForegroundColor Green

# Load the current RAG-optimized data
if (-not (Test-Path $InputFile)) {
    Write-Error "Input file not found: $InputFile"
    exit 1
}

Write-Host "Loading RAG-optimized data from: $InputFile" -ForegroundColor Yellow
try {
    $currentData = Get-Content $InputFile -Raw | ConvertFrom-Json
    Write-Host "Successfully loaded data with $($currentData.commands.Count) commands" -ForegroundColor Green
}
catch {
    Write-Error "Failed to parse JSON file: $($_.Exception.Message)"
    exit 1
}

# Initialize final RAG structure
$finalRagData = @{
    metadata = @{
        created_at = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        source_file = $InputFile
        total_commands = 0
        optimization_version = "3.0-Final"
        description = "Final RAG-optimized PowerShell Active Directory command data with consolidated rag_document field for vector embedding"
        features = @(
            "Consolidated rag_document field for vector embedding",
            "Keywords array for hybrid search",
            "Common tasks mapping user intent to commands",
            "Semantic search optimized structure",
            "Maintained detailed structure for SLM context",
            "Removed redundant searchable fields"
        )
    }
    commands = @()
}

Write-Host "Creating final RAG format..." -ForegroundColor Yellow

$processedCount = 0
foreach ($command in $currentData.commands) {
    try {
        $processedCount++
        
        if ($Verbose) {
            Write-Host "  Processing: $($command.command_name)" -ForegroundColor Cyan
        }
        
        # Generate keywords
        $keywords = @()
        
        # Add category-based keywords
        switch ($command.category) {
            'User Management' { $keywords += @('user', 'account', 'person', 'employee', 'identity') }
            'Group Management' { $keywords += @('group', 'team', 'membership', 'security-group', 'distribution') }
            'Computer Management' { $keywords += @('computer', 'machine', 'workstation', 'server', 'device') }
            'Domain Management' { $keywords += @('domain', 'forest', 'directory', 'infrastructure') }
            'Policy Management' { $keywords += @('policy', 'security', 'rules', 'access', 'permissions') }
            'Replication Management' { $keywords += @('replication', 'site', 'topology', 'sync') }
            'Service Account Management' { $keywords += @('service-account', 'managed-account', 'automation') }
            'Organizational Unit Management' { $keywords += @('ou', 'organizational-unit', 'container', 'structure') }
            'Security Management' { $keywords += @('security', 'permissions', 'access', 'authentication') }
            default { $keywords += @('active-directory', 'management') }
        }
        
        # Add verb-based keywords
        switch ($command.verb) {
            'Get' { $keywords += @('retrieve', 'query', 'search', 'find', 'list') }
            'New' { $keywords += @('create', 'add', 'provision', 'establish') }
            'Set' { $keywords += @('modify', 'update', 'change', 'configure', 'edit') }
            'Remove' { $keywords += @('delete', 'remove', 'cleanup', 'purge') }
            'Add' { $keywords += @('add', 'include', 'associate', 'attach') }
            'Enable' { $keywords += @('enable', 'activate', 'turn-on', 'start') }
            'Disable' { $keywords += @('disable', 'deactivate', 'turn-off', 'stop') }
        }
        
        # Add command-specific keywords
        $keywords += $command.command_name.ToLower()
        $keywords += $command.verb.ToLower()
        $keywords += ($command.noun.ToLower() -replace 'ad', '')
        
        # Generate common tasks
        $commonTasks = @()
        $noun = $command.noun -replace '^AD', ''
        
        switch ($command.verb) {
            'Get' {
                $commonTasks += "Find a specific $noun"
                $commonTasks += "List all $noun objects"
                $commonTasks += "Search for $noun by criteria"
                $commonTasks += "Retrieve $noun properties"
            }
            'New' {
                $commonTasks += "Create a new $noun"
                $commonTasks += "Provision $noun with specific properties"
                $commonTasks += "Add $noun to Active Directory"
            }
            'Set' {
                $commonTasks += "Modify $noun properties"
                $commonTasks += "Update $noun configuration"
                $commonTasks += "Change $noun attributes"
            }
            'Remove' {
                $commonTasks += "Delete a $noun"
                $commonTasks += "Remove $noun from Active Directory"
                $commonTasks += "Clean up $noun objects"
            }
            'Add' {
                $commonTasks += "Add members to $noun"
                $commonTasks += "Associate objects with $noun"
                $commonTasks += "Include items in $noun"
            }
            'Enable' {
                $commonTasks += "Enable $noun functionality"
                $commonTasks += "Activate $noun"
            }
            'Disable' {
                $commonTasks += "Disable $noun functionality"
                $commonTasks += "Deactivate $noun"
            }
        }
        
        # Create consolidated RAG document
        $ragDocParts = @()
        $ragDocParts += "Command: $($command.command_name)"
        $ragDocParts += "Purpose: $($command.primary_purpose)"
        
        # Primary task
        $primaryTask = switch ($command.verb) {
            'Get' { "Retrieve $noun information from Active Directory" }
            'New' { "Create a new $noun in Active Directory" }
            'Set' { "Modify $noun properties in Active Directory" }
            'Remove' { "Delete a $noun from Active Directory" }
            'Add' { "Add items to $noun in Active Directory" }
            'Enable' { "Enable $noun functionality" }
            'Disable' { "Disable $noun functionality" }
            default { $command.primary_purpose }
        }
        $ragDocParts += "Primary Task: $primaryTask"
        
        # Common tasks
        if ($commonTasks.Count -gt 0) {
            $ragDocParts += "Common Tasks:"
            foreach ($task in $commonTasks) {
                $ragDocParts += "- $task"
            }
        }
        
        # Key parameters
        if ($command.parameters -and $command.parameters.Count -gt 0) {
            $ragDocParts += "Key Parameters:"
            
            # Focus on required and important parameters
            $keyParams = $command.parameters | Where-Object { 
                $_.mandatory -or 
                $_.name -match '^(Identity|Filter|Name|Path|Properties|SearchBase)$' 
            } | Select-Object -First 5
            
            foreach ($param in $keyParams) {
                $mandatoryText = if ($param.mandatory) { " (Required)" } else { "" }
                $description = $param.description -replace '\s+', ' '
                if ($description.Length -gt 100) {
                    $description = $description.Substring(0, 100) + "..."
                }
                $ragDocParts += "- $($param.name)$mandatoryText`: $description"
            }
        }
        
        # Example scenarios
        if ($command.examples -and $command.examples.Count -gt 0) {
            $ragDocParts += "Example Scenarios:"
            foreach ($example in ($command.examples | Select-Object -First 3)) {
                $cleanDescription = $example.description -replace '\s+', ' '
                if ($cleanDescription.Length -gt 100) {
                    $cleanDescription = $cleanDescription.Substring(0, 100) + "..."
                }
                $ragDocParts += "- $cleanDescription"
            }
        }
        
        $ragDocParts += "Category: $($command.category)"
        
        $ragDocument = $ragDocParts -join "`n"
        
        # Create final command structure (matching your exact specification)
        $finalCommand = @{
            command_name = $command.command_name
            verb = $command.verb
            noun = $command.noun
            module = $command.module
            category = $command.category
            primary_purpose = $command.primary_purpose
            description = $command.description
            keywords = ($keywords | Sort-Object -Unique)
            rag_document = $ragDocument
            required_parameters = $command.required_parameters
            optional_parameters = $command.optional_parameters
            parameters = $command.parameters
            examples = $command.examples
        }
        
        $finalRagData.commands += $finalCommand
    }
    catch {
        Write-Warning "Error processing command $($command.command_name): $($_.Exception.Message)"
    }
}

# Finalize metadata
$finalRagData.metadata.total_commands = $processedCount

Write-Host "Saving final RAG-optimized JSON..." -ForegroundColor Yellow

# Save the final RAG JSON
try {
    $json = $finalRagData | ConvertTo-Json -Depth 20
    $json | Out-File -FilePath $OutputFile -Encoding UTF8
    
    # Display summary
    Write-Host "`n" + "="*70 -ForegroundColor Green
    Write-Host "FINAL RAG OPTIMIZATION COMPLETE!" -ForegroundColor Green
    Write-Host "="*70 -ForegroundColor Green
    Write-Host "Input file: $InputFile" -ForegroundColor White
    Write-Host "Output file: $OutputFile" -ForegroundColor White
    Write-Host "Commands processed: $processedCount" -ForegroundColor Cyan
    Write-Host "File size: $([math]::Round((Get-Item $OutputFile).Length / 1MB, 2)) MB" -ForegroundColor Yellow
    
    # Show sample statistics
    $totalKeywords = ($finalRagData.commands | ForEach-Object { $_.keywords.Count } | Measure-Object -Sum).Sum
    $avgRagDocLength = ($finalRagData.commands | ForEach-Object { $_.rag_document.Length } | Measure-Object -Average).Average
    
    Write-Host "`nFinal RAG Features:" -ForegroundColor Yellow
    Write-Host "  Total Keywords: $totalKeywords" -ForegroundColor Cyan
    Write-Host "  Average RAG Document Length: $([math]::Round($avgRagDocLength, 0)) characters" -ForegroundColor Cyan
    Write-Host "  Categories: $($finalRagData.commands | Group-Object category | Measure-Object).Count" -ForegroundColor Cyan
    
    Write-Host "`nOptimization Features:" -ForegroundColor Yellow
    Write-Host "✓ Consolidated rag_document field for vector embedding" -ForegroundColor Green
    Write-Host "✓ Keywords array for hybrid search systems" -ForegroundColor Green
    Write-Host "✓ Common tasks mapping user intent to commands" -ForegroundColor Green
    Write-Host "✓ Semantic search optimized structure" -ForegroundColor Green
    Write-Host "✓ Maintained detailed structure for SLM context" -ForegroundColor Green
    Write-Host "✓ Clean structure matching your specification" -ForegroundColor Green
    
    Write-Host "`nThe final RAG data is ready for vector database ingestion!" -ForegroundColor Green
    Write-Host "Perfect for Phi-3.5 mini and agentic workflows!" -ForegroundColor White
}
catch {
    Write-Error "Failed to save final RAG JSON: $($_.Exception.Message)"
}
