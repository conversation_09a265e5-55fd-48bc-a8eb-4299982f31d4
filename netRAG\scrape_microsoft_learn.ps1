# Enhanced PowerShell Active Directory Documentation Scraper
# Scrapes comprehensive information from Microsoft Learn for AD PowerShell commands
param(
    [string]$OutputFile = "microsoft_learn_data.json",
    [string]$CommandListFile = "",
    [switch]$ScrapeAllCommands,
    [int]$DelayMs = 1000,
    [switch]$Verbose
)

# Function to get all AD commands from various sources
function Get-ADCommandList {
    $allCommands = @()

    if ($CommandListFile -and (Test-Path $CommandListFile)) {
        Write-Host "Loading commands from file: $CommandListFile"
        $allCommands = Get-Content $CommandListFile | Where-Object { $_ -and $_.Trim() }
    }
    elseif (Test-Path "..\ADPsMCPSvr\Tools\*.ps1") {
        Write-Host "Extracting commands from MCP server tools..."
        Get-ChildItem "..\ADPsMCPSvr\Tools\*.ps1" | ForEach-Object {
            $content = Get-Content $_.FullName -Raw
            $matches = [regex]::Matches($content, 'Register-McpTool -Name "([^"]+)"')
            $matches | ForEach-Object {
                $allCommands += $_.Groups[1].Value
            }
        }
    }
    elseif ($ScrapeAllCommands) {
        Write-Host "Using predefined list of all AD PowerShell commands..."
        # Comprehensive list of AD PowerShell commands
        $allCommands = @(
            "Add-ADCentralAccessPolicyMember", "Add-ADComputerServiceAccount", "Add-ADDomainControllerPasswordReplicationPolicy",
            "Add-ADFineGrainedPasswordPolicySubject", "Add-ADGroupMember", "Add-ADPrincipalGroupMembership",
            "Add-ADResourcePropertyListMember", "Clear-ADAccountExpiration", "Clear-ADClaimTransformLink",
            "Complete-ADServiceAccountMigration", "Disable-ADAccount", "Disable-ADOptionalFeature",
            "Enable-ADAccount", "Enable-ADOptionalFeature", "Get-ADAccountAuthorizationGroup",
            "Get-ADAccountResultantPasswordReplicationPolicy", "Get-ADAuthenticationPolicy", "Get-ADAuthenticationPolicySilo",
            "Get-ADCentralAccessPolicy", "Get-ADCentralAccessRule", "Get-ADClaimTransformPolicy",
            "Get-ADClaimType", "Get-ADComputer", "Get-ADComputerServiceAccount",
            "Get-ADDCCloningExcludedApplicationList", "Get-ADDefaultDomainPasswordPolicy", "Get-ADDomain",
            "Get-ADDomainController", "Get-ADDomainControllerPasswordReplicationPolicy", "Get-ADDomainControllerPasswordReplicationPolicyUsage",
            "Get-ADFineGrainedPasswordPolicy", "Get-ADFineGrainedPasswordPolicySubject", "Get-ADForest",
            "Get-ADGroup", "Get-ADGroupMember", "Get-ADObject", "Get-ADOptionalFeature",
            "Get-ADOrganizationalUnit", "Get-ADPrincipalGroupMembership", "Get-ADReplicationAttributeMetadata",
            "Get-ADReplicationConnection", "Get-ADReplicationFailure", "Get-ADReplicationPartnerMetadata",
            "Get-ADReplicationQueueOperation", "Get-ADReplicationSite", "Get-ADReplicationSiteLink",
            "Get-ADReplicationSiteLinkBridge", "Get-ADReplicationSubnet", "Get-ADReplicationUpToDatenessVectorTable",
            "Get-ADResourceProperty", "Get-ADResourcePropertyList", "Get-ADResourcePropertyValueType",
            "Get-ADRootDSE", "Get-ADServiceAccount", "Get-ADTrust", "Get-ADUser", "Get-ADUserResultantPasswordPolicy"
        )
    }
    else {
        Write-Error "No command source specified. Use -CommandListFile, ensure MCP server tools exist, or use -ScrapeAllCommands"
        return @()
    }

    return $allCommands | Sort-Object -Unique
}

# Microsoft Learn base URL for AD module
$baseUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory"

# Simple HTML decoding function (since System.Web.HttpUtility may not be available)
function Decode-Html {
    param([string]$HtmlText)

    if (-not $HtmlText) { return "" }

    $decoded = $HtmlText
    $decoded = $decoded -replace '&amp;', '&'
    $decoded = $decoded -replace '&lt;', '<'
    $decoded = $decoded -replace '&gt;', '>'
    $decoded = $decoded -replace '&quot;', '"'
    $decoded = $decoded -replace '&#39;', "'"
    $decoded = $decoded -replace '&nbsp;', ' '

    return $decoded
}

# Enhanced function to extract comprehensive data from Microsoft Learn pages
function Get-MicrosoftLearnData {
    param([string]$CommandName)

    $url = "$baseUrl/$($CommandName.ToLower())"

    try {
        if ($Verbose) { Write-Host "Scraping: $CommandName from $url" }

        # Use Invoke-WebRequest to get the page content
        $response = Invoke-WebRequest -Uri $url -UseBasicParsing -TimeoutSec 30
        $content = $response.Content

        # Initialize result object with comprehensive structure
        $result = @{
            command = $CommandName
            url = $url
            module = "ActiveDirectory"
            synopsis = ""
            description = ""
            syntax_blocks = @()
            parameters = @{}
            examples = @()
            inputs = @()
            outputs = @()
            notes = @()
            related_links = @()
            scraped_at = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            status = "success"
            metadata = @{
                page_title = ""
                last_updated = ""
                version = ""
            }
            raw_sections = @{}
        }

        # Extract page title
        if ($content -match '<title>([^<]+)</title>') {
            $result.metadata.page_title = $matches[1].Trim()
        }

        # Extract synopsis from meta description
        if ($content -match '<meta name="description" content="([^"]+)"') {
            $result.synopsis = [System.Web.HttpUtility]::HtmlDecode($matches[1])
        }

        # Extract main description from the Description section
        $descMatches = [regex]::Matches($content, '(?s)<h2[^>]*>Description</h2>\s*<p>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</p>')
        if ($descMatches.Count -gt 0) {
            $result.description = [System.Web.HttpUtility]::HtmlDecode($descMatches[0].Groups[1].Value) -replace '<[^>]+>', '' -replace '\s+', ' '
        }

        # Extract syntax information
        $syntaxMatches = [regex]::Matches($content, '(?s)<h3[^>]*>([^<]+)</h3>\s*<pre><code[^>]*>([^<]+)</code></pre>')
        foreach ($match in $syntaxMatches) {
            $syntaxName = $match.Groups[1].Value.Trim()
            $syntaxCode = [System.Web.HttpUtility]::HtmlDecode($match.Groups[2].Value.Trim())
            if ($syntaxCode -match $CommandName) {
                $result.syntax += @{
                    name = $syntaxName
                    code = $syntaxCode
                }
            }
        }

        # Extract examples with improved parsing
        $exampleSections = [regex]::Matches($content, '(?s)<h3[^>]*>Example \d+[^<]*</h3>(.*?)(?=<h[23][^>]*>|$)')
        foreach ($exampleSection in $exampleSections) {
            $sectionContent = $exampleSection.Groups[1].Value

            # Extract example title
            $titleMatch = [regex]::Match($exampleSection.Groups[0].Value, '<h3[^>]*>([^<]+)</h3>')
            $exampleTitle = if ($titleMatch.Success) { $titleMatch.Groups[1].Value.Trim() } else { "Example" }

            # Extract code blocks
            $codeMatches = [regex]::Matches($sectionContent, '<pre><code[^>]*>([^<]+)</code></pre>')
            foreach ($codeMatch in $codeMatches) {
                $exampleCode = [System.Web.HttpUtility]::HtmlDecode($codeMatch.Groups[1].Value.Trim())

                # Extract description (text between title and code, or after code)
                $description = ""
                $descMatch = [regex]::Match($sectionContent, '(?s)<p>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</p>')
                if ($descMatch.Success) {
                    $description = [System.Web.HttpUtility]::HtmlDecode($descMatch.Groups[1].Value) -replace '<[^>]+>', '' -replace '\s+', ' '
                }

                $result.examples += @{
                    title = $exampleTitle
                    description = $description
                    code = $exampleCode
                    source = "Microsoft Learn"
                }
            }
        }

        # Extract detailed parameter information
        $result.parameters = Extract-ParameterDetails -content $content -commandName $CommandName

        # Extract additional sections
        $additionalSections = Extract-AdditionalSections -content $content
        $result.inputs = $additionalSections.inputs
        $result.outputs = $additionalSections.outputs
        $result.notes = $additionalSections.notes
        $result.related_links = $additionalSections.related_links

        return $result
    }
    catch {
        Write-Warning "Failed to scrape $CommandName`: $($_.Exception.Message)"
        return @{
            command = $CommandName
            url = $url
            status = "failed"
            error = $_.Exception.Message
            scraped_at = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
    }
}

# Enhanced parameter extraction function
function Extract-ParameterDetails {
    param([string]$content, [string]$commandName)

    $parameters = @{}

    # Look for parameter sections in the documentation
    $parameterSections = [regex]::Matches($content, '(?s)<h3[^>]*>-(\w+)</h3>(.*?)(?=<h3[^>]*>-\w+</h3>|<h2[^>]*>|$)')

    foreach ($paramSection in $parameterSections) {
        $paramName = $paramSection.Groups[1].Value
        $paramContent = $paramSection.Groups[2].Value

        $paramInfo = @{
            name = $paramName
            description = ""
            type = ""
            default_value = ""
            accepted_values = @()
            mandatory = $false
            position = ""
            pipeline_input = $false
            wildcards_supported = $false
            aliases = @()
        }

        # Extract description
        $descMatch = [regex]::Match($paramContent, '(?s)<p>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</p>')
        if ($descMatch.Success) {
            $paramInfo.description = [System.Web.HttpUtility]::HtmlDecode($descMatch.Groups[1].Value) -replace '<[^>]+>', '' -replace '\s+', ' '
        }

        # Extract parameter properties table
        $propMatches = [regex]::Matches($paramContent, '(?s)<dt>([^<]+)</dt>\s*<dd>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</dd>')
        foreach ($propMatch in $propMatches) {
            $propName = $propMatch.Groups[1].Value.Trim()
            $propValue = [System.Web.HttpUtility]::HtmlDecode($propMatch.Groups[2].Value) -replace '<[^>]+>', '' -replace '\s+', ' '

            switch ($propName) {
                "Type:" { $paramInfo.type = $propValue }
                "Default value:" { $paramInfo.default_value = $propValue }
                "Accepted values:" { $paramInfo.accepted_values = $propValue -split ',\s*' }
                "Position:" { $paramInfo.position = $propValue }
                "Mandatory:" { $paramInfo.mandatory = $propValue -eq "True" }
                "Value from pipeline:" { $paramInfo.pipeline_input = $propValue -eq "True" }
                "Supports wildcards:" { $paramInfo.wildcards_supported = $propValue -eq "True" }
                "Aliases:" { $paramInfo.aliases = $propValue -split ',\s*' }
            }
        }

        $parameters[$paramName] = $paramInfo
    }

    return $parameters
}

# Enhanced function to extract additional sections
function Extract-AdditionalSections {
    param([string]$content)

    $sections = @{
        inputs = @()
        outputs = @()
        notes = @()
        related_links = @()
    }

    # Extract Inputs section
    $inputsMatch = [regex]::Match($content, '(?s)<h2[^>]*>Inputs</h2>(.*?)(?=<h2[^>]*>|$)')
    if ($inputsMatch.Success) {
        $inputContent = $inputsMatch.Groups[1].Value
        $inputMatches = [regex]::Matches($inputContent, '<h3[^>]*>([^<]+)</h3>')
        foreach ($match in $inputMatches) {
            $sections.inputs += $match.Groups[1].Value.Trim()
        }
    }

    # Extract Outputs section
    $outputsMatch = [regex]::Match($content, '(?s)<h2[^>]*>Outputs</h2>(.*?)(?=<h2[^>]*>|$)')
    if ($outputsMatch.Success) {
        $outputContent = $outputsMatch.Groups[1].Value
        $outputMatches = [regex]::Matches($outputContent, '<h3[^>]*>([^<]+)</h3>')
        foreach ($match in $outputMatches) {
            $sections.outputs += $match.Groups[1].Value.Trim()
        }
    }

    # Extract Notes section
    $notesMatch = [regex]::Match($content, '(?s)<h2[^>]*>Notes</h2>(.*?)(?=<h2[^>]*>|$)')
    if ($notesMatch.Success) {
        $noteContent = $notesMatch.Groups[1].Value
        $noteMatches = [regex]::Matches($noteContent, '<li>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</li>')
        foreach ($match in $noteMatches) {
            $note = [System.Web.HttpUtility]::HtmlDecode($match.Groups[1].Value) -replace '<[^>]+>', '' -replace '\s+', ' '
            $sections.notes += $note
        }
    }

    # Extract Related Links section
    $linksMatch = [regex]::Match($content, '(?s)<h2[^>]*>Related Links</h2>(.*?)(?=<h2[^>]*>|$)')
    if ($linksMatch.Success) {
        $linkContent = $linksMatch.Groups[1].Value
        $linkMatches = [regex]::Matches($linkContent, '<a[^>]+href="([^"]+)"[^>]*>([^<]+)</a>')
        foreach ($match in $linkMatches) {
            $sections.related_links += @{
                url = $match.Groups[1].Value
                title = $match.Groups[2].Value.Trim()
            }
        }
    }

    return $sections
}

# Main execution
Write-Host "Enhanced PowerShell Active Directory Documentation Scraper" -ForegroundColor Green
Write-Host "=======================================================" -ForegroundColor Green

# Get list of commands to scrape
$allCommands = Get-ADCommandList
if ($allCommands.Count -eq 0) {
    Write-Error "No commands found to scrape. Exiting."
    exit 1
}

Write-Host "Found $($allCommands.Count) unique commands to scrape" -ForegroundColor Yellow

# Scrape data for all commands
$scrapedData = @()
$successCount = 0
$failCount = 0
$startTime = Get-Date

Write-Host "`nStarting scraping process..." -ForegroundColor Cyan

foreach ($command in $allCommands) {
    $progress = [math]::Round(($scrapedData.Count / $allCommands.Count) * 100, 1)
    Write-Progress -Activity "Scraping PowerShell Documentation" -Status "Processing $command" -PercentComplete $progress

    $data = Get-MicrosoftLearnData -CommandName $command
    $scrapedData += $data

    if ($data.status -eq "success") {
        $successCount++
        if ($Verbose) { Write-Host "✓ Successfully scraped: $command" -ForegroundColor Green }
    } else {
        $failCount++
        Write-Host "✗ Failed to scrape: $command - $($data.error)" -ForegroundColor Red
    }

    # Add delay to be respectful to Microsoft's servers
    Start-Sleep -Milliseconds $DelayMs
}

Write-Progress -Activity "Scraping PowerShell Documentation" -Completed

$endTime = Get-Date
$duration = $endTime - $startTime

# Create comprehensive result structure
$result = @{
    scraping_summary = @{
        total_commands = $allCommands.Count
        successful_scrapes = $successCount
        failed_scrapes = $failCount
        success_rate = [math]::Round(($successCount / $allCommands.Count) * 100, 2)
        scraping_date = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        duration_minutes = [math]::Round($duration.TotalMinutes, 2)
        source_url = $baseUrl
        scraper_version = "2.0"
        delay_ms = $DelayMs
    }
    scraped_commands = $scrapedData
    command_index = @{}
}

# Create command index for faster lookups
foreach ($cmd in $scrapedData) {
    if ($cmd.status -eq "success") {
        $result.command_index[$cmd.command] = @{
            synopsis = $cmd.synopsis
            parameter_count = $cmd.parameters.Count
            example_count = $cmd.examples.Count
            has_syntax = $cmd.syntax.Count -gt 0
        }
    }
}

# Save results with better formatting
$json = $result | ConvertTo-Json -Depth 15
$json | Out-File -FilePath $OutputFile -Encoding UTF8

# Display summary
Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "SCRAPING COMPLETE!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green
Write-Host "Total Commands: $($allCommands.Count)" -ForegroundColor White
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red
Write-Host "Success Rate: $($result.scraping_summary.success_rate)%" -ForegroundColor Yellow
Write-Host "Duration: $($result.scraping_summary.duration_minutes) minutes" -ForegroundColor Cyan
Write-Host "Data saved to: $OutputFile" -ForegroundColor White
Write-Host "File size: $([math]::Round((Get-Item $OutputFile).Length / 1MB, 2)) MB" -ForegroundColor Cyan

if ($failCount -gt 0) {
    Write-Host "`nFailed commands:" -ForegroundColor Red
    $scrapedData | Where-Object { $_.status -eq "failed" } | ForEach-Object {
        Write-Host "  - $($_.command): $($_.error)" -ForegroundColor Red
    }
}
