using Microsoft.Extensions.AI;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text;

namespace NetRagService;

public class PowerShellCommandIngestionService
{
    private readonly IEmbeddingGenerator<string, Embedding<float>> _embeddingService;
    private readonly VectorStoreService _vectorStoreService;
    private readonly ILogger<PowerShellCommandIngestionService> _logger;

    public PowerShellCommandIngestionService(
        IEmbeddingGenerator<string, Embedding<float>> embeddingService,
        VectorStoreService vectorStoreService,
        ILogger<PowerShellCommandIngestionService> logger)
    {
        _embeddingService = embeddingService;
        _vectorStoreService = vectorStoreService;
        _logger = logger;
    }

    public async Task IngestMicrosoftLearnDataAsync(string jsonFilePath)
    {
        if (!File.Exists(jsonFilePath))
        {
            throw new FileNotFoundException($"Microsoft Learn data file not found: {jsonFilePath}");
        }

        _logger.LogInformation("Starting ingestion of Microsoft Learn PowerShell commands from: {FilePath}", jsonFilePath);

        var jsonContent = await File.ReadAllTextAsync(jsonFilePath);
        var data = JsonSerializer.Deserialize<MicrosoftLearnData>(jsonContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        if (data?.AdPowershellCommands == null)
        {
            throw new InvalidOperationException("Invalid Microsoft Learn data format");
        }

        _logger.LogInformation("Found {CommandCount} PowerShell commands to ingest", data.AdPowershellCommands.Count);

        int processedCount = 0;
        foreach (var command in data.AdPowershellCommands)
        {
            try
            {
                await IngestSingleCommandAsync(command, processedCount);
                processedCount++;

                if (processedCount % 10 == 0)
                {
                    _logger.LogInformation("Processed {ProcessedCount}/{TotalCount} commands", 
                        processedCount, data.AdPowershellCommands.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to ingest command: {CommandName}", command.Command ?? "Unknown");
                throw;
            }
        }

        _logger.LogInformation("Successfully ingested {ProcessedCount} PowerShell commands", processedCount);
    }

    private async Task IngestSingleCommandAsync(PowerShellCommand command, int index)
    {
        // Create comprehensive text representation for embedding
        var commandText = BuildCommandText(command);
        
        // Generate embedding for the command
        var embeddingResult = await _embeddingService.GenerateAsync(commandText);
        var embedding = embeddingResult.Vector;

        // Create metadata
        var metadata = new Dictionary<string, object>
        {
            ["command_name"] = command.Command ?? "Unknown",
            ["command_index"] = index,
            ["parameter_count"] = command.Parameters?.Count ?? 0,
            ["example_count"] = command.Examples?.Count ?? 0,
            ["full_text"] = commandText,
            ["ingestion_timestamp"] = DateTime.UtcNow.ToString("O"),
            ["data_type"] = "powershell_command"
        };

        // Add parameter names for better searchability
        if (command.Parameters != null && command.Parameters.Count > 0)
        {
            metadata["parameter_names"] = string.Join(", ", command.Parameters.Keys);
        }

        // Store in vector database
        await _vectorStoreService.UpsertAsync(
            id: $"ps_cmd_{index}_{command.Command?.Replace("-", "_").ToLowerInvariant()}",
            embedding: embedding,
            metadata: metadata
        );
    }

    private string BuildCommandText(PowerShellCommand command)
    {
        var sb = new StringBuilder();
        
        // Add command name
        if (!string.IsNullOrEmpty(command.Command))
        {
            sb.AppendLine($"Command: {command.Command}");
        }

        // Add parameters and descriptions
        if (command.Parameters != null && command.Parameters.Count > 0)
        {
            sb.AppendLine("Parameters:");
            foreach (var param in command.Parameters)
            {
                sb.AppendLine($"- {param.Key}: {param.Value.Description}");
            }
        }

        // Add examples
        if (command.Examples != null && command.Examples.Count > 0)
        {
            sb.AppendLine("Examples:");
            for (int i = 0; i < command.Examples.Count; i++)
            {
                sb.AppendLine($"Example {i + 1}:");
                sb.AppendLine(command.Examples[i].Command);
                sb.AppendLine();
            }
        }

        return sb.ToString();
    }
}

// Data models for JSON deserialization
public class MicrosoftLearnData
{
    [JsonPropertyName("Command Knowledge")]
    public CommandKnowledge? CommandKnowledge { get; set; }

    [JsonPropertyName("AD Powershell Commands")]
    public List<PowerShellCommand> AdPowershellCommands { get; set; } = new();
}

public class CommandKnowledge
{
    [JsonPropertyName("Author")]
    public string? Author { get; set; }

    [JsonPropertyName("Crated")] // Note: This matches the typo in the JSON
    public string? Created { get; set; }

    [JsonPropertyName("source_url")]
    public string? SourceUrl { get; set; }

    [JsonPropertyName("total_commands")]
    public int TotalCommands { get; set; }
}

public class PowerShellCommand
{
    [JsonPropertyName("command")]
    public string? Command { get; set; }

    [JsonPropertyName("parameters")]
    public Dictionary<string, ParameterInfo>? Parameters { get; set; }

    [JsonPropertyName("examples")]
    public List<CommandExample>? Examples { get; set; }
}

public class ParameterInfo
{
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
}

public class CommandExample
{
    [JsonPropertyName("command")]
    public string Command { get; set; } = string.Empty;
}
