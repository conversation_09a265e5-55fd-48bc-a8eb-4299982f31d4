using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace NetRagService;

[ApiController]
[Route("api/[controller]")]
public class PowerShellCommandController : ControllerBase
{
    private readonly PowerShellCommandSearchService _searchService;
    private readonly ILogger<PowerShellCommandController> _logger;

    public PowerShellCommandController(
        PowerShellCommandSearchService searchService,
        ILogger<PowerShellCommandController> logger)
    {
        _searchService = searchService;
        _logger = logger;
    }

    /// <summary>
    /// Search for PowerShell commands using semantic similarity
    /// </summary>
    /// <param name="query">The search query</param>
    /// <param name="limit">Maximum number of results to return (default: 5, max: 20)</param>
    /// <returns>List of matching PowerShell commands</returns>
    [HttpGet("search")]
    public async Task<ActionResult<List<PowerShellCommandResult>>> Search(
        [FromQuery] string query,
        [FromQuery] int limit = 5)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return BadRequest("Query parameter is required");
        }

        if (limit <= 0 || limit > 20)
        {
            return BadRequest("Limit must be between 1 and 20");
        }

        try
        {
            var results = await _searchService.SearchAsync(query, limit);
            return Ok(results);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search PowerShell commands");
            return StatusCode(500, "Internal server error occurred while searching");
        }
    }

    /// <summary>
    /// Get a specific PowerShell command by ID
    /// </summary>
    /// <param name="id">The command ID</param>
    /// <returns>PowerShell command details</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<PowerShellCommandResult>> GetById(string id)
    {
        if (string.IsNullOrWhiteSpace(id))
        {
            return BadRequest("Command ID is required");
        }

        try
        {
            var result = await _searchService.GetCommandByIdAsync(id);
            
            if (result == null)
            {
                return NotFound($"PowerShell command with ID '{id}' not found");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get PowerShell command by ID: {CommandId}", id);
            return StatusCode(500, "Internal server error occurred while retrieving command");
        }
    }

    /// <summary>
    /// Get all available PowerShell command names
    /// </summary>
    /// <returns>List of all command names</returns>
    [HttpGet("names")]
    public async Task<ActionResult<List<string>>> GetAllCommandNames()
    {
        try
        {
            var commandNames = await _searchService.GetAllCommandNamesAsync();
            return Ok(commandNames);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all PowerShell command names");
            return StatusCode(500, "Internal server error occurred while retrieving command names");
        }
    }

    /// <summary>
    /// Get search statistics and information
    /// </summary>
    /// <returns>Statistics about the search index</returns>
    [HttpGet("stats")]
    public async Task<ActionResult<Dictionary<string, object>>> GetStats()
    {
        try
        {
            var stats = await _searchService.GetSearchStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get search statistics");
            return StatusCode(500, "Internal server error occurred while retrieving statistics");
        }
    }

    /// <summary>
    /// Data re-ingestion endpoint (disabled in read-only mode)
    /// </summary>
    /// <returns>Information message</returns>
    [HttpPost("reingest")]
    public async Task<ActionResult<object>> ReingestData()
    {
        _logger.LogInformation("Re-ingestion API called, but service is in read-only mode");

        var stats = await _searchService.GetSearchStatsAsync();

        return Ok(new
        {
            message = "Service is in read-only mode. Use the DatabaseConverter utility to create new database files.",
            mode = "read-only",
            stats = stats,
            instructions = "To update data: 1) Use DatabaseConverter.exe to convert JSON to database.bin, 2) Restart the service to load new data"
        });
    }

    /// <summary>
    /// Health check endpoint for the PowerShell command search service
    /// </summary>
    /// <returns>Health status</returns>
    [HttpGet("health")]
    public async Task<ActionResult<object>> HealthCheck()
    {
        try
        {
            var stats = await _searchService.GetSearchStatsAsync();
            var vectorCount = (int)stats["total_vectors"];
            
            return Ok(new
            {
                status = "healthy",
                service = "PowerShell Command Search",
                vector_count = vectorCount,
                timestamp = DateTime.UtcNow.ToString("O")
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
            return StatusCode(500, new
            {
                status = "unhealthy",
                service = "PowerShell Command Search",
                error = ex.Message,
                timestamp = DateTime.UtcNow.ToString("O")
            });
        }
    }
}
