using Microsoft.Extensions.AI;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using NetRagService;
using System.IO.Compression;
using System.Text.Json;

namespace DatabaseConverter;

/// <summary>
/// Improved Database Converter for RAG-optimized PowerShell Active Directory commands
/// Supports the new advanced RAG format with rag_document, keywords, and enhanced metadata
/// </summary>
public class RagDatabaseConverter
{
    public static async Task ConvertRagDatabase(string[] args)
    {
        Console.WriteLine("=== Enhanced NetRAG Database Converter ===");
        Console.WriteLine("Converts RAG-optimized PowerShell AD JSON data to compressed binary database files.");
        Console.WriteLine();

        if (args.Length < 2)
        {
            Console.WriteLine("Usage: DatabaseConverter.exe <input-rag-json-file> <output-database-file>");
            Console.WriteLine("Example: DatabaseConverter.exe ad_powershell_final_rag.json database.bin");
            Environment.Exit(1);
        }

        string inputJsonFile = args[0];
        string outputDatabaseFile = args[1];

        if (!File.Exists(inputJsonFile))
        {
            Console.WriteLine($"Error: Input file '{inputJsonFile}' not found.");
            Environment.Exit(1);
        }

        try
        {
            await ConvertRagJsonToDatabase(inputJsonFile, outputDatabaseFile);
            Console.WriteLine($"✅ Enhanced conversion completed successfully!");
            Console.WriteLine($"📁 Output file: {outputDatabaseFile}");
            Console.WriteLine($"📊 File size: {new FileInfo(outputDatabaseFile).Length:N0} bytes");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error during conversion: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            Environment.Exit(1);
        }
    }

    static async Task ConvertRagJsonToDatabase(string inputJsonFile, string outputDatabaseFile)
    {
        Console.WriteLine($"📖 Reading RAG-optimized input file: {inputJsonFile}");
        
        // Set up dependency injection and services
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        
        // Configure Semantic Kernel with BERT ONNX embedding
        var kernelBuilder = Kernel.CreateBuilder();
        
        // Use default model paths - adjust these if your models are in different locations
        string modelPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "model.onnx");
        string vocabPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "vocab.txt");
        
        if (!File.Exists(modelPath))
        {
            throw new FileNotFoundException($"BERT ONNX model not found at: {modelPath}");
        }
        
        if (!File.Exists(vocabPath))
        {
            throw new FileNotFoundException($"BERT vocabulary file not found at: {vocabPath}");
        }
        
        Console.WriteLine($"🧠 Loading BERT embedding model: {modelPath}");
        kernelBuilder.AddBertOnnxEmbeddingGenerator(modelPath, vocabPath);
        
        var kernel = kernelBuilder.Build();
        var embeddingService = kernel.GetRequiredService<IEmbeddingGenerator<string, Microsoft.Extensions.AI.Embedding<float>>>();
        
        services.AddSingleton(embeddingService);
        services.AddSingleton<EnhancedPowerShellCommandIngestionService>();
        
        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<RagDatabaseConverter>>();
        var ingestionService = serviceProvider.GetRequiredService<EnhancedPowerShellCommandIngestionService>();
        
        // Create in-memory vector store for conversion
        var vectorStore = new VectorStoreService(serviceProvider.GetRequiredService<ILogger<VectorStoreService>>());
        await vectorStore.InitializeAsync(768); // Standard BERT embedding size
        
        Console.WriteLine($"⚡ Processing RAG-optimized PowerShell commands from JSON...");
        
        // Read and parse RAG-optimized JSON file
        string jsonContent = await File.ReadAllTextAsync(inputJsonFile);
        var ragData = JsonSerializer.Deserialize<RagOptimizedData>(jsonContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        
        if (ragData?.Commands == null || ragData.Commands.Count == 0)
        {
            throw new InvalidOperationException("No PowerShell commands found in the RAG-optimized input file.");
        }
        
        Console.WriteLine($"📋 Found {ragData.Commands.Count} RAG-optimized PowerShell commands to process");
        
        // Process each command and generate embeddings using the rag_document field
        int processed = 0;
        foreach (var command in ragData.Commands)
        {
            try
            {
                // Use the rag_document field as the primary text for embedding
                // This is specifically designed for vector search and contains all the important context
                string embeddingText = command.RagDocument ?? "";
                
                // Fallback to building text if rag_document is missing
                if (string.IsNullOrEmpty(embeddingText))
                {
                    embeddingText = BuildFallbackEmbeddingText(command);
                }
                
                // Generate embedding using the rag_document
                var embeddingResults = await embeddingService.GenerateAsync(new[] { embeddingText });
                var embedding = embeddingResults.First();
                
                // Create enhanced metadata with all the RAG-optimized fields
                var metadata = new Dictionary<string, object>
                {
                    // Core command information
                    ["command_name"] = command.CommandName ?? "",
                    ["verb"] = command.Verb ?? "",
                    ["noun"] = command.Noun ?? "",
                    ["module"] = command.Module ?? "",
                    ["category"] = command.Category ?? "",
                    ["primary_purpose"] = command.PrimaryPurpose ?? "",
                    ["description"] = command.Description ?? "",
                    
                    // RAG-specific fields
                    ["rag_document"] = command.RagDocument ?? "",
                    ["keywords"] = JsonSerializer.Serialize(command.Keywords ?? new List<string>()),
                    
                    // Parameter information
                    ["required_parameters"] = JsonSerializer.Serialize(command.RequiredParameters ?? new List<string>()),
                    ["optional_parameters"] = JsonSerializer.Serialize(command.OptionalParameters ?? new List<string>()),
                    ["parameter_count"] = command.Parameters?.Count ?? 0,
                    ["example_count"] = command.Examples?.Count ?? 0,
                    
                    // Detailed data for LLM context
                    ["parameters"] = JsonSerializer.Serialize(command.Parameters ?? new List<ParameterData>()),
                    ["examples"] = JsonSerializer.Serialize(command.Examples ?? new List<ExampleData>()),
                    
                    // Search optimization
                    ["searchable_keywords"] = string.Join(" ", command.Keywords ?? new List<string>()),
                    ["parameter_names"] = string.Join(", ", command.Parameters?.Select(p => p.Name) ?? new List<string>()),
                    
                    // Metadata
                    ["ingestion_timestamp"] = DateTime.UtcNow.ToString("O"),
                    ["data_type"] = "rag_optimized_powershell_command",
                    ["optimization_version"] = ragData.Metadata?.OptimizationVersion ?? "unknown"
                };
                
                // Add to vector store
                await vectorStore.UpsertAsync(
                    id: $"rag_cmd_{processed}_{command.CommandName?.Replace("-", "_").ToLowerInvariant()}",
                    embedding: embedding.Vector,
                    metadata: metadata
                );
                
                processed++;
                if (processed % 50 == 0)
                {
                    Console.WriteLine($"⏳ Processed {processed}/{ragData.Commands.Count} commands...");
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning("Failed to process command {CommandName}: {Error}", command.CommandName, ex.Message);
            }
        }
        
        Console.WriteLine($"✨ Processed {processed} RAG-optimized commands successfully");
        Console.WriteLine($"💾 Saving to enhanced database file: {outputDatabaseFile}");

        // Save to compressed binary format
        await vectorStore.SaveToDatabaseFileAsync(outputDatabaseFile);

        Console.WriteLine($"🎉 Enhanced database conversion completed!");
        Console.WriteLine($"🔍 Database now supports:");
        Console.WriteLine($"   • Vector search using rag_document embeddings");
        Console.WriteLine($"   • Keyword-based filtering");
        Console.WriteLine($"   • Category and purpose-based search");
        Console.WriteLine($"   • Rich metadata for LLM context");
    }
    
    private static string BuildFallbackEmbeddingText(RagOptimizedCommand command)
    {
        var parts = new List<string>();
        
        if (!string.IsNullOrEmpty(command.CommandName))
            parts.Add($"Command: {command.CommandName}");
            
        if (!string.IsNullOrEmpty(command.PrimaryPurpose))
            parts.Add($"Purpose: {command.PrimaryPurpose}");
            
        if (!string.IsNullOrEmpty(command.Description))
            parts.Add($"Description: {command.Description}");
            
        if (command.Keywords?.Any() == true)
            parts.Add($"Keywords: {string.Join(", ", command.Keywords)}");
            
        if (command.Parameters?.Any() == true)
        {
            parts.Add("Parameters:");
            foreach (var param in command.Parameters.Take(5)) // Limit to avoid too much text
            {
                parts.Add($"- {param.Name}: {param.Description}");
            }
        }
        
        return string.Join("\n", parts);
    }
}

// Data structures are now in DataModels.cs to avoid duplication
