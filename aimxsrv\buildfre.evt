ts=359 Merging config files using BUILD_CONFIG_FILE=e:\os\obj\amd64fre\objfre\amd64\build-exe-merged.config
ts=937 queue pregraph command
ts=937 run PreGraph commands build_pre_graph
ts=1578 queue prebuild command
ts=1578 run preprocess commands build_pre_process
ts=2875 initializing DBB query
ts=9046 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv
ts=9046 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod
ts=9046 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx
ts=9046 reading parent chain of e:\os\src\onecore\ds\ds\src
ts=9046 reading parent chain of e:\os\src\onecore\ds\ds
ts=9046 reading parent chain of e:\os\src\onecore\ds
ts=9046 reading parent chain of e:\os\src\onecore
ts=9046 reading parent chain of e:\os\src
ts=9046 scanning focus directory e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv
ts=9078 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\92420b199da1353a5153dd681a72a770\_asmid.inc in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS0) 
ts=9078 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\92420b199da1353a5153dd681a72a770\_asmid.xml in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS0) 
ts=9078 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\f3eb340592f82345a38296f4f345c344\_generated.cs in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS1) 
ts=9078 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\f3eb340592f82345a38296f4f345c344\aimxpsh.asmmeta_temp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS1) 
ts=9078 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\f3eb340592f82345a38296f4f345c344\aimxpsh.metadata_dll in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS1) 
ts=9093 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\d199b05061b984b7b5395b71ab4edf04\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS2) 
ts=9109 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\306954141ccac36c84bbaa1b3bf30da0\binp_2.rsp in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS2) 
ts=9109 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\306954141ccac36c84bbaa1b3bf30da0\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (PASS2) 
ts=9125 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): sent to build client (4).
ts=9140 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): sent to build client (1).
ts=9140 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): distributed work started.
ts=9140 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): distributed work completed.
ts=9140 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): sent to build client (5).
ts=9156 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): sent to build client (1).
ts=9156 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): distributed work started.
ts=9156 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): distributed work completed.
ts=9156 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): distributed work started.
ts=9156 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): distributed work completed.
ts=9171 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\9d1f4f09e82ac857e708a305bff55c60\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (PASS2) 
ts=9171 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\cpprestsdk
ts=9187 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work started.
ts=9187 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work completed.
ts=9187 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(12): sent to build client (6).
ts=9187 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): sent to build client (5).
ts=9187 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\admcpsvr
ts=9187 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\llmclientlib
ts=9187 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(12): distributed work started.
ts=9203 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\mcpprotocollib
ts=9203 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(12): distributed work completed.
ts=9203 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample\lib
ts=9203 reading parent chain of e:\os\src\onecore\ds\ds\src\aimx\prod\mcpserversample
ts=9203 DELETE OUTPUT LIST: Invalid output entry e:\os\obj\amd64fre\temp\97238c20fe107b7b1a729a23e41e089c\post_link_concurrent.log in e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller (PASS2) 
ts=9203 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): distributed work started.
ts=9218 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): distributed work completed.
ts=9218 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): sent to build client (4).
ts=9218 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): distributed work started.
ts=9218 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): distributed work completed.
ts=9218 scanning focus directory e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv
ts=9234 BUILD: Processing dependencies...
ts=9250 (PASS0) onecore\ds\ds\src\aimx\prod\mcpprotocollib(20): sent to build client (5).
ts=9250 (PASS0) onecore\ds\ds\src\aimx\prod\mcpprotocollib(20): distributed work started.
ts=9250 (PASS0) onecore\ds\ds\src\aimx\prod\mcpprotocollib(20): distributed work completed.
ts=9250 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): sent to build client (4).
ts=9250 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): distributed work started.
ts=9265 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): distributed work completed.
ts=9265 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): sent to build client (5).
ts=9265 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): distributed work started.
ts=9265 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): distributed work completed.
ts=9546 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): sent to build client (4).
ts=11062 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): distributed work started.
ts=11062 (PASS0) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): distributed work completed.
ts=11078 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(19): sent to build client (5).
ts=11078 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(19): distributed work started.
ts=11078 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(19): distributed work completed.
ts=11078 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(19): sent to build client (4).
ts=11078 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(19): distributed work started.
ts=11078 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(19): distributed work completed.
ts=11093 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(18): sent to build client (5).
ts=11093 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(18): distributed work started.
ts=11093 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(18): distributed work completed.
ts=11093 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(18): sent to build client (4).
ts=11109 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(18): distributed work started.
ts=11109 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(18): distributed work completed.
ts=11109 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): pre-build pending
ts=11109 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): pre-build pending
ts=11109 BUILD: Scanning for circular dependencies...
ts=11109 BUILD: Processing dependencies complete
ts=11109 (onecore\ds\ds\src\aimx\prod\aimxsrv\server) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\server\objfre\amd64\_objects.mac created (133978920645638697), sources (133978544472720915), sources recorded (133978544472720915)
ts=11109 (onecore\ds\ds\src\aimx\prod\mcpprotocollib) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpprotocollib\objfre\amd64\_objects.mac is current (133978845263456911), sources (133977770786641380), sources recorded (133977770786641380)
ts=11109 (onecore\ds\ds\src\aimx\prod\llmclientlib) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\llmclientlib\objfre\amd64\_objects.mac is current (133978845263456911), sources (133969941762565131), sources recorded (133969941762565131)
ts=11109 (onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller\objfre\amd64\_objects.mac created (133978920645678787), sources (133969941762565131), sources recorded (133969941762565131)
ts=11109 (onecore\ds\ds\src\aimx\prod\aimxsrv\dll) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\dll\objfre\amd64\_objects.mac created (133978920645688864), sources (133977770693216991), sources recorded (133977770693216991)
ts=11109 (onecore\ds\ds\src\aimx\prod\aimxsrv\idl) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\idl\objfre\amd64\_objects.mac created (133978920645708943), sources (133969941762565131), sources recorded (133969941762565131)
ts=11109 (onecore\ds\ds\src\aimx\prod\mcpserversample\lib) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\mcpserversample\lib\objfre\amd64\_objects.mac is current (133978845263637949), sources (133977770786641380), sources recorded (133977770786641380)
ts=11109 (onecore\ds\ds\src\aimx\prod\admcpsvr) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\admcpsvr\objfre\amd64\_objects.mac is current (133978845263698079), sources (133977770690027574), sources recorded (133977770690027574)
ts=11125 (onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll\objfre\amd64\_objects.mac created (133978920645728937), sources (133969941762565131), sources recorded (133969941762565131)
ts=11125 (onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib\objfre\amd64\_objects.mac created (133978920645739477), sources (133969941762565131), sources recorded (133969941762565131)
ts=11125 (onecore\ds\ds\src\aimx\prod\aimxsrv\powershell) e:\os\obj\amd64fre\onecore\ds\ds\src\aimx\prod\aimxsrv\powershell\objfre\amd64\_objects.mac created (133978920645759538), sources (133970660830610246), sources recorded (133970660830610246)
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): in transit: passQ->distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): submitted to distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): in transit: passQ->distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): submitted to distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): sent to build client (5).
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): in transit: passQ->distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): sent to build client (4).
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): submitted to distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): distributed work started.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): in transit: passQ->distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): sent to build client (6).
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): submitted to distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(12): in transit: passQ->distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(12): submitted to distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): in transit: passQ->distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): sent to build client (1).
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): submitted to distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): in transit: passQ->distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(12): sent to build client (7).
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): submitted to distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(19): in transit: passQ->distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): distributed work started.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(19): submitted to distributedQ.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): distributed work started.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work started.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): sent to build client (8).
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(12): distributed work started.
ts=11375 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(18): in transit: passQ->distributedQ.
ts=11390 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): sent to build client (9).
ts=11390 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): distributed work started.
ts=11390 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(18): submitted to distributedQ.
ts=11390 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): distributed work started.
ts=11390 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(19): sent to build client (10).
ts=11390 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(19): distributed work started.
ts=11390 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(18): sent to build client (11).
ts=11390 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(18): distributed work started.
ts=13265 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(19): distributed work completed.
ts=13265 (PASS0) onecore\ds\ds\src\aimx\prod\llmclientlib(19): operation completed.
ts=13265 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(18): distributed work completed.
ts=13265 (PASS0) onecore\ds\ds\src\aimx\prod\admcpsvr(18): operation completed.
ts=13281 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): distributed work completed.
ts=13281 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): operation completed.
ts=13312 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): distributed work completed.
ts=13312 (PASS0) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): operation completed.
ts=13328 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work completed.
ts=13328 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): operation completed.
ts=13343 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): distributed work completed.
ts=13343 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): operation completed.
ts=13390 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): distributed work completed.
ts=13390 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): operation completed.
ts=13859 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(12): distributed work completed.
ts=13859 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\idl(12): operation completed.
ts=14500 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): distributed work completed.
ts=14500 (PASS0) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): operation completed.
ts=15437 (PASS0) processing complete.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): in transit: passQ->distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): submitted to distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): in transit: passQ->distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): submitted to distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): in transit: passQ->distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): sent to build client (12).
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): submitted to distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): sent to build client (4).
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): in transit: passQ->distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): distributed work started.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): sent to build client (7).
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): submitted to distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\mcpprotocollib(20): in transit: passQ->distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\mcpprotocollib(20): submitted to distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): in transit: passQ->distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): submitted to distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): in transit: passQ->distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): sent to build client (6).
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): submitted to distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(19): in transit: passQ->distributedQ.
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\mcpprotocollib(20): sent to build client (8).
ts=15437 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(19): submitted to distributedQ.
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): sent to build client (1).
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(18): in transit: passQ->distributedQ.
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(18): submitted to distributedQ.
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): sent to build client (9).
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(19): sent to build client (5).
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): distributed work started.
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): distributed work started.
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(18): sent to build client (11).
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): distributed work started.
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\mcpprotocollib(20): distributed work started.
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): distributed work started.
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): distributed work started.
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(19): distributed work started.
ts=15453 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(18): distributed work started.
ts=19515 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): distributed work completed.
ts=19515 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): operation completed.
ts=21187 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(19): distributed work completed.
ts=21187 (PASS1) onecore\ds\ds\src\aimx\prod\llmclientlib(19): operation completed.
ts=21203 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): distributed work completed.
ts=21203 (PASS1) onecore\ds\ds\src\aimx\prod\mcpserversample\lib(22): operation completed.
ts=21640 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): distributed work completed.
ts=21640 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): operation completed.
ts=21921 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): distributed work completed.
ts=21921 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib(13): operation completed.
ts=21921 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): distributed work completed.
ts=21937 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): operation completed.
ts=23718 (PASS1) onecore\ds\ds\src\aimx\prod\mcpprotocollib(20): distributed work completed.
ts=23718 (PASS1) onecore\ds\ds\src\aimx\prod\mcpprotocollib(20): operation completed.
ts=33828 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(18): distributed work completed.
ts=33828 (PASS1) onecore\ds\ds\src\aimx\prod\admcpsvr(18): operation completed.
ts=41953 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): distributed work completed.
ts=41953 
READYQ INSERTED: e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\server (PASS1 RC:0, pending:1, queued:1)
READYQ INSERTED: e:\os\src\onecore\ds\ds\src\aimx\prod\aimxsrv\dll (PASS1 RC:0, pending:0, queued:2)
ts=41953 (PASS1) onecore\ds\ds\src\aimx\prod\cpprestsdk(17): operation completed.
ts=41953 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): transition: readyQ->passQ.
ts=41953 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): transition: readyQ->passQ.
ts=41953 (PASS1) flush readyQ: 2 work items transitioned to pass queue.
ts=41953 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): in transit: passQ->distributedQ.
ts=41953 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): submitted to distributedQ.
ts=41953 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): in transit: passQ->distributedQ.
ts=41953 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): submitted to distributedQ.
ts=41953 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): sent to build client (10).
ts=41953 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): sent to build client (9).
ts=41953 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work started.
ts=41953 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): distributed work started.
ts=47546 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work completed.
ts=47546 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): operation completed.
ts=56937 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): distributed work completed.
ts=56937 (PASS1) onecore\ds\ds\src\aimx\prod\aimxsrv\server(15): operation completed.
ts=57125 (PASS1) processing complete.
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): in transit: passQ->distributedQ.
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): submitted to distributedQ.
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): in transit: passQ->distributedQ.
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): submitted to distributedQ.
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): sent to build client (11).
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): in transit: passQ->distributedQ.
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): sent to build client (9).
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): distributed work started.
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): submitted to distributedQ.
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): in transit: passQ->distributedQ.
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): submitted to distributedQ.
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): sent to build client (10).
ts=57125 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): sent to build client (8).
ts=57140 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work started.
ts=57140 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): distributed work started.
ts=57140 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): distributed work started.
ts=60859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): distributed work completed.
ts=60859 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\powershell(14): operation completed.
ts=63531 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): distributed work completed.
ts=63531 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll(11): operation completed.
ts=64343 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): distributed work completed.
ts=64343 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller(16): operation completed.
ts=69156 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): distributed work completed.
ts=69156 (PASS2) onecore\ds\ds\src\aimx\prod\aimxsrv\dll(10): operation completed.
ts=69265 (PASS2) processing complete.
ts=69265 PASS_INDEPENDENT processing complete.
ts=69265 PASS_INDEPENDENT processing complete.

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\server (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\server
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\server
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=1
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=1
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\mcpprotocollib 
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=0
  CLEAN_PASS1=1
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=0
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=0
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=1
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\mcpprotocollib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\llmclientlib 
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=1
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\llmclientlib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\llmclientlib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\cpprestsdk 
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=0
  CLEAN_PASS1=1
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=0
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=0
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=1
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=1
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\cpprestsdk
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=0
    MSBUILD_COMMAND_LINE=1
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=0
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=0
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\serviceinstaller
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\dll (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=1
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=1
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\idl (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=0
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=0
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\idl
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample\lib 
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=1
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample\lib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\mcpserversample\lib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\admcpsvr 
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=0
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=0
  CLEAN_PASS_INDEPENDENT=0
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=0
  QUEUE_TO_PASS_LIST_2=0
  QUEUE_TO_PASS_LIST_3=1
  ADD_DEPENDENCIES_TO_GRAPH=0
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\admcpsvr
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\admcpsvr
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\dll
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=0
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=0
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\client\lib
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

DIR EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell (IN FOCUS)
  CREATED=1
  FOUND_IN_DIRS=1
  INITIAL_DIRECTORY=0
  FOUND_IN_DATABASE=1
  CLEAN_ALL=1
  CLEAN_ALL_FORCED=0
  CLEAN_PASS0=1
  CLEAN_PASS1=1
  CLEAN_PASS2=1
  CLEAN_PASS_INDEPENDENT=1
  ALLOC_WORKITEM_PASS0=1
  ALLOC_WORKITEM_PASS1=1
  ALLOC_WORKITEM_PASS2=1
  ALLOC_WORKITEM_PASS_INDEPENDENT=0
  SOURCES_READ=1
  NODE_DIR=1
  DIRS_PROJ_SKIPPED=0
  MSBUILD_PROJECT_FILE_READ=0
  MSBUILD_POST_BUILD_PROJECT_FILE_READ=0
  TEST_CODE=0
  SAMPLE_CODE=0
  TESTCODE_SKIPPED=0
  PASS0_NEEDED=1
  PASS1_NEEDED=1
  PASS2_NEEDED=1
  PASS3_NEEDED=0
  SOURCES_DEP_READ=0
  QUEUE_TO_PASS_LIST_1=1
  QUEUE_TO_PASS_LIST_2=1
  QUEUE_TO_PASS_LIST_3=0
  ADD_DEPENDENCIES_TO_GRAPH=1
  SET_NON_PRODUCT=0
  SET_NON_CRITICAL=0
  SET_CS_WINDOWS=0
  SET_CS_XBOX=0
  SET_CS_PHONE=0
  SET_CS_AZURE=0
  SCAN_COMPLETE=1

  BUILD PASS0 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=1
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS1 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=1
    ADDED_TO_PASS2_Q=0
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1

  BUILD PASS2 EVENTS: onecore\ds\ds\src\aimx\prod\aimxsrv\powershell
    BUILD_CREATED=1
    TASK_CREATED=0
    ADDED_TO_DEPENDENCY_GRAPH=1
    ADDED_TO_PASSQ=1
    ADDED_TO_TASKQ=0
    TRANSITION_STANDBYQ_TO_PASSQ=0
    ADDED_TO_PASS0_Q=0
    ADDED_TO_PASS1_Q=0
    ADDED_TO_PASS2_Q=1
    ADDED_TO_PASS3_Q=0
    ADDED_TO_PASS_INDEPENDENT_Q=0
    ADDED_TO_TASK_Q=0
    REMOVED_FROM_PASSQ=1
    DEPENDENT_MOVED_FROM_READYQ_TO_PASSQ=0
    NMAKE_COMMAND_LINE=1
    MSBUILD_COMMAND_LINE=0
    SUBMIT_TO_DISTRIBUTED_SCHEDULER=1
    DISTRIBUTED_SCHEDULER_WORK_STARTED=1
    DISTRIBUTED_SCHEDULER_WORK_COMPLETED=1
    ADDED_TO_READYQ=0
    BYPASSED=0
    SENT_TO_CLIENT=1
    INCREMENTAL=0
    BUILD_PASS_TERMINATED_FOR_ERRORS=0
    BUILD_OPERATION_COMPLETE=1
