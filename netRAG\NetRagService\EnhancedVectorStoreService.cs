using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace NetRagService;

/// <summary>
/// Enhanced vector store service with usage frequency-based ranking
/// </summary>
public class EnhancedVectorStoreService : IVectorStoreService
{
    private readonly IVectorStoreService _baseVectorStore;
    private readonly ILogger<EnhancedVectorStoreService> _logger;
    
    // Usage frequency weights for different categories
    private readonly Dictionary<string, float> _usageWeights = new()
    {
        ["very_common"] = 1.5f,
        ["common"] = 1.2f,
        ["moderate"] = 1.0f,
        ["rare"] = 0.8f
    };

    public EnhancedVectorStoreService(IVectorStoreService baseVectorStore, ILogger<EnhancedVectorStoreService> logger)
    {
        _baseVectorStore = baseVectorStore;
        _logger = logger;
    }

    public Task UpsertAsync(string id, ReadOnlyMemory<float> embedding, Dictionary<string, object> metadata)
    {
        return _baseVectorStore.UpsertAsync(id, embedding, metadata);
    }

    public async Task<List<SearchResult>> SearchAsync(ReadOnlyMemory<float> queryEmbedding, int limit = 3)
    {
        // Get more results initially to allow for re-ranking
        var initialLimit = Math.Max(limit * 2, 20);
        var baseResults = await _baseVectorStore.SearchAsync(queryEmbedding, initialLimit);
        
        // Apply usage frequency boost to scores
        var enhancedResults = baseResults.Select(result => 
        {
            var enhancedResult = new SearchResult
            {
                Id = result.Id,
                Score = CalculateEnhancedScore(result),
                Metadata = result.Metadata
            };
            
            return enhancedResult;
        }).ToList();
        
        // Re-sort by enhanced score and take the requested limit
        var finalResults = enhancedResults
            .OrderByDescending(r => r.Score)
            .Take(limit)
            .ToList();
            
        _logger.LogDebug("Enhanced ranking applied. Original top score: {OriginalScore:F4}, Enhanced top score: {EnhancedScore:F4}", 
            baseResults.FirstOrDefault()?.Score ?? 0, 
            finalResults.FirstOrDefault()?.Score ?? 0);
            
        return finalResults;
    }

    private float CalculateEnhancedScore(SearchResult result)
    {
        var baseScore = result.Score;
        
        // Get usage frequency information from metadata
        var usageFrequency = GetUsageFrequency(result.Metadata);
        var usageCategory = GetUsageCategory(result.Metadata);
        
        // Calculate frequency boost
        var frequencyBoost = CalculateFrequencyBoost(usageFrequency, usageCategory);
        
        // Apply boost to base semantic similarity score
        var enhancedScore = baseScore * frequencyBoost;
        
        // Log the boost for debugging
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            var commandName = result.Metadata.TryGetValue("command_name", out var name) ? name.ToString() : "Unknown";
            _logger.LogDebug("Command: {CommandName}, Base Score: {BaseScore:F4}, Frequency: {Frequency}, Category: {Category}, Boost: {Boost:F2}, Enhanced Score: {EnhancedScore:F4}",
                commandName, baseScore, usageFrequency, usageCategory, frequencyBoost, enhancedScore);
        }
        
        return enhancedScore;
    }

    private int GetUsageFrequency(Dictionary<string, object> metadata)
    {
        if (metadata.TryGetValue("usage_frequency", out var freq) && int.TryParse(freq.ToString(), out var frequency))
        {
            return frequency;
        }
        return 0; // Default for commands without frequency data
    }

    private string GetUsageCategory(Dictionary<string, object> metadata)
    {
        if (metadata.TryGetValue("usage_category", out var category))
        {
            return category.ToString() ?? "moderate";
        }
        return "moderate"; // Default category
    }

    private float CalculateFrequencyBoost(int usageFrequency, string usageCategory)
    {
        // Base boost from category
        var categoryBoost = _usageWeights.TryGetValue(usageCategory.ToLower(), out var weight) ? weight : 1.0f;
        
        // Additional boost from raw frequency (logarithmic scale to prevent extreme values)
        var frequencyBoost = usageFrequency > 0 ? 1.0f + (float)Math.Log10(usageFrequency + 1) * 0.1f : 1.0f;
        
        // Combine both boosts
        return categoryBoost * frequencyBoost;
    }

    // Delegate other methods to base implementation
    public Task ClearAsync() => _baseVectorStore.ClearAsync();
    public Task<int> GetVectorCountAsync() => _baseVectorStore.GetVectorCountAsync();
    public Task SaveToDiskAsync(string filePath) => _baseVectorStore.SaveToDiskAsync(filePath);
    public Task LoadFromDiskAsync(string filePath) => _baseVectorStore.LoadFromDiskAsync(filePath);
}
