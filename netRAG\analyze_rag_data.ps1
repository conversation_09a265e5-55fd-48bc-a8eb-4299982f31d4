# Simple analysis of RAG-optimized data
$ragData = Get-Content 'ad_powershell_final_rag.json' -Raw | ConvertFrom-Json

Write-Host "=== Enhanced RAG Database System Analysis ===" -ForegroundColor Green
Write-Host

Write-Host "📊 RAG-Optimized Data Structure:" -ForegroundColor Yellow
Write-Host "  • Total commands: $($ragData.commands.Count)" -ForegroundColor Cyan
Write-Host "  • Optimization version: $($ragData.metadata.optimization_version)" -ForegroundColor Cyan
Write-Host "  • Created: $($ragData.metadata.created_at)" -ForegroundColor Cyan
Write-Host

$sampleCommand = $ragData.commands[0]
Write-Host "🔍 Sample Command Analysis:" -ForegroundColor Yellow
Write-Host "  • Command: $($sampleCommand.command_name)" -ForegroundColor Cyan
Write-Host "  • Verb: $($sampleCommand.verb)" -ForegroundColor Cyan
Write-Host "  • Category: $($sampleCommand.category)" -ForegroundColor Cyan
Write-Host "  • Keywords: $($sampleCommand.keywords.Count) items" -ForegroundColor Cyan
Write-Host "  • RAG document: $($sampleCommand.rag_document.Length) characters" -ForegroundColor Cyan
Write-Host "  • Parameters: $($sampleCommand.parameters.Count)" -ForegroundColor Cyan
Write-Host "  • Examples: $($sampleCommand.examples.Count)" -ForegroundColor Cyan
Write-Host

Write-Host "📋 Categories Distribution:" -ForegroundColor Yellow
$categories = $ragData.commands | Group-Object category | Sort-Object Count -Descending
foreach ($cat in $categories) {
    Write-Host "  • $($cat.Name): $($cat.Count) commands" -ForegroundColor Cyan
}
Write-Host

Write-Host "🎯 Enhanced Features Available:" -ForegroundColor Yellow
Write-Host "  ✓ Vector embeddings from rag_document field" -ForegroundColor Green
Write-Host "  ✓ Keyword-based filtering using keywords array" -ForegroundColor Green
Write-Host "  ✓ Category and purpose-based search" -ForegroundColor Green
Write-Host "  ✓ Rich metadata for LLM context injection" -ForegroundColor Green
Write-Host "  ✓ Hybrid search capabilities" -ForegroundColor Green
Write-Host "  ✓ Enhanced parameter and example information" -ForegroundColor Green
Write-Host

Write-Host "🔍 Sample RAG Document (first 200 chars):" -ForegroundColor Yellow
$ragSample = $sampleCommand.rag_document.Substring(0, [Math]::Min(200, $sampleCommand.rag_document.Length))
Write-Host $ragSample -ForegroundColor Gray
Write-Host "..." -ForegroundColor Gray
Write-Host

Write-Host "🏷️ Sample Keywords:" -ForegroundColor Yellow
Write-Host ($sampleCommand.keywords -join ", ") -ForegroundColor Gray
Write-Host

Write-Host "✨ System Ready for Enhanced Search!" -ForegroundColor Green
