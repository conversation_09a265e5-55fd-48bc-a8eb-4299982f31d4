using System.Text.Json.Serialization;

namespace NetRagService;

// Enhanced data structures for RAG-optimized JSON format
public class RagOptimizedData
{
    public RagMetadata? Metadata { get; set; }
    public List<RagOptimizedCommand> Commands { get; set; } = new();
}

public class RagMetadata
{
    public string? CreatedAt { get; set; }
    public string? SourceFile { get; set; }
    public int TotalCommands { get; set; }
    public string? OptimizationVersion { get; set; }
    public string? Description { get; set; }
    public List<string>? Features { get; set; }
}

public class RagOptimizedCommand
{
    public string? CommandName { get; set; }
    public string? Verb { get; set; }
    public string? Noun { get; set; }
    public string? Module { get; set; }
    public string? Category { get; set; }
    public string? PrimaryPurpose { get; set; }
    public string? Description { get; set; }
    public List<string>? Keywords { get; set; }
    public string? RagDocument { get; set; }
    public List<string>? RequiredParameters { get; set; }
    public List<string>? OptionalParameters { get; set; }
    public List<ParameterData>? Parameters { get; set; }
    public List<ExampleData>? Examples { get; set; }
}

// Shared parameter and example data structures
public class ParameterData
{
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public string Type { get; set; } = "";
    public bool Mandatory { get; set; }
    public string Position { get; set; } = "";
    public bool PipelineInput { get; set; }
    
    // Legacy support
    public bool Required 
    { 
        get => Mandatory; 
        set => Mandatory = value; 
    }
}

public class ExampleData
{
    public string Title { get; set; } = "";
    public string Description { get; set; } = "";
    public string Code { get; set; } = "";
    public string Scenario { get; set; } = "";
}
