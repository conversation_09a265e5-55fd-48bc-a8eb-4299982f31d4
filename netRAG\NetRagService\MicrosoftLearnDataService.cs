using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace NetRagService;

public class MicrosoftLearnDataService : IHostedService
{
    private readonly VectorStoreService _vectorStoreService;
    private readonly PowerShellCommandIngestionService _ingestionService;
    private readonly ILogger<MicrosoftLearnDataService> _logger;
    private readonly RagConfiguration _configuration;
    private readonly string _dataFilePath;
    private readonly bool _autoIngestOnStartup;

    public MicrosoftLearnDataService(
        VectorStoreService vectorStoreService,
        PowerShellCommandIngestionService ingestionService,
        ILogger<MicrosoftLearnDataService> logger,
        RagConfiguration configuration)
    {
        _vectorStoreService = vectorStoreService;
        _ingestionService = ingestionService;
        _logger = logger;
        _configuration = configuration;

        // Get configuration values
        _dataFilePath = Path.IsPathRooted(_configuration.MicrosoftLearnDataFilePath)
            ? _configuration.MicrosoftLearnDataFilePath
            : Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", _configuration.MicrosoftLearnDataFilePath);
        _autoIngestOnStartup = _configuration.MicrosoftLearnDataAutoIngestOnStartup;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting Microsoft Learn Data Service");

        try
        {
            // First, try to load existing vector store from disk
            await _vectorStoreService.LoadFromDiskAsync();
            
            var existingVectorCount = await _vectorStoreService.GetVectorCountAsync();
            
            if (existingVectorCount > 0)
            {
                _logger.LogInformation("Loaded existing vector store with {VectorCount} vectors", existingVectorCount);
                
                // Check if we should re-ingest (e.g., if data file is newer than vector store)
                if (_autoIngestOnStartup && ShouldReingest())
                {
                    _logger.LogInformation("Data file appears to be newer than vector store. Re-ingesting...");
                    await IngestDataAsync();
                }
            }
            else if (_autoIngestOnStartup)
            {
                _logger.LogInformation("No existing vector store found. Ingesting Microsoft Learn data...");
                await IngestDataAsync();
            }
            else
            {
                _logger.LogInformation("Auto-ingestion disabled and no existing vector store found");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Microsoft Learn Data Service");
            throw;
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping Microsoft Learn Data Service");

        // Note: No saving to disk - VectorStoreService is now read-only
        // Database.bin files are created by the separate DatabaseConverter utility

        await Task.CompletedTask;
    }

    public async Task IngestDataAsync()
    {
        if (!File.Exists(_dataFilePath))
        {
            _logger.LogWarning("Microsoft Learn data file not found at: {FilePath}", _dataFilePath);
            return;
        }

        _logger.LogInformation("Starting ingestion of Microsoft Learn data from: {FilePath}", _dataFilePath);
        
        var startTime = DateTime.UtcNow;
        
        try
        {
            // Clear existing data
            await _vectorStoreService.ClearAsync();
            
            // Ingest new data
            await _ingestionService.IngestMicrosoftLearnDataAsync(_dataFilePath);

            // Note: No saving to disk - VectorStoreService is now read-only
            // Database.bin files are created by the separate DatabaseConverter utility

            var duration = DateTime.UtcNow - startTime;
            var vectorCount = await _vectorStoreService.GetVectorCountAsync();
            
            _logger.LogInformation("Successfully completed ingestion in {Duration}. Total vectors: {VectorCount}", 
                duration, vectorCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to ingest Microsoft Learn data");
            throw;
        }
    }

    public Task<List<SearchResult>> SearchCommandsAsync(string query, int limit = 5)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return Task.FromResult(new List<SearchResult>());
        }

        try
        {
            // This would need the embedding service to generate query embedding
            // For now, we'll return empty results - this should be implemented
            // when the search endpoint is created
            _logger.LogInformation("Searching for PowerShell commands with query: {Query}", query);

            // TODO: Implement actual search with query embedding
            return Task.FromResult(new List<SearchResult>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search PowerShell commands");
            throw;
        }
    }

    private bool ShouldReingest()
    {
        try
        {
            if (!File.Exists(_dataFilePath))
                return false;

            var dataFileInfo = new FileInfo(_dataFilePath);
            var vectorStoreFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "vector_store.bin");
            
            if (!File.Exists(vectorStoreFilePath))
                return true;

            var vectorStoreFileInfo = new FileInfo(vectorStoreFilePath);
            
            // Re-ingest if data file is newer than vector store
            return dataFileInfo.LastWriteTime > vectorStoreFileInfo.LastWriteTime;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check if re-ingestion is needed. Defaulting to false.");
            return false;
        }
    }
}

public class MicrosoftLearnDataConfiguration
{
    public string FilePath { get; set; } = string.Empty;
    public bool AutoIngestOnStartup { get; set; } = true;
    public int SearchResultLimit { get; set; } = 5;
}
