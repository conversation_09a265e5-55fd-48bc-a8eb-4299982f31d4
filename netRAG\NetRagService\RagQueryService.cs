using Microsoft.Extensions.AI;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel.ChatCompletion;

namespace NetRagService;

public class RagQueryService
{
    private readonly IEmbeddingGenerator<string, Embedding<float>> _embeddingService;
    private readonly IChatCompletionService _chatService;
    private readonly VectorStoreService _vectorStoreService;
    private readonly DocumentIngestionConfig _config;
    private readonly ILogger<RagQueryService> _logger;

    public RagQueryService(
        IEmbeddingGenerator<string, Embedding<float>> embeddingService,
        IChatCompletionService chatService,
        VectorStoreService vectorStoreService,
        DocumentIngestionConfig config,
        ILogger<RagQueryService> logger)
    {
        _embeddingService = embeddingService;
        _chatService = chatService;
        _vectorStoreService = vectorStoreService;
        _config = config;
        _logger = logger;
    }

    public async Task<string> QueryAsync(string question)
    {
        if (string.IsNullOrWhiteSpace(question))
        {
            throw new ArgumentException("Question cannot be null or empty", nameof(question));
        }

        _logger.LogInformation("Processing RAG query: {Question}", question);

        try
        {
            // 1. Generate query embedding
            var queryEmbeddingResult = await _embeddingService.GenerateAsync(question);
            var queryEmbedding = queryEmbeddingResult.Vector;
            
            _logger.LogDebug("Generated embedding for query with {Dimensions} dimensions", queryEmbedding.Length);

            // 2. Search for relevant chunks
            var searchResults = await _vectorStoreService.SearchAsync(queryEmbedding, limit: _config.SearchLimit);

            _logger.LogInformation("Found {ResultCount} relevant chunks", searchResults.Count);

            // 3. Build context from retrieved chunks
            var contextParts = new List<string>();
            foreach (var result in searchResults)
            {
                if (result.Metadata.TryGetValue("text", out var text))
                {
                    contextParts.Add(text.ToString() ?? string.Empty);
                    _logger.LogDebug("Added chunk with score {Score}", result.Score);
                }
            }

            var context = string.Join("\n\n", contextParts);
            
            if (string.IsNullOrWhiteSpace(context))
            {
                _logger.LogWarning("No relevant context found for query");
                return "I couldn't find relevant information to answer your question.";
            }

            // 4. Generate response using context
            var prompt = $@"Based on the following context, please answer the question. 
If the context doesn't contain enough information to answer the question, please say so.

Context:
{context}

Question: {question}

Answer:";

            var chatHistory = new ChatHistory();
            chatHistory.AddSystemMessage("You are a helpful assistant that answers questions based on the provided context. Be concise and accurate.");
            chatHistory.AddUserMessage(prompt);

            // 5. Stream response from Foundry Local
            var fullMessage = string.Empty;
            await foreach (var chatUpdate in _chatService.GetStreamingChatMessageContentsAsync(chatHistory, cancellationToken: default))
            {
                if (chatUpdate.Content is { Length: > 0 })
                {
                    fullMessage += chatUpdate.Content;
                }
            }

            var response = fullMessage ?? "I couldn't generate a response.";
            _logger.LogInformation("Generated response with {Length} characters", response.Length);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process RAG query: {Question}", question);
            throw;
        }
    }

    public async Task<RagQueryResult> QueryWithDetailsAsync(string question)
    {
        if (string.IsNullOrWhiteSpace(question))
        {
            throw new ArgumentException("Question cannot be null or empty", nameof(question));
        }

        _logger.LogInformation("Processing detailed RAG query: {Question}", question);

        try
        {
            // Generate query embedding
            var queryEmbeddingResult = await _embeddingService.GenerateAsync(question);
            var queryEmbedding = queryEmbeddingResult.Vector;

            // Search for relevant chunks
            var searchResults = await _vectorStoreService.SearchAsync(queryEmbedding, limit: _config.SearchLimit);

            // Build context and collect source information
            var contextParts = new List<string>();
            var sources = new List<SourceInfo>();

            foreach (var result in searchResults)
            {
                if (result.Metadata.TryGetValue("text", out var text))
                {
                    contextParts.Add(text.ToString() ?? string.Empty);

                    sources.Add(new SourceInfo
                    {
                        DocumentId = result.Metadata.GetValueOrDefault("document_id")?.ToString() ?? "unknown",
                        DocumentPath = result.Metadata.GetValueOrDefault("document_path")?.ToString() ?? "unknown",
                        ChunkIndex = int.TryParse(result.Metadata.GetValueOrDefault("chunk_index")?.ToString(), out var idx) ? idx : -1,
                        Score = result.Score,
                        Text = text.ToString() ?? string.Empty
                    });
                }
            }

            var context = string.Join("\n\n", contextParts);
            
            if (string.IsNullOrWhiteSpace(context))
            {
                return new RagQueryResult
                {
                    Question = question,
                    Answer = "I couldn't find relevant information to answer your question.",
                    Sources = sources,
                    ContextUsed = context
                };
            }

            // Generate response
            var answer = await QueryAsync(question);

            return new RagQueryResult
            {
                Question = question,
                Answer = answer,
                Sources = sources,
                ContextUsed = context
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process detailed RAG query: {Question}", question);
            throw;
        }
    }
}

public class RagQueryResult
{
    public string Question { get; set; } = string.Empty;
    public string Answer { get; set; } = string.Empty;
    public List<SourceInfo> Sources { get; set; } = new();
    public string ContextUsed { get; set; } = string.Empty;
}

public class SourceInfo
{
    public string DocumentId { get; set; } = string.Empty;
    public string DocumentPath { get; set; } = string.Empty;
    public int ChunkIndex { get; set; }
    public float Score { get; set; }
    public string Text { get; set; } = string.Empty;
}
