# PowerShell Active Directory JSON Optimizer for RAG
# Restructures comprehensive scraped data into RAG-optimized JSON format
# Keeps it as JSON but optimizes content structure for better search and LLM integration

param(
    [string]$InputFile = "comprehensive_ad_data.json",
    [string]$OutputFile = "ad_powershell_rag_optimized.json",
    [switch]$Verbose
)

Write-Host "PowerShell Active Directory JSON RAG Optimizer" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Load the comprehensive data
if (-not (Test-Path $InputFile)) {
    Write-Error "Input file not found: $InputFile"
    exit 1
}

Write-Host "Loading comprehensive AD data from: $InputFile" -ForegroundColor Yellow
try {
    $rawData = Get-Content $InputFile -Raw | ConvertFrom-Json
    Write-Host "Successfully loaded data with $($rawData.scraped_commands.Count) commands" -ForegroundColor Green
}
catch {
    Write-Error "Failed to parse JSON file: $($_.Exception.Message)"
    exit 1
}

# Initialize RAG-optimized JSON structure
$ragOptimizedData = @{
    metadata = @{
        created_at = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        source_file = $InputFile
        total_commands = 0
        optimization_version = "1.0"
        description = "RAG-optimized PowerShell Active Directory command data for LLM integration"
        optimization_features = @(
            "Flattened searchable content",
            "Enhanced parameter descriptions",
            "Contextual examples with explanations",
            "Search keywords and tags",
            "LLM-friendly formatting",
            "Categorized by use cases"
        )
    }
    commands = @()
}

Write-Host "Processing and optimizing commands..." -ForegroundColor Yellow

$processedCount = 0
foreach ($command in $rawData.scraped_commands) {
    try {
        if ($command.status -eq "failed") {
            continue
        }

        # Safely get command name
        $commandName = ""
        if ($command.page_info -and $command.page_info.command_name) {
            $commandName = $command.page_info.command_name
        } elseif ($command.command) {
            $commandName = $command.command
        } else {
            Write-Warning "Skipping command with no name"
            continue
        }

        $processedCount++

        if ($Verbose) {
            Write-Host "  Optimizing: $commandName" -ForegroundColor Cyan
        }
    
    # Create RAG-optimized command entry
    $optimizedCommand = @{
        # Core identification - flattened for easy search
        command_name = $commandName
        verb = ($commandName -split '-')[0]
        noun = ($commandName -split '-')[1]
        module = "ActiveDirectory"
        
        # Enhanced searchable content - safely handle null values
        synopsis = if ($command.synopsis) { $command.synopsis } else { "" }
        description = if ($command.description) { $command.description } else { "" }
        full_description = "$($command.synopsis) $($command.description)" -replace '\s+', ' '
        
        # Categorization for better retrieval
        category = Get-CommandCategory -CommandName $commandName
        primary_use_case = Get-PrimaryUseCase -CommandName $commandName
        use_cases = Get-UseCases -CommandName $commandName
        
        # Search optimization
        search_keywords = Generate-SearchKeywords -CommandName $commandName -Synopsis $command.synopsis -Description $command.description
        search_tags = Generate-SearchTags -CommandName $commandName
        
        # LLM context - what the LLM needs to know
        llm_summary = Generate-LLMSummary -CommandName $commandName -Synopsis $command.synopsis -Description $command.description
        when_to_use = Generate-WhenToUse -CommandName $commandName
        common_scenarios = Generate-CommonScenarios -CommandName $commandName
        
        # Optimized parameters - flattened and enhanced
        parameters = @()
        parameter_summary = ""
        required_parameters = @()
        optional_parameters = @()
        
        # Optimized examples - enhanced with context
        examples = @()
        example_scenarios = @()
        
        # Additional context for LLM
        related_commands = Get-RelatedCommands -CommandName $commandName
        best_practices = Get-BestPractices -CommandName $commandName
        common_errors = Get-CommonErrors -CommandName $commandName
        
        # Original metadata
        source_url = $command.url
        scraped_at = $command.scraped_at
    }
    
    # Process and optimize parameters - safely handle null parameters
    $paramDescriptions = @()
    if ($command.parameters -and $command.parameters.GetType().Name -eq "PSCustomObject") {
        $paramNames = $command.parameters.PSObject.Properties.Name
    } elseif ($command.parameters -and $command.parameters.Keys) {
        $paramNames = $command.parameters.Keys
    } else {
        $paramNames = @()
    }

    foreach ($paramName in $paramNames) {
        try {
            $param = $command.parameters.$paramName
        
            $optimizedParam = @{
                name = $paramName
                description = if ($param.description) { $param.description } else { "" }
                type = if ($param.properties -and $param.properties.Type) { $param.properties.Type } else { "" }
                mandatory = if ($param.properties -and $param.properties.Mandatory) { ($param.properties.Mandatory -eq "True") } else { $false }
                position = if ($param.properties -and $param.properties.Position) { $param.properties.Position } else { "" }
                default_value = if ($param.properties -and $param.properties."Default value") { $param.properties."Default value" } else { "" }
                accepts_pipeline = if ($param.properties -and $param.properties."Value from pipeline") { ($param.properties."Value from pipeline" -eq "True") } else { $false }
                supports_wildcards = if ($param.properties -and $param.properties."Supports wildcards") { ($param.properties."Supports wildcards" -eq "True") } else { $false }
            
                # Enhanced for LLM understanding
                purpose = Get-ParameterPurpose -ParamName $paramName -Description $param.description
                typical_values = Get-TypicalValues -ParamName $paramName
                usage_examples = Get-ParameterUsageExamples -ParamName $paramName

                # Searchable content
                searchable_text = "$paramName $($param.description)" -replace '\s+', ' '
            }

            $optimizedCommand.parameters += $optimizedParam
            $paramDescriptions += "$paramName`: $($param.description)"

            if ($optimizedParam.mandatory) {
                $optimizedCommand.required_parameters += $paramName
            } else {
                $optimizedCommand.optional_parameters += $paramName
            }
        }
        catch {
            Write-Warning "Error processing parameter $paramName for command $commandName`: $($_.Exception.Message)"
        }
    }
    
    $optimizedCommand.parameter_summary = $paramDescriptions -join "; "
    
    # Process and optimize examples - safely handle null examples
    if ($command.examples -and $command.examples.Count -gt 0) {
        foreach ($example in $command.examples) {
            try {
                $optimizedExample = @{
                    title = if ($example.title) { $example.title } else { "Example" }
                    description = if ($example.description) { $example.description } else { "" }
                    code = if ($example.code_blocks) { ($example.code_blocks -join "`n") } else { "" }

                    # Enhanced for LLM
                    scenario = Extract-Scenario -Title $example.title -Description $example.description
                    intent = Extract-Intent -Title $example.title -Description $example.description
                    explanation = Generate-ExampleExplanation -Example $example -CommandName $commandName
                    parameters_used = if ($example.code_blocks) { Extract-ParametersFromCode -Code ($example.code_blocks -join " ") } else { @() }

                    # Searchable content
                    searchable_text = "$($example.title) $($example.description) $($example.code_blocks -join ' ')" -replace '\s+', ' '
                }

                $optimizedCommand.examples += $optimizedExample
                $optimizedCommand.example_scenarios += $optimizedExample.scenario
            }
            catch {
                Write-Warning "Error processing example for command $commandName`: $($_.Exception.Message)"
            }
        }
    }
    
        # Remove duplicates from example scenarios
        $optimizedCommand.example_scenarios = $optimizedCommand.example_scenarios | Sort-Object -Unique

        $ragOptimizedData.commands += $optimizedCommand
    }
    catch {
        Write-Warning "Error processing command $commandName`: $($_.Exception.Message)"
    }
}

# Helper functions for optimization
function Get-CommandCategory {
    param([string]$CommandName)
    
    $noun = ($CommandName -split '-')[1]
    
    switch -Regex ($noun) {
        'User' { return 'User Management' }
        'Group' { return 'Group Management' }
        'Computer' { return 'Computer Management' }
        'Domain|Forest' { return 'Domain Management' }
        'Replication|Site' { return 'Replication Management' }
        'ServiceAccount' { return 'Service Account Management' }
        'OrganizationalUnit' { return 'Organizational Unit Management' }
        'Policy|Authentication' { return 'Policy Management' }
        'Account|Object' { return 'Security Management' }
        default { return 'General Operations' }
    }
}

function Get-PrimaryUseCase {
    param([string]$CommandName)
    
    $verb = ($CommandName -split '-')[0]
    $noun = ($CommandName -split '-')[1]
    
    switch ($verb) {
        'Get' { return "Retrieve $noun information" }
        'New' { return "Create new $noun" }
        'Set' { return "Modify $noun properties" }
        'Remove' { return "Delete $noun" }
        'Add' { return "Add to $noun" }
        'Enable' { return "Enable $noun" }
        'Disable' { return "Disable $noun" }
        default { return "Manage $noun" }
    }
}

function Get-UseCases {
    param([string]$CommandName)
    
    $verb = ($CommandName -split '-')[0]
    $noun = ($CommandName -split '-')[1]
    
    $useCases = @()
    
    switch ($verb) {
        'Get' { 
            $useCases += "Query $noun information"
            $useCases += "Search for $noun objects"
            $useCases += "List $noun details"
            $useCases += "Filter $noun by criteria"
        }
        'New' { 
            $useCases += "Create new $noun"
            $useCases += "Add $noun to directory"
            $useCases += "Provision $noun"
        }
        'Set' { 
            $useCases += "Modify $noun properties"
            $useCases += "Update $noun configuration"
            $useCases += "Change $noun attributes"
        }
        'Remove' { 
            $useCases += "Delete $noun"
            $useCases += "Remove $noun from directory"
            $useCases += "Clean up $noun objects"
        }
        'Add' { 
            $useCases += "Add members to $noun"
            $useCases += "Associate with $noun"
            $useCases += "Include in $noun"
        }
        'Enable' { 
            $useCases += "Activate $noun"
            $useCases += "Turn on $noun"
            $useCases += "Enable $noun functionality"
        }
        'Disable' { 
            $useCases += "Deactivate $noun"
            $useCases += "Turn off $noun"
            $useCases += "Disable $noun functionality"
        }
    }
    
    return $useCases
}

function Generate-SearchKeywords {
    param([string]$CommandName, [string]$Synopsis, [string]$Description)
    
    $keywords = @()
    
    # Command parts
    $keywords += $CommandName
    $keywords += ($CommandName -split '-')[0]  # Verb
    $keywords += ($CommandName -split '-')[1]  # Noun
    
    # Extract meaningful words from synopsis and description
    $text = "$Synopsis $Description"
    $words = $text -split '\s+' | Where-Object { 
        $_.Length -gt 3 -and 
        $_ -notmatch '^(the|and|for|with|this|that|from|into|will|can|are|you|use|get|set)$' 
    }
    
    $keywords += $words | ForEach-Object { $_.ToLower().Trim('.,!?;:') } | Sort-Object -Unique
    
    return $keywords
}

function Generate-SearchTags {
    param([string]$CommandName)
    
    $tags = @()
    $verb = ($CommandName -split '-')[0]
    $noun = ($CommandName -split '-')[1]
    
    # Verb-based tags
    switch ($verb) {
        'Get' { $tags += @('query', 'search', 'retrieve', 'list') }
        'New' { $tags += @('create', 'add', 'provision', 'new') }
        'Set' { $tags += @('modify', 'update', 'change', 'configure') }
        'Remove' { $tags += @('delete', 'remove', 'cleanup') }
        'Add' { $tags += @('add', 'include', 'associate') }
        'Enable' { $tags += @('enable', 'activate', 'turn-on') }
        'Disable' { $tags += @('disable', 'deactivate', 'turn-off') }
    }
    
    # Noun-based tags
    switch -Regex ($noun) {
        'User' { $tags += @('user', 'account', 'person', 'employee') }
        'Group' { $tags += @('group', 'team', 'membership', 'security-group') }
        'Computer' { $tags += @('computer', 'machine', 'workstation', 'server') }
        'Domain' { $tags += @('domain', 'directory', 'forest') }
    }
    
    return $tags | Sort-Object -Unique
}

function Generate-LLMSummary {
    param([string]$CommandName, [string]$Synopsis, [string]$Description)
    
    $verb = ($CommandName -split '-')[0]
    $noun = ($CommandName -split '-')[1]
    
    $summary = "The $CommandName command is used to $($verb.ToLower()) $noun objects in Active Directory."
    
    if ($Synopsis) {
        $summary += " $Synopsis"
    }
    
    if ($Description -and $Description -ne $Synopsis) {
        $summary += " $Description"
    }
    
    return $summary
}

function Generate-WhenToUse {
    param([string]$CommandName)
    
    $verb = ($CommandName -split '-')[0]
    $noun = ($CommandName -split '-')[1]
    
    switch ($verb) {
        'Get' { return "Use when you need to find, search, or retrieve information about $noun objects" }
        'New' { return "Use when you need to create a new $noun in Active Directory" }
        'Set' { return "Use when you need to modify or update properties of an existing $noun" }
        'Remove' { return "Use when you need to delete a $noun from Active Directory" }
        'Add' { return "Use when you need to add something to a $noun (like members to a group)" }
        'Enable' { return "Use when you need to enable or activate a $noun" }
        'Disable' { return "Use when you need to disable or deactivate a $noun" }
        default { return "Use when you need to manage $noun objects in Active Directory" }
    }
}

function Generate-CommonScenarios {
    param([string]$CommandName)
    
    $verb = ($CommandName -split '-')[0]
    $noun = ($CommandName -split '-')[1]
    
    $scenarios = @()
    
    switch ($verb) {
        'Get' {
            $scenarios += "Finding specific $noun by name or properties"
            $scenarios += "Listing all $noun objects in a container"
            $scenarios += "Searching $noun with filters"
            $scenarios += "Retrieving $noun properties for reporting"
        }
        'New' {
            $scenarios += "Creating new $noun during onboarding"
            $scenarios += "Bulk creation of $noun objects"
            $scenarios += "Provisioning $noun with specific properties"
        }
        'Set' {
            $scenarios += "Updating $noun information"
            $scenarios += "Bulk modification of $noun properties"
            $scenarios += "Changing $noun configuration"
        }
    }
    
    return $scenarios
}

function Get-RelatedCommands {
    param([string]$CommandName)
    
    $noun = ($CommandName -split '-')[1]
    $verb = ($CommandName -split '-')[0]
    
    $related = @()
    
    # Same noun, different verbs
    @('Get', 'New', 'Set', 'Remove') | ForEach-Object {
        if ($_ -ne $verb) {
            $related += "$_-$noun"
        }
    }
    
    return $related
}

function Get-BestPractices {
    param([string]$CommandName)
    
    $practices = @()
    $verb = ($CommandName -split '-')[0]
    
    switch ($verb) {
        'Get' { 
            $practices += "Use -Filter parameter for better performance"
            $practices += "Specify -Properties to get additional attributes"
            $practices += "Use -Server to target specific domain controller"
        }
        'New' { 
            $practices += "Always specify required parameters"
            $practices += "Use -WhatIf to preview changes"
            $practices += "Consider using -PassThru to return created object"
        }
        'Set' { 
            $practices += "Use -Identity to specify target object"
            $practices += "Use -WhatIf to preview changes"
            $practices += "Be careful with bulk operations"
        }
        'Remove' { 
            $practices += "Always use -WhatIf first"
            $practices += "Use -Confirm for interactive confirmation"
            $practices += "Have backup strategy before removing objects"
        }
    }
    
    return $practices
}

function Get-CommonErrors {
    param([string]$CommandName)
    
    $errors = @()
    $verb = ($CommandName -split '-')[0]
    
    switch ($verb) {
        'Get' { 
            $errors += "Object not found - check spelling and existence"
            $errors += "Access denied - ensure proper permissions"
            $errors += "Server not available - check domain controller connectivity"
        }
        'New' { 
            $errors += "Object already exists - check for duplicates"
            $errors += "Missing required parameters"
            $errors += "Invalid parameter values"
        }
        'Set' { 
            $errors += "Object not found - verify identity parameter"
            $errors += "Cannot modify read-only properties"
            $errors += "Invalid property values"
        }
        'Remove' { 
            $errors += "Object not found or already deleted"
            $errors += "Cannot delete - object has dependencies"
            $errors += "Insufficient permissions to delete"
        }
    }
    
    return $errors
}

function Get-ParameterPurpose {
    param([string]$ParamName, [string]$Description)
    
    switch -Regex ($ParamName) {
        'Identity' { return "Specifies the target object to work with" }
        'Filter' { return "Defines criteria to filter results" }
        'Properties' { return "Specifies which properties to retrieve or display" }
        'Server' { return "Specifies which domain controller to connect to" }
        'SearchBase' { return "Defines the starting point for the search" }
        'Name' { return "Specifies the name of the object" }
        'Path' { return "Specifies the container where the object should be created" }
        default { return $Description }
    }
}

function Get-TypicalValues {
    param([string]$ParamName)
    
    switch -Regex ($ParamName) {
        'Identity' { return @('username', 'CN=User,OU=Users,DC=domain,DC=com', 'GUID') }
        'Filter' { return @('Name -like "John*"', 'Enabled -eq $true', 'Department -eq "IT"') }
        'Properties' { return @('*', 'Department,Title,Manager', 'MemberOf') }
        'Server' { return @('dc01.domain.com', 'domain.com') }
        'Enabled' { return @('$true', '$false') }
        'GroupScope' { return @('Global', 'Universal', 'DomainLocal') }
        'GroupCategory' { return @('Security', 'Distribution') }
        default { return @() }
    }
}

function Get-ParameterUsageExamples {
    param([string]$ParamName)
    
    switch -Regex ($ParamName) {
        'Identity' { return @('-Identity "john.doe"', '-Identity "CN=John Doe,OU=Users,DC=contoso,DC=com"') }
        'Filter' { return @('-Filter "Name -like `"John*`""', '-Filter "Enabled -eq `$true"') }
        'Properties' { return @('-Properties *', '-Properties Department,Title') }
        'Server' { return @('-Server "dc01.contoso.com"') }
        default { return @() }
    }
}

function Extract-Scenario {
    param([string]$Title, [string]$Description)
    
    switch -Regex ($Title) {
        'all|list' { return 'List All Objects' }
        'specific|particular' { return 'Get Specific Object' }
        'filter|search' { return 'Search and Filter' }
        'properties|attributes' { return 'Property Management' }
        'create|new' { return 'Object Creation' }
        'modify|update' { return 'Object Modification' }
        'remove|delete' { return 'Object Deletion' }
        default { return 'General Usage' }
    }
}

function Extract-Intent {
    param([string]$Title, [string]$Description)
    
    if ($Title -match 'Get all|List all') { return 'list_all' }
    if ($Title -match 'Get.*specific|Get a specified') { return 'get_specific' }
    if ($Title -match 'filter|search') { return 'search_filter' }
    if ($Title -match 'properties') { return 'get_properties' }
    if ($Title -match 'Create|New') { return 'create_new' }
    if ($Title -match 'Modify|Update|Set') { return 'modify_existing' }
    if ($Title -match 'Remove|Delete') { return 'remove_object' }
    
    return 'general_usage'
}

function Generate-ExampleExplanation {
    param($Example, [string]$CommandName)
    
    $explanation = "This example demonstrates how to use $CommandName"
    
    if ($Example.description) {
        $explanation += " to " + $Example.description.ToLower()
    }
    
    if ($Example.code_blocks -and $Example.code_blocks.Count -gt 0) {
        $code = $Example.code_blocks[0]
        $parameters = Extract-ParametersFromCode -Code $code
        if ($parameters.Count -gt 0) {
            $explanation += ". It uses the following parameters: " + ($parameters -join ', ')
        }
    }
    
    return $explanation
}

function Extract-ParametersFromCode {
    param([string]$Code)
    
    $parameters = @()
    $matches = [regex]::Matches($Code, '-(\w+)')
    foreach ($match in $matches) {
        $parameters += $match.Groups[1].Value
    }
    return $parameters | Sort-Object -Unique
}

# Finalize metadata
$ragOptimizedData.metadata.total_commands = $processedCount

Write-Host "Saving RAG-optimized JSON..." -ForegroundColor Yellow

# Save the optimized JSON
$json = $ragOptimizedData | ConvertTo-Json -Depth 20
$json | Out-File -FilePath $OutputFile -Encoding UTF8

# Display summary
Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "JSON RAG OPTIMIZATION COMPLETE!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green
Write-Host "Input file: $InputFile" -ForegroundColor White
Write-Host "Output file: $OutputFile" -ForegroundColor White
Write-Host "Commands processed: $processedCount" -ForegroundColor Cyan
Write-Host "File size: $([math]::Round((Get-Item $OutputFile).Length / 1MB, 2)) MB" -ForegroundColor Yellow

Write-Host "`nRAG Optimization Features Applied:" -ForegroundColor Yellow
Write-Host "✓ Flattened searchable content structure" -ForegroundColor Green
Write-Host "✓ Enhanced parameter descriptions with context" -ForegroundColor Green
Write-Host "✓ LLM-friendly example formatting with explanations" -ForegroundColor Green
Write-Host "✓ Search keywords and tags for better matching" -ForegroundColor Green
Write-Host "✓ Categorized commands by use cases and scenarios" -ForegroundColor Green
Write-Host "✓ Added best practices and common error guidance" -ForegroundColor Green
Write-Host "✓ Related commands mapping for recommendations" -ForegroundColor Green

Write-Host "`nThe optimized JSON is ready for RAG database conversion!" -ForegroundColor Green
