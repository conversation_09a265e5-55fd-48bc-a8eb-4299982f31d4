# Advanced RAG Document Optimizer for PowerShell Active Directory Commands
# Creates optimized structure with rag_document field for vector embedding and semantic search

param(
    [string]$InputFile = "ad_powershell_rag_optimized.json",
    [string]$OutputFile = "ad_powershell_advanced_rag.json",
    [switch]$Verbose
)

Write-Host "Advanced RAG Document Optimizer for PowerShell AD Commands" -ForegroundColor Green
Write-Host "==========================================================" -ForegroundColor Green

# Load the current RAG-optimized data
if (-not (Test-Path $InputFile)) {
    Write-Error "Input file not found: $InputFile"
    exit 1
}

Write-Host "Loading RAG-optimized data from: $InputFile" -ForegroundColor Yellow
try {
    $currentData = Get-Content $InputFile -Raw | ConvertFrom-Json
    Write-Host "Successfully loaded data with $($currentData.commands.Count) commands" -ForegroundColor Green
}
catch {
    Write-Error "Failed to parse JSON file: $($_.Exception.Message)"
    exit 1
}

# Initialize advanced RAG structure
$advancedRagData = @{
    metadata = @{
        created_at = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        source_file = $InputFile
        total_commands = 0
        optimization_version = "2.0-Advanced"
        description = "Advanced RAG-optimized PowerShell Active Directory command data with consolidated rag_document field for vector embedding"
        features = @(
            "Consolidated rag_document field for vector embedding",
            "Keywords array for hybrid search",
            "Common tasks mapping user intent to commands",
            "Semantic search optimized structure",
            "Maintained detailed structure for SLM context"
        )
    }
    commands = @()
}

# Helper function to generate keywords
function Generate-Keywords {
    param($Command)

    $keywords = @()

    # Add category-based keywords
    switch ($Command.category) {
        'User Management' { $keywords += @('user', 'account', 'person', 'employee', 'identity') }
        'Group Management' { $keywords += @('group', 'team', 'membership', 'security-group', 'distribution') }
        'Computer Management' { $keywords += @('computer', 'machine', 'workstation', 'server', 'device') }
        'Domain Management' { $keywords += @('domain', 'forest', 'directory', 'infrastructure') }
        'Policy Management' { $keywords += @('policy', 'security', 'rules', 'access', 'permissions') }
        'Replication Management' { $keywords += @('replication', 'site', 'topology', 'sync') }
        'Service Account Management' { $keywords += @('service-account', 'managed-account', 'automation') }
        'Organizational Unit Management' { $keywords += @('ou', 'organizational-unit', 'container', 'structure') }
        'Security Management' { $keywords += @('security', 'permissions', 'access', 'authentication') }
        default { $keywords += @('active-directory', 'management') }
    }

    # Add verb-based keywords
    switch ($Command.verb) {
        'Get' { $keywords += @('retrieve', 'query', 'search', 'find', 'list') }
        'New' { $keywords += @('create', 'add', 'provision', 'establish') }
        'Set' { $keywords += @('modify', 'update', 'change', 'configure', 'edit') }
        'Remove' { $keywords += @('delete', 'remove', 'cleanup', 'purge') }
        'Add' { $keywords += @('add', 'include', 'associate', 'attach') }
        'Enable' { $keywords += @('enable', 'activate', 'turn-on', 'start') }
        'Disable' { $keywords += @('disable', 'deactivate', 'turn-off', 'stop') }
    }

    # Add command-specific keywords
    $keywords += $Command.command_name.ToLower()
    $keywords += $Command.verb.ToLower()
    $keywords += $Command.noun.ToLower() -replace 'ad', ''

    # Add keywords from description
    if ($Command.description) {
        $descWords = $Command.description -split '\s+' | Where-Object {
            $_.Length -gt 3 -and
            $_ -notmatch '^(the|and|for|with|this|that|from|into|will|can|are|you|use|get|set|cmdlet)$'
        }
        $keywords += $descWords | ForEach-Object { $_.ToLower().Trim('.,!?;:') } | Select-Object -First 5
    }

    return ($keywords | Sort-Object -Unique)
}

# Helper function to generate common tasks
function Generate-CommonTasks {
    param($Command)

    $tasks = @()
    $verb = $Command.verb
    $noun = $Command.noun -replace '^AD', ''

    # Generate tasks based on verb and noun
    switch ($verb) {
        'Get' {
            $tasks += "Find a specific $noun"
            $tasks += "List all $noun objects"
            $tasks += "Search for $noun by criteria"
            $tasks += "Retrieve $noun properties"
            if ($Command.examples) {
                foreach ($example in $Command.examples) {
                    if ($example.scenario -eq 'List All') {
                        $tasks += "Get all $noun objects in a container"
                    }
                    elseif ($example.scenario -eq 'Search Filter') {
                        $tasks += "Filter $noun objects by specific attributes"
                    }
                    elseif ($example.scenario -eq 'Property Management') {
                        $tasks += "Retrieve additional $noun properties"
                    }
                }
            }
        }
        'New' {
            $tasks += "Create a new $noun"
            $tasks += "Provision $noun with specific properties"
            $tasks += "Add $noun to Active Directory"
        }
        'Set' {
            $tasks += "Modify $noun properties"
            $tasks += "Update $noun configuration"
            $tasks += "Change $noun attributes"
            $tasks += "Configure $noun settings"
        }
        'Remove' {
            $tasks += "Delete a $noun"
            $tasks += "Remove $noun from Active Directory"
            $tasks += "Clean up $noun objects"
        }
        'Add' {
            $tasks += "Add members to $noun"
            $tasks += "Associate objects with $noun"
            $tasks += "Include items in $noun"
        }
        'Enable' {
            $tasks += "Enable $noun functionality"
            $tasks += "Activate $noun"
            $tasks += "Turn on $noun features"
        }
        'Disable' {
            $tasks += "Disable $noun functionality"
            $tasks += "Deactivate $noun"
            $tasks += "Turn off $noun features"
        }
    }

    # Add specific tasks based on command name patterns
    if ($Command.command_name -match 'Password') {
        $tasks += "Manage password settings"
        $tasks += "Reset or change passwords"
    }
    if ($Command.command_name -match 'Member') {
        $tasks += "Manage group membership"
        $tasks += "Add or remove members"
    }
    if ($Command.command_name -match 'Replication') {
        $tasks += "Manage Active Directory replication"
        $tasks += "Configure replication topology"
    }

    return ($tasks | Select-Object -Unique)
}

# Helper function to create consolidated RAG document
function Create-RagDocument {
    param($Command, $CommonTasks)

    $ragDoc = @()

    # Command header
    $ragDoc += "Command: $($Command.command_name)"
    $ragDoc += "Purpose: $($Command.primary_purpose)"

    # Primary task
    $primaryTask = switch ($Command.verb) {
        'Get' { "Retrieve $($Command.noun -replace '^AD', '') information from Active Directory" }
        'New' { "Create a new $($Command.noun -replace '^AD', '') in Active Directory" }
        'Set' { "Modify $($Command.noun -replace '^AD', '') properties in Active Directory" }
        'Remove' { "Delete a $($Command.noun -replace '^AD', '') from Active Directory" }
        'Add' { "Add items to $($Command.noun -replace '^AD', '') in Active Directory" }
        'Enable' { "Enable $($Command.noun -replace '^AD', '') functionality" }
        'Disable' { "Disable $($Command.noun -replace '^AD', '') functionality" }
        default { $Command.primary_purpose }
    }
    $ragDoc += "Primary Task: $primaryTask"

    # Common tasks section
    if ($CommonTasks.Count -gt 0) {
        $ragDoc += "Common Tasks:"
        foreach ($task in $CommonTasks) {
            $ragDoc += "- $task"
        }
    }

    # Key parameters section
    if ($Command.parameters -and $Command.parameters.Count -gt 0) {
        $ragDoc += "Key Parameters:"

        # Focus on the most important parameters
        $keyParams = $Command.parameters | Where-Object {
            $_.mandatory -or
            $_.name -match '^(Identity|Filter|Name|Path|Properties|SearchBase)$'
        } | Select-Object -First 5

        foreach ($param in $keyParams) {
            $mandatoryText = if ($param.mandatory) { " (Required)" } else { "" }
            $ragDoc += "- $($param.name)$mandatoryText`: $($param.description -replace '\s+', ' ' | Select-Object -First 100)..."
        }
    }

    # Example scenarios section
    if ($Command.examples -and $Command.examples.Count -gt 0) {
        $ragDoc += "Example Scenarios:"
        foreach ($example in ($Command.examples | Select-Object -First 3)) {
            $cleanDescription = $example.description -replace '\s+', ' '
            if ($cleanDescription.Length -gt 100) {
                $cleanDescription = $cleanDescription.Substring(0, 100) + "..."
            }
            $ragDoc += "- $cleanDescription"
        }
    }

    # Category and related information
    $ragDoc += "Category: $($Command.category)"

    return ($ragDoc -join "`n")
}

Write-Host "Creating advanced RAG documents..." -ForegroundColor Yellow

$processedCount = 0
foreach ($command in $currentData.commands) {
    try {
        $processedCount++

        if ($Verbose) {
            Write-Host "  Processing: $($command.command_name)" -ForegroundColor Cyan
        }

        # Generate keywords array
        $keywords = Generate-Keywords -Command $command

        # Generate common tasks based on command purpose and examples
        $commonTasks = Generate-CommonTasks -Command $command

        # Create consolidated RAG document
        $ragDocument = Create-RagDocument -Command $command -CommonTasks $commonTasks
        
        # Create advanced RAG command structure
        $advancedCommand = @{
            command_name = $command.command_name
            verb = $command.verb
            noun = $command.noun
            module = $command.module
            category = $command.category
            primary_purpose = $command.primary_purpose
            description = $command.description
            
            # New advanced fields
            keywords = $keywords
            rag_document = $ragDocument
            
            # Maintained structure for SLM
            required_parameters = $command.required_parameters
            optional_parameters = $command.optional_parameters
            parameters = $command.parameters
            examples = $command.examples
            
            # Keep source information
            source_url = if ($command.source_url) { $command.source_url } else { "" }
            scraped_at = if ($command.scraped_at) { $command.scraped_at } else { "" }
        }
        
        $advancedRagData.commands += $advancedCommand
    }
    catch {
        Write-Warning "Error processing command $($command.command_name): $($_.Exception.Message)"
    }
}







# Finalize metadata
$advancedRagData.metadata.total_commands = $processedCount

Write-Host "Saving advanced RAG-optimized JSON..." -ForegroundColor Yellow

# Save the advanced RAG JSON
try {
    $json = $advancedRagData | ConvertTo-Json -Depth 20
    $json | Out-File -FilePath $OutputFile -Encoding UTF8
    
    # Display summary
    Write-Host "`n" + "="*70 -ForegroundColor Green
    Write-Host "ADVANCED RAG OPTIMIZATION COMPLETE!" -ForegroundColor Green
    Write-Host "="*70 -ForegroundColor Green
    Write-Host "Input file: $InputFile" -ForegroundColor White
    Write-Host "Output file: $OutputFile" -ForegroundColor White
    Write-Host "Commands processed: $processedCount" -ForegroundColor Cyan
    Write-Host "File size: $([math]::Round((Get-Item $OutputFile).Length / 1MB, 2)) MB" -ForegroundColor Yellow
    
    # Show sample statistics
    $totalKeywords = ($advancedRagData.commands | ForEach-Object { $_.keywords.Count } | Measure-Object -Sum).Sum
    $avgRagDocLength = ($advancedRagData.commands | ForEach-Object { $_.rag_document.Length } | Measure-Object -Average).Average
    
    Write-Host "`nAdvanced RAG Features:" -ForegroundColor Yellow
    Write-Host "  Total Keywords: $totalKeywords" -ForegroundColor Cyan
    Write-Host "  Average RAG Document Length: $([math]::Round($avgRagDocLength, 0)) characters" -ForegroundColor Cyan
    Write-Host "  Categories: $($advancedRagData.commands | Group-Object category | Measure-Object).Count" -ForegroundColor Cyan
    
    Write-Host "`nOptimization Features Applied:" -ForegroundColor Yellow
    Write-Host "✓ Consolidated rag_document field for vector embedding" -ForegroundColor Green
    Write-Host "✓ Keywords array for hybrid search systems" -ForegroundColor Green
    Write-Host "✓ Common tasks mapping user intent to commands" -ForegroundColor Green
    Write-Host "✓ Semantic search optimized structure" -ForegroundColor Green
    Write-Host "✓ Maintained detailed structure for SLM context" -ForegroundColor Green
    Write-Host "✓ Removed redundant searchable_content fields" -ForegroundColor Green
    
    Write-Host "`nThe advanced RAG data is ready for vector database ingestion!" -ForegroundColor Green
    Write-Host "Perfect for:" -ForegroundColor White
    Write-Host "  • Vector embedding and semantic search" -ForegroundColor Gray
    Write-Host "  • Hybrid search with keyword filtering" -ForegroundColor Gray
    Write-Host "  • User intent mapping to PowerShell commands" -ForegroundColor Gray
    Write-Host "  • Context injection for Phi-3.5 mini" -ForegroundColor Gray
}
catch {
    Write-Error "Failed to save advanced RAG JSON: $($_.Exception.Message)"
}
