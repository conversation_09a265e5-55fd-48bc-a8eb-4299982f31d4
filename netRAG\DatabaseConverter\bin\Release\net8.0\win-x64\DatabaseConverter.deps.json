{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"DatabaseConverter/1.0.0": {"dependencies": {"Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.SemanticKernel": "1.60.0", "Microsoft.SemanticKernel.Connectors.Onnx": "1.60.0-alpha", "NetRagService": "1.0.0", "System.Numerics.Tensors": "9.0.6"}, "runtime": {"DatabaseConverter.dll": {}}}, "Azure.AI.OpenAI/2.2.0-beta.4": {"dependencies": {"Azure.Core": "1.44.1", "OpenAI": "2.2.0-beta.4", "System.ClientModel": "1.4.0-beta.1"}, "runtime": {"lib/net8.0/Azure.AI.OpenAI.dll": {"assemblyVersion": "*******", "fileVersion": "2.200.25.16901"}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.4.0-beta.1", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Memory.Data": "8.0.1", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "********", "fileVersion": "1.4400.124.50905"}}}, "FastBertTokenizer/1.0.28": {"runtime": {"lib/net8.0/FastBertTokenizer.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.28.51251"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Bcl.HashCode/1.1.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.56604"}}}, "Microsoft.Extensions.AI/9.6.0": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Text.Json": "8.0.5", "System.Threading.Channels": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.AI.dll": {"assemblyVersion": "9.6.0.0", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.AI.Abstractions/9.6.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll": {"assemblyVersion": "9.6.0.0", "fileVersion": "9.600.25.31002"}}}, "Microsoft.Extensions.AI.OpenAI/9.5.0-preview.1.25265.7": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0", "OpenAI": "2.2.0-beta.4", "System.Memory.Data": "8.0.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/Microsoft.Extensions.AI.OpenAI.dll": {"assemblyVersion": "9.5.0.0", "fileVersion": "9.500.25.26507"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.1"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {}, "Microsoft.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3"}}, "Microsoft.Extensions.Hosting.WindowsServices/8.0.0": {"dependencies": {"Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "System.ServiceProcess.ServiceController": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.WindowsServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1325.6609"}}}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Logging.Console/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3"}}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.EventLog": "8.0.0"}}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Extensions.VectorData.Abstractions/9.7.0": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.0.0"}}}, "Microsoft.ML.OnnxRuntime/1.22.0": {"dependencies": {"Microsoft.ML.OnnxRuntime.Managed": "1.22.0"}, "native": {"runtimes/win-x64/native/onnxruntime.dll": {"fileVersion": "1.22.25.508"}, "runtimes/win-x64/native/onnxruntime.lib": {"fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onnxruntime_providers_shared.dll": {"fileVersion": "1.22.25.508"}, "runtimes/win-x64/native/onnxruntime_providers_shared.lib": {"fileVersion": "0.0.0.0"}}}, "Microsoft.ML.OnnxRuntime.Managed/1.22.0": {"dependencies": {"System.Memory": "4.5.5", "System.Numerics.Tensors": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.ML.OnnxRuntime.dll": {"assemblyVersion": "1.22.0.0", "fileVersion": "1.22.0.0"}}}, "Microsoft.ML.OnnxRuntimeGenAI/0.8.2": {"dependencies": {"Microsoft.ML.OnnxRuntime": "1.22.0", "Microsoft.ML.OnnxRuntimeGenAI.Managed": "0.8.2"}, "native": {"runtimes/win-x64/native/onnxruntime-genai.dll": {"fileVersion": "0.8.2.0"}, "runtimes/win-x64/native/onnxruntime-genai.lib": {"fileVersion": "0.0.0.0"}}}, "Microsoft.ML.OnnxRuntimeGenAI.Managed/0.8.2": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.6.0"}, "runtime": {"lib/net8.0/Microsoft.ML.OnnxRuntimeGenAI.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Microsoft.SemanticKernel/1.60.0": {"dependencies": {"Microsoft.SemanticKernel.Connectors.AzureOpenAI": "1.60.0", "Microsoft.SemanticKernel.Core": "1.60.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.dll": {"assemblyVersion": "1.60.0.0", "fileVersion": "1.60.0.0"}}}, "Microsoft.SemanticKernel.Abstractions/1.60.0": {"dependencies": {"Microsoft.Bcl.HashCode": "1.1.1", "Microsoft.Extensions.AI": "9.6.0", "Microsoft.Extensions.VectorData.Abstractions": "9.7.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"assemblyVersion": "1.60.0.0", "fileVersion": "1.60.0.0"}}}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.60.0": {"dependencies": {"Azure.AI.OpenAI": "2.2.0-beta.4", "Microsoft.SemanticKernel.Connectors.OpenAI": "1.60.0", "Microsoft.SemanticKernel.Core": "1.60.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll": {"assemblyVersion": "1.60.0.0", "fileVersion": "1.60.0.0"}}}, "Microsoft.SemanticKernel.Connectors.Onnx/1.60.0-alpha": {"dependencies": {"FastBertTokenizer": "1.0.28", "Microsoft.ML.OnnxRuntime": "1.22.0", "Microsoft.ML.OnnxRuntimeGenAI": "0.8.2", "Microsoft.SemanticKernel.Core": "1.60.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.Onnx.dll": {"assemblyVersion": "1.60.0.0", "fileVersion": "1.60.0.0"}}}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.60.0": {"dependencies": {"Microsoft.Extensions.AI.OpenAI": "9.5.0-preview.1.25265.7", "Microsoft.SemanticKernel.Core": "1.60.0", "OpenAI": "2.2.0-beta.4"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.OpenAI.dll": {"assemblyVersion": "1.60.0.0", "fileVersion": "1.60.0.0"}}}, "Microsoft.SemanticKernel.Core/1.60.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.SemanticKernel.Abstractions": "1.60.0", "System.Numerics.Tensors": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Core.dll": {"assemblyVersion": "1.60.0.0", "fileVersion": "1.60.0.0"}}}, "OpenAI/2.2.0-beta.4": {"dependencies": {"System.ClientModel": "1.4.0-beta.1", "System.Diagnostics.DiagnosticSource": "8.0.1"}, "runtime": {"lib/net8.0/OpenAI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.ClientModel/1.4.0-beta.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.3", "System.Memory.Data": "8.0.1"}, "runtime": {"lib/net8.0/System.ClientModel.dll": {"assemblyVersion": "1.4.0.0", "fileVersion": "1.400.25.15605"}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {}, "System.Diagnostics.EventLog/8.0.0": {}, "System.Memory/4.5.5": {}, "System.Memory.Data/8.0.1": {"runtime": {"lib/net8.0/System.Memory.Data.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.1024.46610"}}}, "System.Numerics.Tensors/9.0.6": {"runtime": {"lib/net8.0/System.Numerics.Tensors.dll": {"assemblyVersion": "9.0.0.6", "fileVersion": "9.0.625.26613"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.ServiceProcess.ServiceController/8.0.0": {"dependencies": {"System.Diagnostics.EventLog": "8.0.0"}, "runtime": {"runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/8.0.5": {}, "System.Threading.Channels/8.0.0": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "NetRagService/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Hosting.WindowsServices": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.SemanticKernel": "1.60.0", "Microsoft.SemanticKernel.Connectors.Onnx": "1.60.0-alpha", "System.Numerics.Tensors": "9.0.6"}, "runtime": {"NetRagService.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}}}, "libraries": {"DatabaseConverter/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.AI.OpenAI/2.2.0-beta.4": {"type": "package", "serviceable": true, "sha512": "sha512-qjCgspdq67x+urifvf7Dkz4tX5HVU3AlF2XUYU/kQBObKQihPsTYSQJ4tiMHEMNjaKRbfHzxnE2vnuhcqUUWCg==", "path": "azure.ai.openai/2.2.0-beta.4", "hashPath": "azure.ai.openai.2.2.0-beta.4.nupkg.sha512"}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "FastBertTokenizer/1.0.28": {"type": "package", "serviceable": true, "sha512": "sha512-7luZ+kHNzGesTIri8FfTEq6hLqPrMkd0Gq4KdFyytqDnqSueuQckxzqb7yYI6FOL9ucEwD7szMJAXDfcaywJ2A==", "path": "fastberttokenizer/1.0.28", "hashPath": "fastberttokenizer.1.0.28.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "path": "microsoft.bcl.hashcode/1.1.1", "hashPath": "microsoft.bcl.hashcode.1.1.1.nupkg.sha512"}, "Microsoft.Extensions.AI/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-JrMdI7lKN23axyQpWLF2B1Pgzxo3+oO/1XNC90rlInlkdHnhOwqZ9vHlcZu5gZLtQPQLf6MbnWwgInm+GVuEpA==", "path": "microsoft.extensions.ai/9.6.0", "hashPath": "microsoft.extensions.ai.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.AI.Abstractions/9.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-xGO7rHg3qK8jRdriAxIrsH4voNemCf8GVmgdcPXI5gpZ6lZWqOEM4ZO8yfYxUmg7+URw2AY1h7Uc/H17g7X1Kw==", "path": "microsoft.extensions.ai.abstractions/9.6.0", "hashPath": "microsoft.extensions.ai.abstractions.9.6.0.nupkg.sha512"}, "Microsoft.Extensions.AI.OpenAI/9.5.0-preview.1.25265.7": {"type": "package", "serviceable": true, "sha512": "sha512-htxD19JfZekY2vJSoMJn6lkBlZLcgMm7iK0MZc8pmuVT7FfNP7o+mTS4S0ZUDdBm1YR7NzZfYYjbwFOK/Z1gKg==", "path": "microsoft.extensions.ai.openai/9.5.0-preview.1.25265.7", "hashPath": "microsoft.extensions.ai.openai.9.5.0-preview.1.25265.7.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "path": "microsoft.extensions.dependencyinjection/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "path": "microsoft.extensions.hosting/8.0.0", "hashPath": "microsoft.extensions.hosting.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.WindowsServices/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-8JIvU6R9fejZj/p6QATeDaNEPtIvLuwE5Uh7qyPx7tp+Fc9FqYaAe6ylU2dM839IUpmsU4ZVev956slZyrYEhQ==", "path": "microsoft.extensions.hosting.windowsservices/8.0.0", "hashPath": "microsoft.extensions.hosting.windowsservices.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-dL0QGToTxggRLMYY4ZYX5AMwBb+byQBd/5dMiZE07Nv73o6I5Are3C7eQTh7K2+A4ct0PVISSr7TZANbiNb2yQ==", "path": "microsoft.extensions.logging.abstractions/8.0.3", "hashPath": "microsoft.extensions.logging.abstractions.8.0.3.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "path": "microsoft.extensions.logging.configuration/8.0.0", "hashPath": "microsoft.extensions.logging.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e+48o7DztoYog+PY430lPxrM4mm3PbA6qucvQtUDDwVo4MO+ejMw7YGc/o2rnxbxj4isPxdfKFzTxvXMwAz83A==", "path": "microsoft.extensions.logging.console/8.0.0", "hashPath": "microsoft.extensions.logging.console.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "path": "microsoft.extensions.logging.debug/8.0.0", "hashPath": "microsoft.extensions.logging.debug.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "path": "microsoft.extensions.logging.eventlog/8.0.0", "hashPath": "microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "path": "microsoft.extensions.logging.eventsource/8.0.0", "hashPath": "microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.VectorData.Abstractions/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vth/omSCX2vR0JabzSRU/hdPhr0CvUVZlaS2lJPWHrEwvak8ntrQLDtLMtMiWKSvviGBe/WmjUW8gA3qqn9tjw==", "path": "microsoft.extensions.vectordata.abstractions/9.7.0", "hashPath": "microsoft.extensions.vectordata.abstractions.9.7.0.nupkg.sha512"}, "Microsoft.ML.OnnxRuntime/1.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-IC5jOaU6YZ7qcLl7JiR2yL6+NznthdCPwW8XgN2XkbvDERRFqMLIVtwfzu2H110fhms59VIyyMOxHXHl2iVzCg==", "path": "microsoft.ml.onnxruntime/1.22.0", "hashPath": "microsoft.ml.onnxruntime.1.22.0.nupkg.sha512"}, "Microsoft.ML.OnnxRuntime.Managed/1.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-zlG3eY5mJnx1BhYAxRwpuHCGHzl3B+cY5/se0RmlVBw6Yh6QTGjPAXdjhlBIcw6BPFhgMn9lxWPE/U3Fvis+BQ==", "path": "microsoft.ml.onnxruntime.managed/1.22.0", "hashPath": "microsoft.ml.onnxruntime.managed.1.22.0.nupkg.sha512"}, "Microsoft.ML.OnnxRuntimeGenAI/0.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-DOFNGSmtGXt9w1T5710Rguf3vZt2oUaGi40teJK7D6QSByfs7c2xtqX2WmbMxWczIzI6ET1Qp66HvanvSk5zWQ==", "path": "microsoft.ml.onnxruntimegenai/0.8.2", "hashPath": "microsoft.ml.onnxruntimegenai.0.8.2.nupkg.sha512"}, "Microsoft.ML.OnnxRuntimeGenAI.Managed/0.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-G5HL1thndfJXanw9Q4IHrHWgyFWnpHUWYb5zQnc2ifnh4fgK/FNgDbkfq60UVbVMpwI/NQUZwk2w0yBxuEALkQ==", "path": "microsoft.ml.onnxruntimegenai.managed/0.8.2", "hashPath": "microsoft.ml.onnxruntimegenai.managed.0.8.2.nupkg.sha512"}, "Microsoft.SemanticKernel/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-zlioOIPIM7HdtIe/Dczg2rldGwqaSDpntf/z5wCiJWZLx9+ijgrODqakZYY5bHpyX9NlCAqSqr98UinncYEOMQ==", "path": "microsoft.semantickernel/1.60.0", "hashPath": "microsoft.semantickernel.1.60.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Abstractions/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-//jUQGgpWHf3Q9cNCsa259/j2FnSNQFrnW3fLgdEZ+aRC/C727j75GjwVlAhbsDd0K2+p+x3/bEM9jVHcNrKlw==", "path": "microsoft.semantickernel.abstractions/1.60.0", "hashPath": "microsoft.semantickernel.abstractions.1.60.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-eV9svk8LepzfN5UTgEfLYV3NReH+8oYBGlQBBextZ2w1VQnxDyV6eq3PPyuoyr0HZ7K5sUPARicXE7cQuQS1EA==", "path": "microsoft.semantickernel.connectors.azureopenai/1.60.0", "hashPath": "microsoft.semantickernel.connectors.azureopenai.1.60.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.Onnx/1.60.0-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-zpIxffK/2b578RqPsaAXgTsdJGZ6WoJQ7CVSNj03Qvf7ZMYI7S+k4+7oVrPp9jXf0tvD22tO2BzR2vc4RMI8zQ==", "path": "microsoft.semantickernel.connectors.onnx/1.60.0-alpha", "hashPath": "microsoft.semantickernel.connectors.onnx.1.60.0-alpha.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-JtHBVzKkFDBfpzMExBvJj0nAVbBdauWrUSHFAdsvC3MJGZzRxl+glcun4tfZXKBfDWt0VXgWpklgFLdV3FzrEQ==", "path": "microsoft.semantickernel.connectors.openai/1.60.0", "hashPath": "microsoft.semantickernel.connectors.openai.1.60.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Core/1.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-dE3JkvQNKYN29mTg4Fu6iwf1Ao51jrbReudqEoQhGePcsDBsjQh7YVs6PM4zwUtZzdMeKfkEF4n39HQEz86oXw==", "path": "microsoft.semantickernel.core/1.60.0", "hashPath": "microsoft.semantickernel.core.1.60.0.nupkg.sha512"}, "OpenAI/2.2.0-beta.4": {"type": "package", "serviceable": true, "sha512": "sha512-JZ4/mlVXLaXDIZuC4Ddu0KCAA23z4Ax1AQTS26mpJRuSShjXik7DU8a3basY3ddD51W04F7jeX5eAXamKA6rHw==", "path": "openai/2.2.0-beta.4", "hashPath": "openai.2.2.0-beta.4.nupkg.sha512"}, "System.ClientModel/1.4.0-beta.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZR0fKC94VS4P80vmxjk7l13/jPBXV0GMoE4jQfkYk8m2YV+dlw8jSC+b6eAfyBz0u+soN4CjhT3OdOC5KHaXxg==", "path": "system.clientmodel/1.4.0-beta.1", "hashPath": "system.clientmodel.1.4.0-beta.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "path": "system.diagnostics.diagnosticsource/8.0.1", "hashPath": "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "path": "system.diagnostics.eventlog/8.0.0", "hashPath": "system.diagnostics.eventlog.8.0.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B<PERSON><PERSON>uec3jV23EMRDeR7Dr1/qhx7369dZzJ9IWy2xylvb4YfXsrUxspWc4UWYid/tj4zZK58uGZqn2WQiaDMhmAg==", "path": "system.memory.data/8.0.1", "hashPath": "system.memory.data.8.0.1.nupkg.sha512"}, "System.Numerics.Tensors/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-NOLvZVal7jhuhmLFNuMQnCUclSAEvemJlwjyBxoa8CeK6Oj8326bM4AqB2dcH+8FGna3X3ZtP4PCLrIScyddtA==", "path": "system.numerics.tensors/9.0.6", "hashPath": "system.numerics.tensors.9.0.6.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.ServiceProcess.ServiceController/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jtYVG3bpw2n/NvNnP2g/JLri0D4UtfusTvLeH6cZPNAEjJXJVGspS3wLgVvjNbm+wjaYkFgsXejMTocV1T5DIQ==", "path": "system.serviceprocess.servicecontroller/8.0.0", "hashPath": "system.serviceprocess.servicecontroller.8.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Threading.Channels/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CMaFr7v+57RW7uZfZkPExsPB6ljwzhjACWW1gfU35Y56rk72B/Wu+sTqxVmGSk4SFUlPc3cjeKND0zktziyjBA==", "path": "system.threading.channels/8.0.0", "hashPath": "system.threading.channels.8.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "NetRagService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}