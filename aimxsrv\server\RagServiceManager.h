/*++

Copyright (c) Microsoft Corporation. All rights reserved.

Module Name:

    RagServiceManager.h

Abstract:

    Header file for the RAG Service Manager component that manages the lifecycle
    of the netrag.exe service and provides HTTP client functionality for tool
    registration and semantic search operations.

Author:

    <PERSON><PERSON><PERSON> (rizhang) 07/24/2025

--*/

#pragma once

#include <windows.h>
#include <string>
#include <memory>
#include <vector>
#include <mutex>
#include "AimxCommon.h"
#include "McpSvrMgr.h"
#include <nlohmann/json.hpp>

// RAG service configuration
struct RAG_SERVICE_CONFIG
{
    std::wstring serviceExecutablePath;
    std::wstring serviceArguments;
    std::wstring baseUrl;
    DWORD healthCheckTimeoutMs;
    DWORD startupTimeoutMs;
    DWORD shutdownTimeoutMs;
};

// RAG tool search result
struct RAG_TOOL_SEARCH_RESULT
{
    std::wstring toolId;
    std::wstring toolName;
    std::wstring description;
    double score;
};

// PowerShell command search result with full context
struct POWERSHELL_COMMAND_SEARCH_RESULT
{
    std::wstring commandName;
    std::wstring fullText;
    std::wstring parameterNames;
    std::wstring id;
    int parameterCount;
    int exampleCount;
    double score;
    nlohmann::json metadata;
};

// RAG Service Manager class
class RagServiceManager
{
public:
    // Initialize the RAG Service Manager
    static HRESULT Initialize();

    // Uninitialize and cleanup resources
    static void Uninitialize();

    // Start the RAG service process
    static HRESULT StartRagService();

    // Stop the RAG service process
    static HRESULT StopRagService();

    // Check if RAG service is healthy
    static HRESULT CheckServiceHealth();

    // Register a tool with the RAG service
    static HRESULT RegisterTool(
        _In_ const MCP_TOOL_INFO& toolInfo
        );

    // Search for tools using semantic similarity
    static HRESULT SearchTools(
        _In_ const std::wstring& query,
        _In_ int topK,
        _Out_ std::vector<RAG_TOOL_SEARCH_RESULT>& results
        );

    // Search for PowerShell commands with full context using semantic similarity
    static HRESULT SearchPowerShellCommands(
        _In_ const std::wstring& query,
        _In_ int topK,
        _Out_ std::vector<POWERSHELL_COMMAND_SEARCH_RESULT>& results
        );

    // Get service statistics
    static HRESULT GetServiceStatistics(
        _Out_ nlohmann::json& statistics
        );

private:
    // Private constructor for singleton pattern
    RagServiceManager();
    
    // Private destructor
    ~RagServiceManager();

    // Delete copy constructor and assignment operator
    RagServiceManager(const RagServiceManager&) = delete;
    RagServiceManager& operator=(const RagServiceManager&) = delete;

    // Internal initialization
    HRESULT InitializeInternal();

    // Internal HTTP client methods
    HRESULT SendHttpRequest(
        _In_ const std::wstring& endpoint,
        _In_ const web::http::method& method,
        _In_ const nlohmann::json& requestBody,
        _Out_ nlohmann::json& responseBody
        );

    HRESULT SendHttpGetRequest(
        _In_ const std::wstring& endpoint,
        _Out_ nlohmann::json& responseBody
        );

    // Process management helpers
    HRESULT StartServiceProcess();
    HRESULT WaitForServiceReady();
    HRESULT TerminateServiceProcess();

    // Internal data structures
    static RagServiceManager* s_instance;
    static std::mutex s_instanceMutex;
    
    RAG_SERVICE_CONFIG m_config;
    std::unique_ptr<web::http::client::http_client> m_httpClient;
    HANDLE m_serviceProcessHandle;
    DWORD m_serviceProcessId;
    bool m_initialized;
    bool m_serviceRunning;
    
    mutable std::mutex m_serviceMutex;
};
