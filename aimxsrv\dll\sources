TARGETNAME =                aimxsrv
TARGETTYPE =                <PERSON><PERSON><PERSON>INK
TARGET_DESTINATION =        retail
UMTYPE =                    windows

DEFFILE =                   aimxsrv.def
VALIDATE_DLLDEF_OBJECTS =   1

DLLENTRY = _DllMainCRTStartup

MSC_WARNING_LEVEL=/W4 /wd4244 

USE_MSVCRT=1
USE_UNICRT=1
USE_STL=1
STL_VER=STL_VER_CURRENT
USE_ATL=1
ATL_VER=ATL_VER_CURRENT

C_DEFINES=$(C_DEFINES) -DWIN32 -D_WIN32 -DUNICODE -D_UNICODE

# /Zo adds improved debugging of optimized code.
USER_C_FLAGS=$(USER_C_FLAGS) /Zo

#wd4244 is needed for httlib's conversion from int64_t to int32_t
MSC_WARNING_LEVEL= /W4 /wd4244 

PRECOMPILED_INCLUDE = pch.hxx
COMPILE_CXX = 1

# Enable exception handling unwind semantics(/EHsc)
USE_NATIVE_EH = 1 

DLOAD_ERROR_HANDLER = kernelbase.dll

SOURCES=\
    dllmain.cpp \
    aimxservice.cpp \

INCLUDES=\
    ..\..\common; \
    ..\..\common\nlohmann; \
    ..\..\McpProtocolLib; \
    ..\client; \
    ..\server; \
    $(OBJ_PATH); \
    $(OBJ_PATH)\$(O); \
    $(OBJ_PATH)\..\idl\$(O); \
    $(DS_INC_PATH); \
    $(BASE_INC_PATH); \
    $(SECURITY_INC); \
    $(ONECORE_EXTERNAL_SDK_INC_PATH_L); \
    $(ONECOREDS_INTERNAL_INC_PATH_L); \
    $(ONECOREDS_PRIVATE_INC_PATH_L)\security\base; \
    $(ONECOREBASE_PRIVATE_INC_PATH_L); \
    $(ONECOREBASE_INTERNAL_INC_PATH_L); \
    $(MINWIN_INTERNAL_PRIV_SDK_INC_PATH_L); \
    $(MINWIN_INTERNAL_PRIV_SDK_INC_PATH_L)\lsa; \
    $(MINWIN_INTERNAL_PRIV_SDK_INC_PATH_L)\apiset; \
    $(MINWIN_PRIVATE_PRIV_SDK_INC_PATH_L); \
    $(MINWIN_PRIVATE_PRIV_SDK_INC_PATH_L)\lsa; \
    $(MINCORE_INTERNAL_PRIV_SDK_INC_PATH_L); \
    $(PROJECT_ROOT)\ds\src\aimx\prod\aimxsrv\server; \
    $(PROJECT_ROOT)\ds\src\aimx\prod\aimxsrv\inc; \
    $(INTERNAL_SDK_INC_PATH); \
    $(ONECORE_INTERNAL_SDK_INC_PATH); \
    $(ONECORE_INTERNAL_PRIV_SDK_INC_PATH_L); \
    $(ONECORECOM_INTERNAL_INC_PATH_L); \

TARGETLIBS_PROJECT=\
    $(OBJ_PATH)\..\client\lib\$(O)\aimxclient_s.lib \
    $(OBJ_PATH)\..\server\$(O)\aimxserver.lib \
    $(OBJ_PATH)\..\..\McpProtocolLib\$(O)\McpProtocolLib.lib \
    $(OBJ_PATH)\..\..\MCPServerSample\lib\$(O)\HelloMcpServer.lib \
    $(OBJ_PATH)\..\..\AdMcpSvr\$(O)\AdMcpSvr.lib \

TARGETLIBS_ONECORE=\
    $(MINWIN_EXTERNAL_SDK_LIB_PATH_L)\ntdll.lib                 \
    $(MINWIN_EXTERNAL_SDK_LIB_PATH_L)\rpcrt4.lib                \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\secur32.lib                \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\ntdsapi.lib                \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\netapi32.lib               \
    $(ONECORE_EXTERNAL_SDK_LIB_PATH)\onecore.lib \
    $(MINCORE_INTERNAL_PRIV_SDK_LIB_VPATH_L)\ext-ms-win-shell32-shellfolders-l1.lib   \

TARGETLIBS_APISETS=\
    $(MINWIN_INTERNAL_SDK_LIB_VPATH_L)\api-ms-win-security-sddl-l1.lib                \
    $(MINWIN_INTERNAL_SDK_LIB_VPATH_L)\api-ms-win-security-base-l1.lib                \

TARGETLIBS=\
    $(TARGETLIBS_PROJECT) \
    $(TARGETLIBS_ONECORE) \
    $(TARGETLIBS_APISETS) \

DELAYLOAD=\
    api-ms-win-security-sddl-l1.dll;            \
    api-ms-win-security-base-l1.dll;            \
    ext-ms-win-shell32-shellfolders-l1.dll;     \
    rpcrt4.dll;                                 \
    secur32.dll;                                \
    ntdsapi.dll;                                \

RUN_WPP = \
    -ext:.cpp.h.hxx                                               \
    -preserveext:.cpp.h.hxx                                       \
    -scan:..\inc\wpp.h                                            \
    -DWPP_CHECK_INIT                                              \
    -p:AIMXSRV                                                    \
    -dll DllMain                                                  \
    -func:TraceCrit{LEVEL=TRACE_LEVEL_CRITICAL}(FLAGS,MSG,...)    \
    -func:TraceErr{LEVEL=TRACE_LEVEL_ERROR}(FLAGS,MSG,...)        \
    -func:TraceWarn{LEVEL=TRACE_LEVEL_WARNING}(FLAGS,MSG,...)     \
    -func:TraceInfo{LEVEL=TRACE_LEVEL_INFORMATION}(FLAGS,MSG,...) \
    -func:TraceVerb{LEVEL=TRACE_LEVEL_VERBOSE}(FLAGS,MSG,...)     \
    $(SOURCES)

# consume cppresetsdk
!include ..\..\cpprestsdk\consume.inc