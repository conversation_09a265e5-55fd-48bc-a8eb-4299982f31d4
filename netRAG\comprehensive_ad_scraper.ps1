# Comprehensive PowerShell Active Directory Documentation Scraper
# Based on successful single-page test - captures ALL information from Microsoft Learn

param(
    [string]$OutputFile = "comprehensive_ad_data.json",
    [string]$CommandListFile = "",
    [switch]$ScrapeAllCommands,
    [int]$DelayMs = 1000,
    [switch]$Verbose
)

# Simple HTML decoding function (since System.Web.HttpUtility may not be available)
function Decode-Html {
    param([string]$HtmlText)
    
    if (-not $HtmlText) { return "" }
    
    $decoded = $HtmlText
    $decoded = $decoded -replace '&amp;', '&'
    $decoded = $decoded -replace '&lt;', '<'
    $decoded = $decoded -replace '&gt;', '>'
    $decoded = $decoded -replace '&quot;', '"'
    $decoded = $decoded -replace '&#39;', "'"
    $decoded = $decoded -replace '&nbsp;', ' '
    
    return $decoded
}

# Function to get all AD commands from various sources
function Get-ADCommandList {
    $allCommands = @()
    
    if ($CommandListFile -and (Test-Path $CommandListFile)) {
        Write-Host "Loading commands from file: $CommandListFile"
        $allCommands = Get-Content $CommandListFile | Where-Object { $_ -and $_.Trim() }
    }
    elseif (Test-Path "..\ADPsMCPSvr\Tools\*.ps1") {
        Write-Host "Extracting commands from MCP server tools..."
        Get-ChildItem "..\ADPsMCPSvr\Tools\*.ps1" | ForEach-Object {
            $content = Get-Content $_.FullName -Raw
            $matches = [regex]::Matches($content, 'Register-McpTool -Name "([^"]+)"')
            $matches | ForEach-Object {
                $allCommands += $_.Groups[1].Value
            }
        }
    }
    elseif ($ScrapeAllCommands) {
        Write-Host "Using predefined list of all AD PowerShell commands..."
        # Comprehensive list of AD PowerShell commands
        $allCommands = @(
            "Add-ADCentralAccessPolicyMember", "Add-ADComputerServiceAccount", "Add-ADDomainControllerPasswordReplicationPolicy",
            "Add-ADFineGrainedPasswordPolicySubject", "Add-ADGroupMember", "Add-ADPrincipalGroupMembership",
            "Add-ADResourcePropertyListMember", "Clear-ADAccountExpiration", "Clear-ADClaimTransformLink",
            "Complete-ADServiceAccountMigration", "Disable-ADAccount", "Disable-ADOptionalFeature",
            "Enable-ADAccount", "Enable-ADOptionalFeature", "Get-ADAccountAuthorizationGroup",
            "Get-ADAccountResultantPasswordReplicationPolicy", "Get-ADAuthenticationPolicy", "Get-ADAuthenticationPolicySilo",
            "Get-ADCentralAccessPolicy", "Get-ADCentralAccessRule", "Get-ADClaimTransformPolicy",
            "Get-ADClaimType", "Get-ADComputer", "Get-ADComputerServiceAccount",
            "Get-ADDCCloningExcludedApplicationList", "Get-ADDefaultDomainPasswordPolicy", "Get-ADDomain",
            "Get-ADDomainController", "Get-ADDomainControllerPasswordReplicationPolicy", "Get-ADDomainControllerPasswordReplicationPolicyUsage",
            "Get-ADFineGrainedPasswordPolicy", "Get-ADFineGrainedPasswordPolicySubject", "Get-ADForest",
            "Get-ADGroup", "Get-ADGroupMember", "Get-ADObject", "Get-ADOptionalFeature",
            "Get-ADOrganizationalUnit", "Get-ADPrincipalGroupMembership", "Get-ADReplicationAttributeMetadata",
            "Get-ADReplicationConnection", "Get-ADReplicationFailure", "Get-ADReplicationPartnerMetadata",
            "Get-ADReplicationQueueOperation", "Get-ADReplicationSite", "Get-ADReplicationSiteLink",
            "Get-ADReplicationSiteLinkBridge", "Get-ADReplicationSubnet", "Get-ADReplicationUpToDatenessVectorTable",
            "Get-ADResourceProperty", "Get-ADResourcePropertyList", "Get-ADResourcePropertyValueType",
            "Get-ADRootDSE", "Get-ADServiceAccount", "Get-ADTrust", "Get-ADUser", "Get-ADUserResultantPasswordPolicy",
            "Grant-ADAuthenticationPolicySiloAccess", "Install-ADServiceAccount", "Move-ADDirectoryServer",
            "Move-ADDirectoryServerOperationMasterRole", "Move-ADObject", "New-ADAuthenticationPolicy",
            "New-ADAuthenticationPolicySilo", "New-ADCentralAccessPolicy", "New-ADCentralAccessRule",
            "New-ADClaimTransformPolicy", "New-ADClaimType", "New-ADComputer", "New-ADDCCloneConfigFile",
            "New-ADFineGrainedPasswordPolicy", "New-ADGroup", "New-ADObject", "New-ADOrganizationalUnit",
            "New-ADReplicationSite", "New-ADReplicationSiteLink", "New-ADReplicationSiteLinkBridge",
            "New-ADReplicationSubnet", "New-ADResourceProperty", "New-ADResourcePropertyList",
            "New-ADServiceAccount", "New-ADUser", "Remove-ADAuthenticationPolicy", "Remove-ADAuthenticationPolicySilo",
            "Remove-ADCentralAccessPolicy", "Remove-ADCentralAccessPolicyMember", "Remove-ADCentralAccessRule",
            "Remove-ADClaimTransformPolicy", "Remove-ADClaimType", "Remove-ADComputer", "Remove-ADComputerServiceAccount",
            "Remove-ADDomainControllerPasswordReplicationPolicy", "Remove-ADFineGrainedPasswordPolicy", "Remove-ADFineGrainedPasswordPolicySubject",
            "Remove-ADGroup", "Remove-ADGroupMember", "Remove-ADObject", "Remove-ADOrganizationalUnit",
            "Remove-ADPrincipalGroupMembership", "Remove-ADReplicationSite", "Remove-ADReplicationSiteLink",
            "Remove-ADReplicationSiteLinkBridge", "Remove-ADReplicationSubnet", "Remove-ADResourceProperty",
            "Remove-ADResourcePropertyList", "Remove-ADResourcePropertyListMember", "Remove-ADServiceAccount",
            "Remove-ADUser", "Rename-ADObject", "Reset-ADServiceAccountMigration", "Reset-ADServiceAccountPassword",
            "Restore-ADObject", "Revoke-ADAuthenticationPolicySiloAccess", "Search-ADAccount", "Set-ADAccountAuthenticationPolicySilo",
            "Set-ADAccountControl", "Set-ADAccountExpiration", "Set-ADAccountPassword", "Set-ADAuthenticationPolicy",
            "Set-ADAuthenticationPolicySilo", "Set-ADCentralAccessPolicy", "Set-ADCentralAccessRule",
            "Set-ADClaimTransformLink", "Set-ADClaimTransformPolicy", "Set-ADClaimType", "Set-ADComputer",
            "Set-ADDefaultDomainPasswordPolicy", "Set-ADDomain", "Set-ADDomainMode", "Set-ADFineGrainedPasswordPolicy",
            "Set-ADForest", "Set-ADForestMode", "Set-ADGroup", "Set-ADObject", "Set-ADOrganizationalUnit",
            "Set-ADReplicationConnection", "Set-ADReplicationSite", "Set-ADReplicationSiteLink",
            "Set-ADReplicationSiteLinkBridge", "Set-ADReplicationSubnet", "Set-ADResourceProperty",
            "Set-ADResourcePropertyList", "Set-ADServiceAccount", "Set-ADUser", "Show-ADAuthenticationPolicyExpression",
            "Start-ADServiceAccountMigration", "Sync-ADObject", "Test-ADServiceAccount", "Undo-ADServiceAccountMigration",
            "Uninstall-ADServiceAccount", "Unlock-ADAccount"
        )
    }
    else {
        Write-Error "No command source specified. Use -CommandListFile, ensure MCP server tools exist, or use -ScrapeAllCommands"
        return @()
    }
    
    return $allCommands | Sort-Object -Unique
}

# Microsoft Learn base URL for AD module
$baseUrl = "https://learn.microsoft.com/en-us/powershell/module/activedirectory"

# Comprehensive function to extract all data from Microsoft Learn pages
function Get-ComprehensiveMicrosoftLearnData {
    param([string]$CommandName)
    
    $url = "$baseUrl/$($CommandName.ToLower())"
    
    try {
        if ($Verbose) { Write-Host "Scraping: $CommandName from $url" }
        
        # Use Invoke-WebRequest to get the page content
        $response = Invoke-WebRequest -Uri $url -UseBasicParsing -TimeoutSec 30
        $content = $response.Content
        
        # Initialize comprehensive result structure
        $result = @{
            url = $url
            scraped_at = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            page_info = @{
                title = ""
                module = ""
                command_name = $CommandName
            }
            synopsis = ""
            description = ""
            syntax_blocks = @()
            parameters = @{}
            examples = @()
            inputs = @()
            outputs = @()
            notes = @()
            related_links = @()
            raw_sections = @{}
        }
        
        # Extract page title
        if ($content -match '<title>([^<]+)</title>') {
            $result.page_info.title = (Decode-Html $matches[1]).Trim()
        }
        
        # Extract module info
        if ($content -match 'Module:\s*<[^>]*>\s*<a[^>]*>([^<]+)</a>') {
            $result.page_info.module = $matches[1].Trim()
        }
        
        # Extract synopsis from meta description
        if ($content -match '<meta name="description" content="([^"]+)"') {
            $result.synopsis = Decode-Html $matches[1]
        }
        
        if ($Verbose) { Write-Host "Extracting main sections..." }
        
        # Extract all major sections by finding h2 headers
        $sectionMatches = [regex]::Matches($content, '(?s)<h2[^>]*>([^<]+)</h2>(.*?)(?=<h2[^>]*>|<div[^>]*class="[^"]*feedback|$)')
        
        foreach ($sectionMatch in $sectionMatches) {
            $sectionTitle = $sectionMatch.Groups[1].Value.Trim()
            $sectionContent = $sectionMatch.Groups[2].Value
            
            if ($Verbose) { Write-Host "  Processing section: $sectionTitle" }
            
            # Store raw section content
            $result.raw_sections[$sectionTitle] = $sectionContent
            
            switch ($sectionTitle.ToLower()) {
                "description" {
                    # Extract description paragraphs
                    $descMatches = [regex]::Matches($sectionContent, '<p>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</p>')
                    $descriptions = @()
                    foreach ($descMatch in $descMatches) {
                        $desc = (Decode-Html $descMatch.Groups[1].Value) -replace '<[^>]+>', '' -replace '\s+', ' '
                        $descriptions += $desc.Trim()
                    }
                    $result.description = $descriptions -join "`n`n"
                }
                
                "syntax" {
                    # Extract syntax blocks with their headers
                    $syntaxSections = [regex]::Matches($sectionContent, '(?s)<h3[^>]*>([^<]+)</h3>\s*<pre><code[^>]*>([^<]+)</code></pre>')
                    foreach ($syntaxSection in $syntaxSections) {
                        $syntaxName = $syntaxSection.Groups[1].Value.Trim()
                        $syntaxCode = Decode-Html $syntaxSection.Groups[2].Value.Trim()
                        
                        $result.syntax_blocks += @{
                            name = $syntaxName
                            code = $syntaxCode
                        }
                    }
                }
                
                "examples" {
                    # Extract examples with titles and descriptions
                    $exampleSections = [regex]::Matches($sectionContent, '(?s)<h3[^>]*>([^<]+)</h3>(.*?)(?=<h3[^>]*>|$)')
                    foreach ($exampleSection in $exampleSections) {
                        $exampleTitle = $exampleSection.Groups[1].Value.Trim()
                        $exampleContent = $exampleSection.Groups[2].Value
                        
                        # Extract code blocks
                        $codeMatches = [regex]::Matches($exampleContent, '<pre><code[^>]*>([^<]+)</code></pre>')
                        $codes = @()
                        foreach ($codeMatch in $codeMatches) {
                            $codes += Decode-Html $codeMatch.Groups[1].Value.Trim()
                        }
                        
                        # Extract description paragraphs
                        $descMatches = [regex]::Matches($exampleContent, '<p>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</p>')
                        $descriptions = @()
                        foreach ($descMatch in $descMatches) {
                            $desc = (Decode-Html $descMatch.Groups[1].Value) -replace '<[^>]+>', '' -replace '\s+', ' '
                            $descriptions += $desc.Trim()
                        }
                        
                        $result.examples += @{
                            title = $exampleTitle
                            description = $descriptions -join "`n"
                            code_blocks = $codes
                        }
                    }
                }
            }
        }
        
        if ($Verbose) { Write-Host "Extracting parameters..." }
        
        # Extract parameters section specifically
        $parametersSection = [regex]::Match($content, '(?s)<h2[^>]*>Parameters</h2>(.*?)(?=<h2[^>]*>|<div[^>]*class="[^"]*feedback|$)')
        if ($parametersSection.Success) {
            $paramContent = $parametersSection.Groups[1].Value
            
            # Find individual parameter sections
            $paramSections = [regex]::Matches($paramContent, '(?s)<h3[^>]*>([^<]+)</h3>(.*?)(?=<h3[^>]*>|$)')
            
            foreach ($paramSection in $paramSections) {
                $paramName = $paramSection.Groups[1].Value.Trim() -replace '^-', ''
                $paramDetails = $paramSection.Groups[2].Value
                
                if ($Verbose) { Write-Host "    Processing parameter: $paramName" }
                
                $paramInfo = @{
                    name = $paramName
                    description = ""
                    properties = @{}
                    raw_content = $paramDetails
                }
                
                # Extract parameter description
                $descMatch = [regex]::Match($paramDetails, '<p>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</p>')
                if ($descMatch.Success) {
                    $paramInfo.description = (Decode-Html $descMatch.Groups[1].Value) -replace '<[^>]+>', '' -replace '\s+', ' '
                }
                
                # Extract parameter properties from table
                $tableMatches = [regex]::Matches($paramDetails, '(?s)<table[^>]*>(.*?)</table>')
                foreach ($tableMatch in $tableMatches) {
                    $tableContent = $tableMatch.Groups[1].Value
                    $rowMatches = [regex]::Matches($tableContent, '(?s)<tr[^>]*><td[^>]*>([^<]+)</td><td[^>]*>([^<]+(?:<[^>]+>[^<]*</[^>]+>[^<]*)*)</td></tr>')
                    
                    foreach ($rowMatch in $rowMatches) {
                        $propName = $rowMatch.Groups[1].Value.Trim() -replace ':$', ''
                        $propValue = (Decode-Html $rowMatch.Groups[2].Value) -replace '<[^>]+>', '' -replace '\s+', ' '
                        $paramInfo.properties[$propName] = $propValue.Trim()
                    }
                }
                
                $result.parameters[$paramName] = $paramInfo
            }
        }
        
        return $result
    }
    catch {
        Write-Warning "Failed to scrape $CommandName`: $($_.Exception.Message)"
        return @{
            command = $CommandName
            url = $url
            status = "failed"
            error = $_.Exception.Message
            scraped_at = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        }
    }
}

# Main execution
Write-Host "Comprehensive PowerShell Active Directory Documentation Scraper" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green

# Get list of commands to scrape
$allCommands = Get-ADCommandList
if ($allCommands.Count -eq 0) {
    Write-Error "No commands found to scrape. Exiting."
    exit 1
}

Write-Host "Found $($allCommands.Count) unique commands to scrape" -ForegroundColor Yellow

# Scrape data for all commands
$scrapedData = @()
$successCount = 0
$failCount = 0
$startTime = Get-Date

Write-Host "`nStarting comprehensive scraping process..." -ForegroundColor Cyan

foreach ($command in $allCommands) {
    $progress = [math]::Round(($scrapedData.Count / $allCommands.Count) * 100, 1)
    Write-Progress -Activity "Scraping PowerShell Documentation" -Status "Processing $command" -PercentComplete $progress

    $data = Get-ComprehensiveMicrosoftLearnData -CommandName $command
    $scrapedData += $data

    if ($data.status -ne "failed") {
        $successCount++
        if ($Verbose) { Write-Host "✓ Successfully scraped: $command" -ForegroundColor Green }
    } else {
        $failCount++
        Write-Host "✗ Failed to scrape: $command - $($data.error)" -ForegroundColor Red
    }

    # Add delay to be respectful to Microsoft's servers
    Start-Sleep -Milliseconds $DelayMs
}

Write-Progress -Activity "Scraping PowerShell Documentation" -Completed

$endTime = Get-Date
$duration = $endTime - $startTime

# Create comprehensive result structure
$result = @{
    scraping_summary = @{
        total_commands = $allCommands.Count
        successful_scrapes = $successCount
        failed_scrapes = $failCount
        success_rate = [math]::Round(($successCount / $allCommands.Count) * 100, 2)
        scraping_date = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        duration_minutes = [math]::Round($duration.TotalMinutes, 2)
        source_url = $baseUrl
        scraper_version = "3.0-Comprehensive"
        delay_ms = $DelayMs
    }
    scraped_commands = $scrapedData
    command_index = @{}
}

# Create command index for faster lookups
foreach ($cmd in $scrapedData) {
    if ($cmd.status -ne "failed") {
        $result.command_index[$cmd.page_info.command_name] = @{
            synopsis = $cmd.synopsis
            parameter_count = $cmd.parameters.Count
            example_count = $cmd.examples.Count
            has_syntax = $cmd.syntax_blocks.Count -gt 0
            has_description = -not [string]::IsNullOrEmpty($cmd.description)
        }
    }
}

# Save results with better formatting
$json = $result | ConvertTo-Json -Depth 20
$json | Out-File -FilePath $OutputFile -Encoding UTF8

# Display comprehensive summary
Write-Host "`n" + "="*70 -ForegroundColor Green
Write-Host "COMPREHENSIVE SCRAPING COMPLETE!" -ForegroundColor Green
Write-Host "="*70 -ForegroundColor Green
Write-Host "Total Commands: $($allCommands.Count)" -ForegroundColor White
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red
Write-Host "Success Rate: $($result.scraping_summary.success_rate)%" -ForegroundColor Yellow
Write-Host "Duration: $($result.scraping_summary.duration_minutes) minutes" -ForegroundColor Cyan
Write-Host "Data saved to: $OutputFile" -ForegroundColor White
Write-Host "File size: $([math]::Round((Get-Item $OutputFile).Length / 1MB, 2)) MB" -ForegroundColor Cyan

# Show detailed statistics
$totalParams = ($scrapedData | Where-Object { $_.status -ne "failed" } | ForEach-Object { $_.parameters.Count } | Measure-Object -Sum).Sum
$totalExamples = ($scrapedData | Where-Object { $_.status -ne "failed" } | ForEach-Object { $_.examples.Count } | Measure-Object -Sum).Sum
$totalSyntaxBlocks = ($scrapedData | Where-Object { $_.status -ne "failed" } | ForEach-Object { $_.syntax_blocks.Count } | Measure-Object -Sum).Sum

Write-Host "`nDetailed Statistics:" -ForegroundColor Yellow
Write-Host "  Total Parameters Extracted: $totalParams" -ForegroundColor Cyan
Write-Host "  Total Examples Extracted: $totalExamples" -ForegroundColor Cyan
Write-Host "  Total Syntax Blocks Extracted: $totalSyntaxBlocks" -ForegroundColor Cyan

if ($failCount -gt 0) {
    Write-Host "`nFailed commands:" -ForegroundColor Red
    $scrapedData | Where-Object { $_.status -eq "failed" } | ForEach-Object {
        Write-Host "  - $($_.command): $($_.error)" -ForegroundColor Red
    }
}

Write-Host "`nScraping completed successfully! The comprehensive data is ready for use in your RAG system." -ForegroundColor Green
