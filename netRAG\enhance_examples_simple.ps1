#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Enhances high-frequency PowerShell commands with real-world community usage examples

.PARAMETER InputFile
    Path to the RAG-optimized JSON file with frequency data

.PARAMETER OutputFile
    Path where the enhanced JSON file will be saved
#>

param(
    [Parameter(Mandatory = $true)]
    [string]$InputFile,
    
    [Parameter(Mandatory = $true)]
    [string]$OutputFile
)

function Get-RealWorldExamples {
    param([string]$CommandName)
    
    switch ($CommandName) {
        'Get-ADUser' {
            return @(
                @{
                    scenario = "Find locked out users for helpdesk"
                    command = "Get-ADUser -Filter {LockedOut -eq `$true} -Properties LockedOut, LastLogonDate"
                    description = "Quickly identify all locked user accounts to assist with helpdesk tickets"
                },
                @{
                    scenario = "Find inactive users for cleanup"
                    command = "Get-ADUser -Filter * -Properties LastLogonDate | Where-Object {`$_.LastLogonDate -lt (Get-Date).AddDays(-90)}"
                    description = "Identify users who haven't logged in for 90+ days for account cleanup"
                },
                @{
                    scenario = "Bulk export user information"
                    command = "Get-ADUser -Filter * -Properties DisplayName, EmailAddress, Department | Export-CSV 'UserReport.csv'"
                    description = "Export all user data for reporting and analysis purposes"
                }
            )
        }
        'Set-ADUser' {
            return @(
                @{
                    scenario = "Employee department transfer"
                    command = "Set-ADUser -Identity 'jdoe' -Department 'IT' -Title 'System Administrator'"
                    description = "Update user attributes when employee changes departments or roles"
                },
                @{
                    scenario = "Set account expiration for contractors"
                    command = "Set-ADUser -Identity 'contractor1' -AccountExpirationDate (Get-Date).AddDays(90)"
                    description = "Set automatic account expiration for temporary employees and contractors"
                }
            )
        }
        'Get-ADGroup' {
            return @(
                @{
                    scenario = "Find security groups for access review"
                    command = "Get-ADGroup -Filter {GroupCategory -eq 'Security'} -Properties Members"
                    description = "List all security groups with members for access governance reviews"
                },
                @{
                    scenario = "Find empty groups for cleanup"
                    command = "Get-ADGroup -Filter * -Properties Members | Where-Object {`$_.Members.Count -eq 0}"
                    description = "Identify unused groups that can be safely removed"
                }
            )
        }
        'Get-ADComputer' {
            return @(
                @{
                    scenario = "Find computers not seen recently"
                    command = "Get-ADComputer -Filter * -Properties LastLogonDate | Where-Object {`$_.LastLogonDate -lt (Get-Date).AddDays(-30)}"
                    description = "Identify stale computer accounts for cleanup and security"
                },
                @{
                    scenario = "Audit server operating systems"
                    command = "Get-ADComputer -Filter {OperatingSystem -like '*Server*'} -Properties OperatingSystem"
                    description = "Inventory server operating systems for patch management"
                }
            )
        }
        'Add-ADGroupMember' {
            return @(
                @{
                    scenario = "Bulk add users to group from CSV"
                    command = "Import-CSV 'NewHires.csv' | ForEach-Object { Add-ADGroupMember -Identity 'All Employees' -Members `$_.SamAccountName }"
                    description = "Add multiple new employees to company-wide groups during onboarding"
                },
                @{
                    scenario = "Add user to multiple groups"
                    command = "@('VPN Users', 'Email Access') | ForEach-Object { Add-ADGroupMember -Identity `$_ -Members 'jdoe' }"
                    description = "Grant standard access by adding user to multiple required groups"
                }
            )
        }
        'Remove-ADGroupMember' {
            return @(
                @{
                    scenario = "Employee termination cleanup"
                    command = "Get-ADUser -Identity 'jdoe' -Properties MemberOf | ForEach-Object { `$_.MemberOf | ForEach-Object { Remove-ADGroupMember -Identity `$_ -Members 'jdoe' -Confirm:`$false } }"
                    description = "Remove terminated employee from all groups during offboarding process"
                }
            )
        }
        'New-ADUser' {
            return @(
                @{
                    scenario = "Standard new employee creation"
                    command = "New-ADUser -Name 'John Doe' -SamAccountName 'jdoe' -UserPrincipalName '<EMAIL>' -AccountPassword (ConvertTo-SecureString 'TempPass123!' -AsPlainText -Force) -Enabled `$true"
                    description = "Create new user account with standard attributes for employee onboarding"
                },
                @{
                    scenario = "Bulk user creation from CSV"
                    command = "Import-CSV 'NewEmployees.csv' | ForEach-Object { New-ADUser -Name `$_.FullName -SamAccountName `$_.Username -Enabled `$true }"
                    description = "Mass create user accounts from HR data for large onboarding batches"
                }
            )
        }
        'Get-ADDefaultDomainPasswordPolicy' {
            return @(
                @{
                    scenario = "Security compliance audit"
                    command = "Get-ADDefaultDomainPasswordPolicy | Select-Object ComplexityEnabled, MinPasswordLength, MaxPasswordAge"
                    description = "Review password policy settings for compliance with security standards"
                },
                @{
                    scenario = "Password policy documentation"
                    command = "Get-ADDefaultDomainPasswordPolicy | Format-List * | Out-File 'PasswordPolicyReport.txt'"
                    description = "Document current password policy settings for security documentation"
                }
            )
        }
        'Disable-ADAccount' {
            return @(
                @{
                    scenario = "Employee termination"
                    command = "Disable-ADAccount -Identity 'jdoe'; Set-ADUser -Identity 'jdoe' -Description 'Terminated `$(Get-Date -Format 'yyyy-MM-dd')'"
                    description = "Disable account and add termination date for audit trail"
                },
                @{
                    scenario = "Bulk disable inactive accounts"
                    command = "Get-ADUser -Filter * -Properties LastLogonDate | Where-Object {`$_.LastLogonDate -lt (Get-Date).AddDays(-180)} | ForEach-Object { Disable-ADAccount -Identity `$_.SamAccountName }"
                    description = "Disable accounts that haven't been used for 6 months"
                }
            )
        }
        default {
            return @()
        }
    }
}

function Enhance-HighFrequencyCommands {
    param(
        [Parameter(Mandatory = $true)]
        [string]$InputPath,
        
        [Parameter(Mandatory = $true)]
        [string]$OutputPath
    )
    
    Write-Host "Loading RAG-optimized data from: $InputPath" -ForegroundColor Green
    
    if (-not (Test-Path $InputPath)) {
        throw "Input file not found: $InputPath"
    }
    
    # Load the JSON data
    $jsonContent = Get-Content -Path $InputPath -Raw -Encoding UTF8
    $data = $jsonContent | ConvertFrom-Json
    
    if (-not $data.commands) {
        throw "Invalid JSON format: 'commands' array not found"
    }
    
    Write-Host "Processing $($data.commands.Count) commands for enhancement..." -ForegroundColor Yellow
    
    $enhancedCount = 0
    
    # Process each command
    foreach ($command in $data.commands) {
        $commandName = $command.command_name
        $frequency = $command.usage_frequency
        
        # Only enhance commands with frequency > 50
        if ($frequency -gt 50) {
            $examples = Get-RealWorldExamples -CommandName $commandName
            
            if ($examples.Count -gt 0) {
                # Add real-world examples to the command
                $command | Add-Member -NotePropertyName "real_world_examples" -NotePropertyValue $examples -Force
                
                # Enhance the RAG document with real-world context
                $originalRagDoc = $command.rag_document
                $realWorldContext = "`n`nReal-World Usage Scenarios:`n"
                
                foreach ($example in $examples) {
                    $realWorldContext += "- $($example.scenario): $($example.description)`n"
                    $realWorldContext += "  Command: $($example.command)`n"
                }
                
                $command.rag_document = $originalRagDoc + $realWorldContext
                
                $enhancedCount++
                Write-Host "  ✓ Enhanced $commandName (frequency: $frequency) with $($examples.Count) real-world examples" -ForegroundColor Cyan
            }
        }
    }
    
    # Update metadata
    if ($data.metadata) {
        $data.metadata | Add-Member -NotePropertyName "real_world_examples_added" -NotePropertyValue (Get-Date -Format "yyyy-MM-dd HH:mm:ss") -Force
        $data.metadata | Add-Member -NotePropertyName "enhanced_commands_count" -NotePropertyValue $enhancedCount -Force
        
        # Update features list
        if ($data.metadata.features) {
            $features = [System.Collections.ArrayList]$data.metadata.features
            if ($features -notcontains "real_world_examples") {
                $features.Add("real_world_examples") | Out-Null
                $data.metadata.features = $features.ToArray()
            }
        }
    }
    
    # Save the enhanced data
    Write-Host "Saving enhanced data to: $OutputPath" -ForegroundColor Green
    $enhancedJson = $data | ConvertTo-Json -Depth 25
    $enhancedJson | Out-File -FilePath $OutputPath -Encoding UTF8
    
    Write-Host "✅ Real-world examples added successfully!" -ForegroundColor Green
    Write-Host "   - Commands enhanced with real-world examples: $enhancedCount" -ForegroundColor White
    Write-Host "   - High-frequency commands (>50) now include practical usage scenarios" -ForegroundColor White
    Write-Host "   - Enhanced RAG documents with community-driven examples" -ForegroundColor White
}

# Main execution
try {
    Enhance-HighFrequencyCommands -InputPath $InputFile -OutputPath $OutputFile
} catch {
    Write-Error "Failed to enhance commands with real-world examples: $_"
    exit 1
}
