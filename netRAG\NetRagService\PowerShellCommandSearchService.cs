using Microsoft.Extensions.AI;
using Microsoft.Extensions.Logging;

namespace NetRagService;

public class PowerShellCommandSearchService
{
    private readonly IEmbeddingGenerator<string, Embedding<float>> _embeddingService;
    private readonly VectorStoreService _vectorStoreService;
    private readonly ILogger<PowerShellCommandSearchService> _logger;

    public PowerShellCommandSearchService(
        IEmbeddingGenerator<string, Embedding<float>> embeddingService,
        VectorStoreService vectorStoreService,
        ILogger<PowerShellCommandSearchService> logger)
    {
        _embeddingService = embeddingService;
        _vectorStoreService = vectorStoreService;
        _logger = logger;
    }

    public async Task<List<PowerShellCommandResult>> SearchAsync(string query, int limit = 5)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return new List<PowerShellCommandResult>();
        }

        try
        {
            _logger.LogInformation("Searching for PowerShell commands with query: '{Query}', limit: {Limit}", query, limit);

            // Generate embedding for the search query
            var queryEmbeddingResult = await _embeddingService.GenerateAsync(query);
            var queryEmbedding = queryEmbeddingResult.Vector;

            // Search the vector store
            var searchResults = await _vectorStoreService.SearchAsync(queryEmbedding, limit);

            // Convert to PowerShell command results - handle both old and new metadata formats
            var commandResults = searchResults.Select(result => new PowerShellCommandResult
            {
                // Try new format first, fallback to old format
                CommandName = GetMetadataValue(result.Metadata, "command_name") ??
                             GetMetadataValue(result.Metadata, "commandName") ?? "Unknown",
                Score = result.Score,
                FullText = GetMetadataValue(result.Metadata, "full_text") ??
                          GetMetadataValue(result.Metadata, "fullText") ?? "",
                ParameterCount = GetMetadataInt(result.Metadata, "parameter_count") ??
                                GetMetadataInt(result.Metadata, "parameterCount") ?? 0,
                ExampleCount = GetMetadataInt(result.Metadata, "example_count") ??
                              GetMetadataInt(result.Metadata, "exampleCount") ?? 0,
                ParameterNames = GetMetadataValue(result.Metadata, "parameter_names") ??
                                GetMetadataValue(result.Metadata, "parameterNames") ?? "",
                Id = result.Id,
                Metadata = result.Metadata
            }).ToList();

            _logger.LogInformation("Found {ResultCount} matching PowerShell commands", commandResults.Count);

            return commandResults;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to search PowerShell commands for query: {Query}", query);
            throw;
        }
    }

    public async Task<PowerShellCommandResult?> GetCommandByIdAsync(string commandId)
    {
        if (string.IsNullOrWhiteSpace(commandId))
        {
            return null;
        }

        try
        {
            // Search for the specific command by ID
            var allResults = await _vectorStoreService.SearchAsync(new float[768], 1000); // Get many results
            var targetResult = allResults.FirstOrDefault(r => r.Id == commandId);

            if (targetResult == null)
            {
                _logger.LogWarning("PowerShell command not found with ID: {CommandId}", commandId);
                return null;
            }

            return new PowerShellCommandResult
            {
                CommandName = targetResult.Metadata.TryGetValue("command_name", out var cmdName) ? cmdName.ToString() ?? "Unknown" : "Unknown",
                Score = targetResult.Score,
                FullText = targetResult.Metadata.TryGetValue("full_text", out var fullText) ? fullText.ToString() ?? "" : "",
                ParameterCount = targetResult.Metadata.TryGetValue("parameter_count", out var paramCount) && int.TryParse(paramCount.ToString(), out var pc) ? pc : 0,
                ExampleCount = targetResult.Metadata.TryGetValue("example_count", out var exampleCount) && int.TryParse(exampleCount.ToString(), out var ec) ? ec : 0,
                ParameterNames = targetResult.Metadata.TryGetValue("parameter_names", out var paramNames) ? paramNames.ToString() ?? "" : "",
                Id = targetResult.Id,
                Metadata = targetResult.Metadata
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get PowerShell command by ID: {CommandId}", commandId);
            throw;
        }
    }

    public async Task<List<string>> GetAllCommandNamesAsync()
    {
        try
        {
            // This is a workaround since we don't have a direct way to get all vectors
            // We'll search with a dummy query and high limit to get all commands
            var allResults = await _vectorStoreService.SearchAsync(new float[768], 1000);
            
            var commandNames = allResults
                .Where(r => r.Metadata.ContainsKey("command_name"))
                .Select(r => r.Metadata["command_name"].ToString() ?? "Unknown")
                .Where(name => name != "Unknown")
                .Distinct()
                .OrderBy(name => name)
                .ToList();

            _logger.LogInformation("Retrieved {CommandCount} unique PowerShell command names", commandNames.Count);

            return commandNames;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get all PowerShell command names");
            throw;
        }
    }

    // Helper methods to handle both old and new metadata formats
    private string? GetMetadataValue(Dictionary<string, object> metadata, string key)
    {
        return metadata.TryGetValue(key, out var value) ? value?.ToString() : null;
    }

    private int? GetMetadataInt(Dictionary<string, object> metadata, string key)
    {
        return metadata.TryGetValue(key, out var value) && int.TryParse(value?.ToString(), out var intValue)
            ? intValue : null;
    }

    public async Task<Dictionary<string, object>> GetSearchStatsAsync()
    {
        try
        {
            var vectorCount = await _vectorStoreService.GetVectorCountAsync();
            
            return new Dictionary<string, object>
            {
                ["total_vectors"] = vectorCount,
                ["data_type"] = "powershell_commands",
                ["last_updated"] = DateTime.UtcNow.ToString("O")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get search statistics");
            throw;
        }
    }

    /// <summary>
    /// Enhanced search that can leverage new RAG-optimized metadata
    /// </summary>
    public async Task<List<EnhancedPowerShellCommandResult>> SearchEnhancedAsync(
        string query,
        int limit = 5,
        string? category = null,
        string? verb = null,
        string? complexity = null)
    {
        if (string.IsNullOrWhiteSpace(query))
        {
            return new List<EnhancedPowerShellCommandResult>();
        }

        try
        {
            _logger.LogInformation("Enhanced search for PowerShell commands with query: '{Query}', limit: {Limit}", query, limit);

            // Generate embedding for the search query
            var queryEmbeddingResult = await _embeddingService.GenerateAsync(query);
            var queryEmbedding = queryEmbeddingResult.Vector;

            // Get more results initially for filtering
            var searchLimit = (category != null || verb != null || complexity != null) ? Math.Max(limit * 3, 50) : limit;
            var searchResults = await _vectorStoreService.SearchAsync(queryEmbedding, searchLimit);

            // Apply enhanced filters if specified
            if (!string.IsNullOrEmpty(category))
            {
                searchResults = searchResults.Where(r =>
                    GetMetadataValue(r.Metadata, "category")?.Contains(category, StringComparison.OrdinalIgnoreCase) == true).ToList();
            }

            if (!string.IsNullOrEmpty(verb))
            {
                searchResults = searchResults.Where(r =>
                    string.Equals(GetMetadataValue(r.Metadata, "verb"), verb, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            if (!string.IsNullOrEmpty(complexity))
            {
                searchResults = searchResults.Where(r =>
                    string.Equals(GetMetadataValue(r.Metadata, "complexity"), complexity, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            // Convert to enhanced command results
            var commandResults = searchResults.Take(limit).Select(result => new EnhancedPowerShellCommandResult
            {
                // Core information
                CommandName = GetMetadataValue(result.Metadata, "command_name") ??
                             GetMetadataValue(result.Metadata, "commandName") ?? "Unknown",
                Verb = GetMetadataValue(result.Metadata, "verb") ?? "",
                Noun = GetMetadataValue(result.Metadata, "noun") ?? "",
                Category = GetMetadataValue(result.Metadata, "category") ?? "",
                PrimaryPurpose = GetMetadataValue(result.Metadata, "primary_purpose") ?? "",
                Description = GetMetadataValue(result.Metadata, "description") ?? "",
                Score = result.Score,

                // RAG-specific fields
                RagDocument = GetMetadataValue(result.Metadata, "rag_document") ?? "",
                Keywords = DeserializeStringList(GetMetadataValue(result.Metadata, "keywords") ?? "[]"),

                // Parameter information
                RequiredParameters = DeserializeStringList(GetMetadataValue(result.Metadata, "required_parameters") ?? "[]"),
                OptionalParameters = DeserializeStringList(GetMetadataValue(result.Metadata, "optional_parameters") ?? "[]"),
                ParameterCount = GetMetadataInt(result.Metadata, "parameter_count") ??
                                GetMetadataInt(result.Metadata, "parameterCount") ?? 0,
                ParameterNames = GetMetadataValue(result.Metadata, "parameter_names") ??
                                GetMetadataValue(result.Metadata, "parameterNames") ?? "",

                // Example information
                ExampleCount = GetMetadataInt(result.Metadata, "example_count") ??
                              GetMetadataInt(result.Metadata, "exampleCount") ?? 0,

                // Search metadata
                Id = result.Id,
                FullText = GetMetadataValue(result.Metadata, "full_text") ??
                          GetMetadataValue(result.Metadata, "fullText") ?? "",
                Metadata = result.Metadata
            }).ToList();

            _logger.LogInformation("Found {ResultCount} matching PowerShell commands with enhanced search", commandResults.Count);

            return commandResults;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to perform enhanced search for query: {Query}", query);
            throw;
        }
    }

    private List<string> DeserializeStringList(string json)
    {
        try
        {
            return System.Text.Json.JsonSerializer.Deserialize<List<string>>(json) ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }
}

public class PowerShellCommandResult
{
    public string CommandName { get; set; } = string.Empty;
    public float Score { get; set; }
    public string FullText { get; set; } = string.Empty;
    public int ParameterCount { get; set; }
    public int ExampleCount { get; set; }
    public string ParameterNames { get; set; } = string.Empty;
    public string Id { get; set; } = string.Empty;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

// EnhancedPowerShellCommandResult is defined in EnhancedPowerShellCommandSearchService.cs
